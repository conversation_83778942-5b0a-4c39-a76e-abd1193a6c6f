# Dockerfile para CapRover - Next.js App
FROM node:18-alpine AS base

# Instalar dependencias solo cuando sea necesario
FROM base AS deps
# Verificar https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine
# Agregar dependencias para Sharp
RUN apk add --no-cache \
    libc6-compat \
    vips-dev \
    build-base \
    python3 \
    make \
    g++
WORKDIR /app

# Instalar dependencias basadas en el gestor de paquetes preferido
COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci --include=dev; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild el código fuente solo cuando sea necesario
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Variables de entorno necesarias para el build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# Variables de entorno mínimas para el build (CapRover las configurará en runtime)
ENV DATABASE_URL="*****************************************************/placeholder"
ENV NEXTAUTH_SECRET="build-time-secret-placeholder"
ENV NEXTAUTH_URL="http://localhost:3018"
ENV WEBHOOK_TOKEN="build-time-webhook-token-placeholder"


# Generar Prisma Client
RUN npx prisma generate

# Verificar que Tailwind CSS esté disponible
RUN npx tailwindcss --help | head -1

# Verificar que PostCSS esté disponible
RUN npx postcss --version

# Verificar configuraciones
RUN echo "=== PostCSS Config ===" && cat postcss.config.mjs
RUN echo "=== Tailwind Config ===" && head -10 tailwind.config.js
RUN echo "=== Globals CSS (first 10 lines) ===" && head -10 src/app/globals.css

# Construir la aplicación
RUN npm run build

# Verificar archivos CSS generados
RUN find .next/static -name "*.css" -exec echo "CSS file: {}" \; -exec head -3 {} \; -exec echo "---" \;

# Verificar que CSS se generó correctamente (sin @tailwind directives)
RUN if find .next/static -name "*.css" -exec grep -l "@tailwind" {} \; | grep -q .; then echo "ERROR: CSS contains unprocessed @tailwind directives" && find .next/static -name "*.css" -exec grep -l "@tailwind" {} \; && exit 1; else echo "SUCCESS: CSS compiled correctly"; fi

# Imagen de producción, copiar todos los archivos y ejecutar next
FROM base AS runner
WORKDIR /app

# Instalar dependencias de runtime para Sharp
RUN apk add --no-cache vips

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copiar archivos necesarios
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma

# Crear directorio de uploads con permisos correctos
RUN mkdir -p ./public/uploads/images && \
    chown -R nextjs:nodejs ./public/uploads

# Copiar script de inicio
COPY --from=builder /app/scripts/start-caprover.sh ./start.sh
RUN chmod +x ./start.sh

# Copiar archivos de build
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Verificar que los archivos CSS se copiaron correctamente
RUN ls -la ./.next/static/css/ || echo "Warning: No CSS files found after copy"
RUN find ./.next/static -name "*.css" -type f || echo "Warning: No CSS files found with find"

# Asegurar permisos correctos para archivos estáticos
RUN chmod -R 755 ./.next/static/ || echo "Warning: Could not set permissions"

# Copiar node_modules con Prisma Client
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

USER nextjs

EXPOSE 3018

ENV PORT 3018
ENV HOSTNAME "0.0.0.0"

# Comando de inicio con servidor standalone de Next.js
CMD ["node", "server.js"]
