// This is your Prisma schema file for PRODUCTION (PostgreSQL)
// Learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NOTA: Este archivo es para PRODUCCIÓN con PostgreSQL
// Para desarrollo local se usa schema.prisma con SQLite

// Modelo de Usuario
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String
  password  String
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  noticias Noticia[]
  correcciones Correccion[]
  notificaciones Notificacion[]
  boardMemberships BoardMember[]
  createdBoards Board[] @relation("BoardCreator")
  versionesGeneradas VersionNoticia[]
  generacionesImagen GeneracionImagen[]
  limitesUsoIA LimiteUsoIA[]
  systemConfigsCreated SystemConfig[] @relation("SystemConfigCreator")
  systemConfigsUpdated SystemConfig[] @relation("SystemConfigUpdater")
  auditLogs AuditLog[]

  // Índices de seguridad
  @@index([email])
  @@index([isActive])
  @@index([role])
  @@index([createdAt])
  @@map("users")
}

// Modelo de Categoría
model Categoria {
  id          Int      @id @default(autoincrement())
  nombre      String   @unique
  descripcion String?
  color       String   @default("#3B82F6")
  isActive    Boolean  @default(true)
  orden       Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  noticias Noticia[]
  categoriaMapeos CategoriaMapeo[]

  @@map("categorias")
}

// Modelo de Noticia
model Noticia {
  id          Int      @id @default(autoincrement())
  titulo      String
  subtitulo   String?
  volanta     String?
  contenido   String
  resumen     String?
  imagenUrl   String?
  imagenAlt   String?
  autor       String?
  fuente      String?
  urlFuente   String?
  estado      EstadoNoticia @default(BORRADOR)
  destacada   Boolean  @default(false)
  publicada   Boolean  @default(false)
  fechaPublicacion DateTime?

  // Campos para integración con WhatsApp
  periodista  String?  // Nombre del periodista que envía la noticia
  origen      OrigenNoticia @default(PANEL) // Origen de la noticia (PANEL o WEBHOOK)
  webhookData Json?    // Datos adicionales del webhook (metadatos)

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  categoriaId Int?
  categoria   Categoria? @relation(fields: [categoriaId], references: [id])
  userId      Int
  user        User @relation(fields: [userId], references: [id])
  versiones   VersionNoticia[]
  generacionesImagen GeneracionImagen[]
  notificaciones Notificacion[]
  publicacionesExternas PublicacionExterna[]

  // Índices de seguridad y rendimiento
  @@index([estado])
  @@index([publicada])
  @@index([userId])
  @@index([categoriaId])
  @@index([origen])
  @@index([createdAt])
  @@index([fechaPublicacion])
  @@map("noticias")
}

// Modelo de Notificación para sistema global
model Notificacion {
  id          Int      @id @default(autoincrement())
  tipo        TipoNotificacion @default(WEBHOOK_NOTICIA)
  titulo      String
  mensaje     String?
  leida       Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relación con la noticia (para notificaciones de webhook)
  noticiaId   Int?
  noticia     Noticia? @relation(fields: [noticiaId], references: [id], onDelete: Cascade)

  // Relación con el usuario que debe ver la notificación
  userId      Int
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Metadatos adicionales
  metadata    Json?

  @@map("notificaciones")
}

// Modelo de Corrección (mantenido para compatibilidad)
model Correccion {
  id                Int           @id @default(autoincrement())
  titulo            String
  contenido         String
  medio             String
  fechaPublicacion  DateTime
  fechaCorreccion   DateTime
  estado            Estado        @default(PENDIENTE)
  prioridad         Prioridad     @default(MEDIA)
  imagenUrl         String?
  observaciones     String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relaciones
  userId Int
  user   User @relation(fields: [userId], references: [id])

  @@map("correcciones")
}

// Modelo de Board/Equipo
model Board {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  createdById Int
  createdBy   User @relation("BoardCreator", fields: [createdById], references: [id])
  members     BoardMember[]

  @@map("boards")
}

// Modelo de Membresía de Board
model BoardMember {
  id        Int      @id @default(autoincrement())
  role      BoardRole @default(MEMBER)
  joinedAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  userId  Int
  user    User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  boardId Int
  board   Board @relation(fields: [boardId], references: [id], onDelete: Cascade)

  // Un usuario solo puede tener un rol por board
  @@unique([userId, boardId])
  @@map("board_members")
}

// Enum para roles de usuario
enum Role {
  ADMIN
  EDITOR
  USER
}

// Enum para estados de noticia
enum EstadoNoticia {
  BORRADOR
  EN_REVISION
  APROBADA
  PUBLICADA
  ARCHIVADA
}

// Enum para origen de noticia
enum OrigenNoticia {
  PANEL    // Creada desde el panel web
  WEBHOOK  // Recibida vía webhook desde WhatsApp
}

// Enum para tipos de notificación
enum TipoNotificacion {
  WEBHOOK_NOTICIA  // Nueva noticia recibida vía webhook
  SISTEMA         // Notificación del sistema
}

// Enum para estados de corrección
enum Estado {
  PENDIENTE
  EN_REVISION
  COMPLETADA
  RECHAZADA
}

// Enum para prioridades
enum Prioridad {
  BAJA
  MEDIA
  ALTA
  URGENTE
}

// Enum para roles de board
enum BoardRole {
  ADMIN
  MEMBER
}

// Modelo de Diario
model Diario {
  id          Int      @id @default(autoincrement())
  nombre      String   @unique
  descripcion String?
  prompt      String   @default("Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:")
  isActive    Boolean  @default(true)

  // Configuración de IA
  aiProvider  AIProvider @default(OPENAI)  // Proveedor de IA (OpenAI o Gemini)
  aiModel     String?    // Modelo específico (ej: gpt-4, gemini-pro)
  useGlobalConfig Boolean @default(true)   // Si usa configuración global o específica

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  versiones VersionNoticia[]

  @@map("diarios")
}

// Modelo de Versión de Noticia generada por IA
model VersionNoticia {
  id          Int      @id @default(autoincrement())
  titulo      String
  subtitulo   String?
  volanta     String?
  contenido   String
  resumen     String?
  imagenUrl   String?  // URL de la imagen para esta versión
  estado      EstadoVersion @default(GENERADA)
  estadoPublicacion EstadoPublicacionVersion? @default(PENDIENTE) // Estado de publicación externa
  urlPublicacion String? // URL de la publicación externa si fue publicada
  promptUsado String?
  metadatos   String?  // JSON con información adicional de la generación
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  noticiaId   Int
  noticia     Noticia @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  diarioId    Int
  diario      Diario @relation(fields: [diarioId], references: [id])
  generadaPor Int
  usuario     User @relation(fields: [generadaPor], references: [id])

  @@map("versiones_noticias")
}

// Modelo de Configuración Global de IA
model AIConfig {
  id                Int      @id @default(autoincrement())

  // Configuración OpenAI
  openaiApiKey      String?
  openaiModel       String   @default("gpt-3.5-turbo")
  openaiMaxTokens   Int      @default(2000)
  openaiTemperature Float    @default(0.7)

  // Configuración Google Gemini
  geminiApiKey      String?
  geminiModel       String   @default("gemini-pro")
  geminiMaxTokens   Int      @default(2000)
  geminiTemperature Float    @default(0.7)

  // Configuración global
  defaultProvider   AIProvider @default(OPENAI)
  isActive          Boolean    @default(true)

  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  // Índices de seguridad
  @@index([isActive])
  @@index([defaultProvider])
  @@map("ai_config")
}

// Enum para proveedores de IA
enum AIProvider {
  OPENAI
  GEMINI

  @@map("ai_provider")
}

// Enum para estados de versiones generadas
enum EstadoVersion {
  GENERADA
  APROBADA
  RECHAZADA
  EN_REVISION
}

// Enum para estados de publicación de versiones
enum EstadoPublicacionVersion {
  PENDIENTE
  PUBLICANDO
  PUBLICADA
  ERROR
}

// Modelo para configuración de IA
model ConfiguracionIA {
  id               Int      @id @default(autoincrement())
  nombre           String   @unique
  proveedor        String   @default("GOOGLE_GEMINI")
  modelo           String   @default("veo3")
  apiKey           String?
  endpoint         String?
  parametros       Json?
  promptPorDefecto String?
  limitesUso       Json?
  activo           Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relaciones
  generaciones GeneracionImagen[]
  limites      LimiteUsoIA[]

  // Índices de seguridad
  @@index([activo])
  @@index([proveedor])
  @@index([nombre])
  @@map("configuracion_ia")
}

// Modelo para generaciones de imágenes
model GeneracionImagen {
  id                 Int      @id @default(autoincrement())
  noticiaId          Int?
  usuarioId          Int
  configuracionIAId  Int
  prompt             String
  promptOriginal     String
  imagenUrl          String?
  estado             String   @default("PENDIENTE")
  metadatos          Json?
  tiempoGeneracion   Int?
  tokensUsados       Int?
  costo              Float?
  error              String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relaciones
  noticia        Noticia?        @relation(fields: [noticiaId], references: [id])
  usuario        User            @relation(fields: [usuarioId], references: [id])
  configuracion  ConfiguracionIA @relation(fields: [configuracionIAId], references: [id])

  @@index([noticiaId])
  @@index([usuarioId])
  @@index([estado])
  @@map("generacion_imagen")
}

// Modelo para límites de uso de IA
model LimiteUsoIA {
  id                Int      @id @default(autoincrement())
  usuarioId         Int
  configuracionIAId Int
  periodo           String   @default("MENSUAL")
  limite            Int
  usado             Int      @default(0)
  fechaReset        DateTime
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relaciones
  usuario       User            @relation(fields: [usuarioId], references: [id])
  configuracion ConfiguracionIA @relation(fields: [configuracionIAId], references: [id])

  @@unique([usuarioId, configuracionIAId, periodo])
  @@map("limite_uso_ia")
}

// Modelo de Configuración del Sistema
model SystemConfig {
  id        Int      @id @default(autoincrement())
  settings  Json     // Configuraciones del sistema en formato JSON
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy Int
  updatedBy Int

  // Relaciones
  creator User @relation("SystemConfigCreator", fields: [createdBy], references: [id])
  updater User @relation("SystemConfigUpdater", fields: [updatedBy], references: [id])

  @@map("system_configs")
}

// Modelo de Log de Auditoría
model AuditLog {
  id         Int      @id @default(autoincrement())
  action     String   // Acción realizada (CREATE, UPDATE, DELETE, etc.)
  entityType String   // Tipo de entidad (User, Noticia, etc.)
  entityId   String   // ID de la entidad afectada
  userId     Int      // Usuario que realizó la acción
  details    Json?    // Detalles adicionales de la acción
  createdAt  DateTime @default(now())

  // Relaciones
  user User @relation(fields: [userId], references: [id])

  // Índices de seguridad y auditoría
  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
  @@map("audit_logs")
}

// ========================================
// SISTEMA DE PUBLICACIÓN EXTERNA
// ========================================

// Modelo para Diarios Externos
model DiarioExterno {
  id                  Int      @id @default(autoincrement())
  nombre              String
  urlBase             String   @map("url_base")
  bearerToken         String   @map("bearer_token")
  categoriaImagenId   Int      @map("categoria_imagen_id")

  // Configuración de Endpoints
  endpointImagen      String   @default("/api/v1/image/insert") @map("endpoint_imagen")
  endpointCategoria   String   @default("/api/v1/categories/fetch") @map("endpoint_categoria")
  endpointArticulo    String   @default("/api/v1/article/insert") @map("endpoint_articulo")

  // Configuración adicional
  configuracion       String?  // JSON con configuraciones específicas
  descripcion         String?
  activo              Boolean  @default(true)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relaciones
  categoriaMapeos     CategoriaMapeo[]
  publicacionesExternas PublicacionExterna[]

  @@index([activo])
  @@index([createdAt])
  @@map("diarios_externos")
}

// Modelo para Mapeo de Categorías
model CategoriaMapeo {
  id                  Int      @id @default(autoincrement())
  diarioExternoId     Int      @map("diario_externo_id")
  categoriaLocalId    Int      @map("categoria_local_id")
  categoriaExternaId  Int      @map("categoria_externa_id")
  createdAt           DateTime @default(now()) @map("created_at")

  // Relaciones
  diarioExterno       DiarioExterno @relation(fields: [diarioExternoId], references: [id], onDelete: Cascade)
  categoriaLocal      Categoria     @relation(fields: [categoriaLocalId], references: [id], onDelete: Cascade)

  @@unique([diarioExternoId, categoriaLocalId])
  @@index([diarioExternoId])
  @@index([categoriaLocalId])
  @@map("categoria_mapeos")
}

// Modelo para Log de Publicaciones Externas
model PublicacionExterna {
  id                  Int      @id @default(autoincrement())
  noticiaId           Int      @map("noticia_id")
  diarioExternoId     Int      @map("diario_externo_id")
  imagenExternaId     Int?     @map("imagen_externa_id")
  articuloExternoId   Int?     @map("articulo_externo_id")
  urlPublicacion      String?  @map("url_publicacion")
  estado              EstadoPublicacion @default(PENDIENTE)
  errorMensaje        String?  @map("error_mensaje")
  metadatos           String?  // JSON con datos adicionales
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relaciones
  noticia             Noticia       @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  diarioExterno       DiarioExterno @relation(fields: [diarioExternoId], references: [id], onDelete: Cascade)

  @@index([noticiaId])
  @@index([diarioExternoId])
  @@index([estado])
  @@index([createdAt])
  @@map("publicaciones_externas")
}

// Enum para Estados de Publicación
enum EstadoPublicacion {
  PENDIENTE
  SUBIENDO_IMAGEN
  IMAGEN_SUBIDA
  PUBLICANDO_ARTICULO
  EXITOSO
  ERROR_IMAGEN
  ERROR_ARTICULO
  ERROR_CONEXION
}
