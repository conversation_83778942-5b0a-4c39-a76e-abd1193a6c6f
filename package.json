{"name": "panel-unificado-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3018", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint --max-warnings 0", "lint:check": "next lint --quiet", "db:seed": "tsx prisma/seed.ts", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^6.10.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/pg": "^8.15.5", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "express": "^5.1.0", "form-data": "^4.0.4", "lucide-react": "^0.523.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.10.1", "pg": "^8.16.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.3", "sonner": "^2.0.6", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.19.4", "@types/react": "^19.1.8", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "postcss-cli": "^11.0.0", "tailwindcss": "^3.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}