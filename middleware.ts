import { withAuth } from "next-auth/middleware"

export default withAuth(
  // `withAuth` augments your `Request` with the user's token.
  function middleware(req) {
    // Log para debugging en desarrollo
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔐 Middleware: ${req.method} ${req.nextUrl.pathname}`);
    }

    // Lógica adicional de middleware si es necesaria
    // Por ejemplo, verificar roles específicos para rutas admin
    if (req.nextUrl.pathname.startsWith('/admin')) {
      // Aquí podrías agregar verificación de roles de admin
      // Por ahora solo verificamos que haya token (manejado por withAuth)
    }
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Verificar que hay token
        if (!token) return false;

        // Para rutas admin, verificar rol de admin
        if (req.nextUrl.pathname.startsWith('/admin')) {
          return token.role === 'ADMIN';
        }

        // Para otras rutas protegidas, solo necesita estar autenticado
        return true;
      },
    },
  }
)

// Configuración de rutas protegidas
// Usamos expresión regular para incluir todas las rutas excepto las públicas
export const config = {
  matcher: [
    /*
     * Proteger todas las rutas excepto:
     * - api/webhook (webhooks públicos)
     * - api/auth (autenticación NextAuth)
     * - api/cron (tareas programadas)
     * - _next/static (archivos estáticos)
     * - _next/image (optimización de imágenes)
     * - favicon.ico, sitemap.xml, robots.txt (archivos públicos)
     * - auth/signin (página de login)
     * - uploads (archivos subidos por usuarios)
     */
    '/((?!api/webhook|api/auth|api/cron|auth/signin|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|uploads).*)'
  ]
}
