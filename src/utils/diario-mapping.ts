/**
 * Utilidades para mapear diarios internos con diarios externos
 * basándose en los nombres de los diarios
 */

interface DiarioInterno {
  id: number;
  nombre: string;
}

interface DiarioExterno {
  id: number;
  nombre: string;
}

/**
 * Mapeo de nombres de diarios internos a nombres de diarios externos
 * Esto permite relacionar qué diario externo corresponde a cada diario interno
 */
const DIARIO_MAPPING: Record<string, string> = {
  // Mapeo basado en nombres similares o equivalentes
  'Del sur': 'Del sur',
  'Telesol Diario': 'Telesol Diario',
  'El Zonda': 'El Zonda',
  // Mapeos adicionales con variaciones de nombres
  'Del Sur': 'Del sur',
  'TELESOL DIARIO': 'Telesol Diario',
  'EL ZONDA': 'El Zonda',
  'Telesol': 'Telesol Diario',
  'Zonda': 'El Zonda',
  // Agregar más mapeos según sea necesario
};

/**
 * Encuentra el diario externo correspondiente a un diario interno
 * basándose en el nombre del diario
 */
export function findCorrespondingExternalDiario(
  diarioInterno: DiarioInterno,
  diariosExternos: DiarioExterno[]
): DiarioExterno | null {
  // Buscar mapeo directo por nombre
  const nombreExterno = DIARIO_MAPPING[diarioInterno.nombre];
  if (nombreExterno) {
    const diarioExterno = diariosExternos.find(d => d.nombre === nombreExterno);
    if (diarioExterno) {
      return diarioExterno;
    }
  }

  // Buscar por nombre exacto si no hay mapeo específico
  const diarioExacto = diariosExternos.find(d => d.nombre === diarioInterno.nombre);
  if (diarioExacto) {
    return diarioExacto;
  }

  // Buscar por nombre similar (case-insensitive)
  const diarioSimilar = diariosExternos.find(d => 
    d.nombre.toLowerCase() === diarioInterno.nombre.toLowerCase()
  );
  if (diarioSimilar) {
    return diarioSimilar;
  }

  // Buscar por coincidencia parcial
  const diarioParcial = diariosExternos.find(d => 
    d.nombre.toLowerCase().includes(diarioInterno.nombre.toLowerCase()) ||
    diarioInterno.nombre.toLowerCase().includes(d.nombre.toLowerCase())
  );
  
  return diarioParcial || null;
}

/**
 * Filtra los diarios externos para mostrar solo el que corresponde
 * al diario interno que generó la versión
 */
export function filterExternalDiariosForVersion(
  versionDiario: DiarioInterno,
  diariosExternos: DiarioExterno[]
): DiarioExterno[] {
  console.log('🔍 filterExternalDiariosForVersion:', {
    versionDiario,
    diariosExternos: diariosExternos.map(d => ({ id: d.id, nombre: d.nombre }))
  });

  const correspondingDiario = findCorrespondingExternalDiario(versionDiario, diariosExternos);

  console.log('🎯 Resultado del mapeo:', {
    correspondingDiario: correspondingDiario ? { id: correspondingDiario.id, nombre: correspondingDiario.nombre } : null
  });

  return correspondingDiario ? [correspondingDiario] : [];
}

/**
 * Verifica si un diario externo corresponde a un diario interno
 */
export function isDiarioExternoForInterno(
  diarioInterno: DiarioInterno,
  diarioExterno: DiarioExterno
): boolean {
  const corresponding = findCorrespondingExternalDiario(diarioInterno, [diarioExterno]);
  return corresponding?.id === diarioExterno.id;
}
