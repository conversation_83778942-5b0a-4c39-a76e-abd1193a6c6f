import { useState, useEffect } from 'react';

interface PublicacionInfo {
  id: number;
  diarioExternoId: number;
  diarioNombre: string;
  urlPublicacion: string | null;
  fechaPublicacion: Date;
  versionId: number | null;
  tipoPublicacion: string;
}

interface PublicationStatusData {
  noticiaId: number;
  publicaciones: PublicacionInfo[];
}

interface UsePublicationStatusReturn {
  publicaciones: PublicacionInfo[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  isPublished: (diarioId: number, versionId?: number) => boolean;
  getPublicationInfo: (diarioId: number, versionId?: number) => PublicacionInfo | null;
}

export function usePublicationStatus(noticiaId: number): UsePublicationStatusReturn {
  const [publicaciones, setPublicaciones] = useState<PublicacionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPublicationStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/noticias/${noticiaId}/publication-status`);
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setPublicaciones(result.data.publicaciones || []);
      } else {
        throw new Error(result.error || 'Error desconocido');
      }
    } catch (err) {
      console.error('Error fetching publication status:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (noticiaId) {
      fetchPublicationStatus();
    }
  }, [noticiaId]);

  const isPublished = (diarioId: number, versionId?: number): boolean => {
    return publicaciones.some(pub => {
      const matchesDiario = pub.diarioExternoId === diarioId;
      
      if (versionId) {
        // Para versiones específicas, verificar que coincida la versión
        return matchesDiario && pub.versionId === versionId;
      } else {
        // Para noticia original, verificar que no tenga versionId o sea null
        return matchesDiario && (pub.versionId === null || pub.versionId === undefined);
      }
    });
  };

  const getPublicationInfo = (diarioId: number, versionId?: number): PublicacionInfo | null => {
    return publicaciones.find(pub => {
      const matchesDiario = pub.diarioExternoId === diarioId;
      
      if (versionId) {
        return matchesDiario && pub.versionId === versionId;
      } else {
        return matchesDiario && (pub.versionId === null || pub.versionId === undefined);
      }
    }) || null;
  };

  return {
    publicaciones,
    isLoading,
    error,
    refetch: fetchPublicationStatus,
    isPublished,
    getPublicationInfo
  };
}
