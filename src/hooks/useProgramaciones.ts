import { useState, useEffect } from 'react';

interface ProgramacionInfo {
  id: number;
  noticiaId: number;
  versionId?: number;
  diarioExternoId: number;
  tipo: 'NOTICIA' | 'VERSION';
  fechaPublicacion: string;
  descripcion?: string;
  estado: 'PENDIENTE' | 'EJECUTADA' | 'ERROR';
  noticia?: {
    titulo: string;
  };
  version?: {
    titulo: string;
    diario: {
      nombre: string;
    };
  };
  diarioExterno?: {
    nombre: string;
  };
}

interface UseProgramacionesReturn {
  programaciones: ProgramacionInfo[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasProgramaciones: boolean;
  getProgramacionesVersion: (versionId: number) => ProgramacionInfo[];
  getProgramacionesNoticia: () => ProgramacionInfo[];
}

export function useProgramaciones(noticiaId: number): UseProgramacionesReturn {
  const [programaciones, setProgramaciones] = useState<ProgramacionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProgramaciones = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/noticias/${noticiaId}/programaciones`);
      
      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setProgramaciones(data || []);
    } catch (err) {
      console.error('Error fetching programaciones:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (noticiaId) {
      fetchProgramaciones();
    }
  }, [noticiaId]);

  const getProgramacionesVersion = (versionId: number): ProgramacionInfo[] => {
    return programaciones.filter(prog => prog.versionId === versionId);
  };

  const getProgramacionesNoticia = (): ProgramacionInfo[] => {
    return programaciones.filter(prog => !prog.versionId);
  };

  return {
    programaciones,
    isLoading,
    error,
    refetch: fetchProgramaciones,
    hasProgramaciones: programaciones.length > 0,
    getProgramacionesVersion,
    getProgramacionesNoticia
  };
}
