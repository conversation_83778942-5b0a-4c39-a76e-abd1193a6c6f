'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Share2, 
  Facebook, 
  Twitter, 
  Instagram, 
  Loader2,
  Calendar,
  Send
} from 'lucide-react';
import { PublishDialog } from './PublishDialogSimple';
import { toast } from 'sonner';

interface PublicacionExterna {
  id: number;
  urlPublicacion: string;
  noticia: {
    titulo: string;
    contenido: string;
    imagenUrl?: string;
  };
}

interface PublishToSocialButtonProps {
  publicacion: PublicacionExterna;
  onPublishSuccess?: () => void;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
}

export function PublishToSocialButton({ 
  publicacion, 
  onPublishSuccess,
  className = '',
  variant = 'default',
  size = 'sm'
}: PublishToSocialButtonProps) {
  const [showDialog, setShowDialog] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  const handlePublishClick = () => {
    if (!publicacion.urlPublicacion) {
      toast.error('Esta noticia no tiene URL de publicación');
      return;
    }
    setShowDialog(true);
  };

  const handlePublishConfirm = async (params: {
    platforms: string[];
    accountIds: number[];
    publishType: 'immediate' | 'scheduled';
    scheduledFor?: Date;
    customCaption?: string;
  }) => {
    console.log('🚀 [PUBLISH] Iniciando publicación con parámetros:', params);
    console.log('🚀 [PUBLISH] Publicación:', publicacion);

    setIsPublishing(true);

    try {
      if (params.publishType === 'immediate') {
        // Publicación inmediata
        console.log('📤 [PUBLISH] Enviando request a /api/social-media/publish-news');
        const response = await fetch('/api/social-media/publish-news', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            publicacionExternaId: publicacion.id,
            platforms: params.platforms,
            accountIds: params.accountIds,
            customCaption: params.customCaption
          }),
        });

        console.log('📤 [PUBLISH] Response status:', response.status);
        const result = await response.json();
        console.log('📤 [PUBLISH] Response data:', result);

        if (result.success) {
          const { successful, failed, total } = result.summary;
          
          if (successful === total) {
            toast.success(`¡Publicado exitosamente en ${successful} cuenta${successful > 1 ? 's' : ''}!`);
          } else if (successful > 0) {
            toast.warning(`Publicado en ${successful}/${total} cuentas. ${failed} fallaron.`);
          } else {
            toast.error(`Error: No se pudo publicar en ninguna cuenta.`);
          }

          // Mostrar detalles de errores si los hay
          const errors = result.results.filter((r: any) => !r.success);
          errors.forEach((error: any) => {
            toast.error(`${error.platform}: ${error.error}`);
          });

          if (successful > 0 && onPublishSuccess) {
            onPublishSuccess();
          }
        } else {
          toast.error(result.error || 'Error al publicar');
        }
      } else {
        // Publicación programada con upload-post nativo
        const response = await fetch('/api/social-media/schedule-native', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            publicacionExternaId: publicacion.id,
            platforms: params.platforms,
            accountIds: params.accountIds,
            scheduledFor: params.scheduledFor?.toISOString(),
            customCaption: params.customCaption
          }),
        });

        const result = await response.json();

        if (result.success) {
          const { successful, failed, total } = result.summary;
          const scheduledDate = new Date(result.summary.scheduledFor).toLocaleString();
          
          if (successful === total) {
            toast.success(`¡Programado exitosamente para ${scheduledDate}!`);
          } else if (successful > 0) {
            toast.warning(`Programado ${successful}/${total} publicaciones. ${failed} fallaron.`);
          } else {
            toast.error(`Error: No se pudo programar ninguna publicación.`);
          }

          if (successful > 0 && onPublishSuccess) {
            onPublishSuccess();
          }
        } else {
          toast.error(result.error || 'Error al programar');
        }
      }
    } catch (error) {
      console.error('❌ [PUBLISH] Error en publicación:', error);
      console.error('❌ [PUBLISH] Detalles del error:', {
        message: error instanceof Error ? error.message : 'Error desconocido',
        stack: error instanceof Error ? error.stack : undefined
      });
      toast.error('Error de conexión al publicar - revisa la consola para detalles');
    } finally {
      setIsPublishing(false);
      setShowDialog(false);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="h-4 w-4" />;
      case 'twitter':
        return <Twitter className="h-4 w-4" />;
      case 'instagram':
        return <Instagram className="h-4 w-4" />;
      default:
        return <Share2 className="h-4 w-4" />;
    }
  };

  return (
    <>
      <Button
        onClick={handlePublishClick}
        disabled={isPublishing || !publicacion.urlPublicacion}
        variant={variant}
        size={size}
        className={`${className} ${!publicacion.urlPublicacion ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {isPublishing ? (
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
        ) : (
          <Share2 className="h-4 w-4 mr-2" />
        )}
        <span className="hidden sm:inline">
          {isPublishing ? 'Publicando...' : 'Publicar en Redes'}
        </span>
        <span className="sm:hidden">
          {isPublishing ? 'Publicando...' : 'Redes'}
        </span>
      </Button>

      <PublishDialog
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        publicacion={publicacion}
        onConfirm={handlePublishConfirm}
        isPublishing={isPublishing}
      />
    </>
  );
}
