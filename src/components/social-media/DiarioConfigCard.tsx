'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Settings, Check, X, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

interface DiarioProfile {
  diario: {
    id: number;
    nombre: string;
    descripcion: string | null;
    activo: boolean;
  };
  configuracion: {
    id: number;
    uploadPostProfile: string;
    facebookEnabled: boolean;
    twitterEnabled: boolean;
    instagramEnabled: boolean;
    linkedinEnabled: boolean;
    facebookPageId: string | null;
    configuradoEn: string;
    activo: boolean;
  } | null;
  estadoConexion: 'configurado' | 'pendiente';
}

interface UploadPostProfile {
  username: string;
  displayName: string;
  connectedPlatforms: string[];
  platformDetails: {
    facebook: { pageId: string; pageName: string } | null;
    twitter: { userId: string; username: string } | null;
    instagram: { userId: string; username: string } | null;
    linkedin: { pageId: string; pageName: string } | null;
  };
  isActive: boolean;
  lastSync: string;
}

interface DiarioConfigCardProps {
  diarioProfile: DiarioProfile;
  uploadPostProfiles: UploadPostProfile[];
  onConfigure: (
    diarioId: number,
    uploadPostProfile: string,
    platforms: { [key: string]: boolean },
    facebookPageId?: string
  ) => Promise<void>;
  isConfiguring: boolean;
}

export default function DiarioConfigCard({
  diarioProfile,
  uploadPostProfiles,
  onConfigure,
  isConfiguring
}: DiarioConfigCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(
    diarioProfile.configuracion?.uploadPostProfile || ''
  );
  const [platforms, setPlatforms] = useState({
    facebook: diarioProfile.configuracion?.facebookEnabled || false,
    twitter: diarioProfile.configuracion?.twitterEnabled || false,
    instagram: diarioProfile.configuracion?.instagramEnabled || false,
    linkedin: diarioProfile.configuracion?.linkedinEnabled || false
  });
  const [facebookPageId, setFacebookPageId] = useState(
    diarioProfile.configuracion?.facebookPageId || ''
  );

  const selectedUploadPostProfile = uploadPostProfiles.find(p => p.username === selectedProfile);

  const handleSave = async () => {
    if (!selectedProfile || selectedProfile === "no-profiles") {
      return;
    }

    await onConfigure(
      diarioProfile.diario.id,
      selectedProfile,
      platforms,
      facebookPageId || undefined
    );
    
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Restaurar valores originales
    setSelectedProfile(diarioProfile.configuracion?.uploadPostProfile || '');
    setPlatforms({
      facebook: diarioProfile.configuracion?.facebookEnabled || false,
      twitter: diarioProfile.configuracion?.twitterEnabled || false,
      instagram: diarioProfile.configuracion?.instagramEnabled || false,
      linkedin: diarioProfile.configuracion?.linkedinEnabled || false
    });
    setFacebookPageId(diarioProfile.configuracion?.facebookPageId || '');
    setIsEditing(false);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook': return <Facebook className="h-4 w-4" />;
      case 'twitter': return <Twitter className="h-4 w-4" />;
      case 'instagram': return <Instagram className="h-4 w-4" />;
      case 'linkedin': return <Linkedin className="h-4 w-4" />;
      default: return null;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'facebook': return 'bg-blue-100 text-blue-800';
      case 'twitter': return 'bg-sky-100 text-sky-800';
      case 'instagram': return 'bg-pink-100 text-pink-800';
      case 'linkedin': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div>
              <h3 className="font-semibold text-lg">{diarioProfile.diario.nombre}</h3>
              <p className="text-sm text-muted-foreground">
                {diarioProfile.estadoConexion === 'configurado' ? 'Configurado' : 'Pendiente de configuración'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {diarioProfile.estadoConexion === 'configurado' ? (
              <Badge className="bg-green-100 text-green-800">
                <Check className="h-3 w-3 mr-1" />
                Configurado
              </Badge>
            ) : (
              <Badge className="bg-yellow-100 text-yellow-800">
                <X className="h-3 w-3 mr-1" />
                Pendiente
              </Badge>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              disabled={isConfiguring}
            >
              <Settings className="h-4 w-4 mr-2" />
              {isEditing ? 'Cancelar' : 'Configurar'}
            </Button>
          </div>
        </div>

        {/* Configuración actual */}
        {!isEditing && diarioProfile.configuracion && (
          <div className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Perfil de Upload-Post:</Label>
              <p className="text-sm text-muted-foreground">{diarioProfile.configuracion.uploadPostProfile}</p>
            </div>
            
            <div>
              <Label className="text-sm font-medium">Plataformas habilitadas:</Label>
              <div className="flex flex-wrap gap-2 mt-1">
                {diarioProfile.configuracion.facebookEnabled && (
                  <Badge className={getPlatformColor('facebook')}>
                    {getPlatformIcon('facebook')}
                    <span className="ml-1">Facebook</span>
                  </Badge>
                )}
                {diarioProfile.configuracion.twitterEnabled && (
                  <Badge className={getPlatformColor('twitter')}>
                    {getPlatformIcon('twitter')}
                    <span className="ml-1">Twitter</span>
                  </Badge>
                )}
                {diarioProfile.configuracion.instagramEnabled && (
                  <Badge className={getPlatformColor('instagram')}>
                    {getPlatformIcon('instagram')}
                    <span className="ml-1">Instagram</span>
                  </Badge>
                )}
                {diarioProfile.configuracion.linkedinEnabled && (
                  <Badge className={getPlatformColor('linkedin')}>
                    {getPlatformIcon('linkedin')}
                    <span className="ml-1">LinkedIn</span>
                  </Badge>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Formulario de edición */}
        {isEditing && (
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">
                Perfil de Upload-Post:
                {uploadPostProfiles.length === 0 && (
                  <span className="ml-2 text-xs text-red-600 font-normal">
                    ⚠️ Primero carga los perfiles
                  </span>
                )}
              </Label>
              <Select
                value={selectedProfile}
                onValueChange={(value) => {
                  if (value !== "no-profiles") {
                    setSelectedProfile(value);
                  }
                }}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={
                    uploadPostProfiles.length === 0
                      ? "Primero carga los perfiles de upload-post"
                      : "Selecciona un perfil de upload-post"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {uploadPostProfiles.length === 0 ? (
                    <SelectItem value="no-profiles" disabled>
                      No hay perfiles cargados. Haz clic en "Cargar Perfiles Upload-Post"
                    </SelectItem>
                  ) : (
                    uploadPostProfiles.map((profile) => (
                      <SelectItem key={profile.username} value={profile.username}>
                        <div className="flex items-center gap-2">
                          <span>{profile.displayName}</span>
                          <div className="flex gap-1">
                            {profile.connectedPlatforms.map((platform) => (
                              <div key={platform} className="w-4 h-4">
                                {getPlatformIcon(platform)}
                              </div>
                            ))}
                          </div>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            {selectedUploadPostProfile && (
              <div>
                <Label className="text-sm font-medium">Plataformas disponibles:</Label>
                <div className="space-y-2 mt-2">
                  {selectedUploadPostProfile.connectedPlatforms.map((platform) => (
                    <div key={platform} className="flex items-center space-x-2">
                      <Checkbox
                        id={`${diarioProfile.diario.id}-${platform}`}
                        checked={platforms[platform as keyof typeof platforms]}
                        onCheckedChange={(checked) =>
                          setPlatforms(prev => ({ ...prev, [platform]: checked }))
                        }
                      />
                      <Label
                        htmlFor={`${diarioProfile.diario.id}-${platform}`}
                        className="flex items-center gap-2 text-sm"
                      >
                        {getPlatformIcon(platform)}
                        <span className="capitalize">{platform}</span>
                        {platform === 'facebook' && selectedUploadPostProfile.platformDetails.facebook && (
                          <span className="text-xs text-muted-foreground">
                            ({selectedUploadPostProfile.platformDetails.facebook.pageName})
                          </span>
                        )}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {platforms.facebook && selectedUploadPostProfile?.platformDetails.facebook && (
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <Facebook className="h-4 w-4 text-blue-600" />
                  <Label className="text-sm font-medium text-blue-800">Configuración de Facebook</Label>
                </div>
                <p className="text-xs text-blue-700 mb-2">
                  ✅ Se publicará en: <strong>{selectedUploadPostProfile.platformDetails.facebook.pageName}</strong>
                </p>
                <details className="text-xs">
                  <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                    ⚙️ Configuración avanzada (opcional)
                  </summary>
                  <div className="mt-2 space-y-2">
                    <Label className="text-xs text-gray-600">Facebook Page ID personalizado:</Label>
                    <Input
                      value={facebookPageId}
                      onChange={(e) => setFacebookPageId(e.target.value)}
                      placeholder="Dejar vacío para usar página por defecto"
                      className="text-xs"
      
                    />
                    <p className="text-xs text-gray-500">
                      💡 Solo necesario si quieres usar una página específica diferente
                    </p>
                  </div>
                </details>
              </div>
            )}

            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                disabled={!selectedProfile || selectedProfile === "no-profiles" || isConfiguring}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isConfiguring ? 'Guardando...' : 'Guardar Configuración'}
              </Button>
              <Button
                onClick={handleCancel}
                disabled={isConfiguring}
                variant="outline"
                size="sm"
              >
                Cancelar
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
