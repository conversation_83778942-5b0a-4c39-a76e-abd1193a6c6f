'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Loader2, 
  Calendar,
  Send,
  Wand2,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface PublicacionExterna {
  id: number;
  urlPublicacion: string;
  noticia: {
    titulo: string;
    contenido: string;
    imagenUrl?: string;
  };
}

interface SocialMediaAccount {
  id: number;
  platform: string;
  accountName: string;
  profileId: string;
  isActive: boolean;
}

interface PublishDialogProps {
  isOpen: boolean;
  onClose: () => void;
  publicacion: PublicacionExterna;
  onConfirm: (params: {
    platforms: string[];
    accountIds: number[];
    publishType: 'immediate' | 'scheduled';
    scheduledFor?: Date;
    customCaption?: string;
  }) => void;
  isPublishing: boolean;
}

export function PublishDialog({
  isOpen,
  onClose,
  publicacion,
  onConfirm,
  isPublishing
}: PublishDialogProps) {
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([]);
  const [selectedAccounts, setSelectedAccounts] = useState<number[]>([]);
  const [publishType, setPublishType] = useState<'immediate' | 'scheduled'>('immediate');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [customCaption, setCustomCaption] = useState('');
  const [isGeneratingCaption, setIsGeneratingCaption] = useState(false);
  const [loadingAccounts, setLoadingAccounts] = useState(true);

  // Cargar cuentas de redes sociales
  useEffect(() => {
    if (isOpen) {
      loadAccounts();
      resetForm();
    }
  }, [isOpen]);

  const loadAccounts = async () => {
    try {
      setLoadingAccounts(true);
      const response = await fetch('/api/social-media/accounts');
      const result = await response.json();
      
      if (result.success) {
        const activeAccounts = result.accounts.filter((acc: SocialMediaAccount) => acc.isActive);
        setAccounts(activeAccounts);
        
        if (activeAccounts.length === 0) {
          toast.warning('No hay cuentas de redes sociales configuradas');
        }
      } else {
        toast.error('Error cargando cuentas de redes sociales');
      }
    } catch (error) {
      console.error('Error cargando cuentas:', error);
      toast.error('Error de conexión al cargar cuentas');
    } finally {
      setLoadingAccounts(false);
    }
  };

  const resetForm = () => {
    setSelectedAccounts([]);
    setPublishType('immediate');
    setScheduledDate('');
    setScheduledTime('');
    setCustomCaption('');
  };

  const generateCaption = async () => {
    setIsGeneratingCaption(true);
    try {
      // Por ahora, generar un caption simple
      // TODO: Implementar generación con IA
      const caption = `📰 ${publicacion.noticia.titulo}\n\n¡Lee la noticia completa! 👆\n\n#Noticias #Actualidad`;
      setCustomCaption(caption);
      toast.success('Caption generado exitosamente');
    } catch (error) {
      toast.error('Error generando caption');
    } finally {
      setIsGeneratingCaption(false);
    }
  };

  const handleAccountToggle = (accountId: number, checked: boolean) => {
    if (checked) {
      setSelectedAccounts(prev => [...prev, accountId]);
    } else {
      setSelectedAccounts(prev => prev.filter(id => id !== accountId));
    }
  };

  const handleConfirm = () => {
    // Validaciones
    if (selectedAccounts.length === 0) {
      toast.error('Selecciona al menos una cuenta');
      return;
    }

    if (publishType === 'scheduled') {
      if (!scheduledDate || !scheduledTime) {
        toast.error('Selecciona fecha y hora para la programación');
        return;
      }

      const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);
      if (scheduledDateTime <= new Date()) {
        toast.error('La fecha programada debe ser futura');
        return;
      }
    }

    // Obtener plataformas seleccionadas
    const selectedPlatforms = accounts
      .filter(acc => selectedAccounts.includes(acc.id))
      .map(acc => acc.platform);

    const params = {
      platforms: [...new Set(selectedPlatforms)], // Eliminar duplicados
      accountIds: selectedAccounts,
      publishType,
      scheduledFor: publishType === 'scheduled' 
        ? new Date(`${scheduledDate}T${scheduledTime}`) 
        : undefined,
      customCaption: customCaption.trim() || undefined
    };

    onConfirm(params);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <Facebook className="h-4 w-4 text-blue-600" />;
      case 'twitter':
        return <Twitter className="h-4 w-4 text-sky-500" />;
      case 'instagram':
        return <Instagram className="h-4 w-4 text-pink-600" />;
      default:
        return null;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return 'border-blue-200 hover:border-blue-300';
      case 'twitter':
        return 'border-sky-200 hover:border-sky-300';
      case 'instagram':
        return 'border-pink-200 hover:border-pink-300';
      default:
        return 'border-gray-200 hover:border-gray-300';
    }
  };

  // Agrupar cuentas por plataforma
  const accountsByPlatform = accounts.reduce((acc, account) => {
    if (!acc[account.platform]) {
      acc[account.platform] = [];
    }
    acc[account.platform].push(account);
    return acc;
  }, {} as Record<string, SocialMediaAccount[]>);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Publicar en Redes Sociales
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Vista previa de la noticia */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 className="font-medium text-sm text-gray-600 dark:text-gray-400 mb-2">
              Noticia a publicar:
            </h3>
            <h4 className="font-semibold mb-2">{publicacion.noticia.titulo}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {publicacion.noticia.contenido.substring(0, 150)}...
            </p>
            <p className="text-xs text-blue-600 break-all">
              {publicacion.urlPublicacion}
            </p>
          </div>

          {/* Selección de cuentas */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Seleccionar Cuentas
            </Label>
            
            {loadingAccounts ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Cargando cuentas...</span>
              </div>
            ) : accounts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>No hay cuentas de redes sociales configuradas</p>
                <p className="text-sm">Ve a configuración para agregar cuentas</p>
              </div>
            ) : (
              <div className="space-y-4">
                {Object.entries(accountsByPlatform).map(([platform, platformAccounts]) => (
                  <div key={platform} className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {getPlatformIcon(platform)}
                      {platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </div>
                    <div className="grid grid-cols-1 gap-2 ml-6">
                      {platformAccounts.map((account) => (
                        <div
                          key={account.id}
                          className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${getPlatformColor(account.platform)}`}
                          onClick={() => handleAccountToggle(account.id, !selectedAccounts.includes(account.id))}
                        >
                          <input
                            type="checkbox"
                            checked={selectedAccounts.includes(account.id)}
                            onChange={(e) => handleAccountToggle(account.id, e.target.checked)}
                            className="h-4 w-4"
                          />
                          <div className="flex-1">
                            <div className="font-medium text-sm">{account.accountName}</div>
                            <div className="text-xs text-gray-500">ID: {account.profileId}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Tipo de publicación */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Tipo de Publicación
            </Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="immediate"
                  id="immediate"
                  checked={publishType === 'immediate'}
                  onChange={() => setPublishType('immediate')}
                  className="h-4 w-4"
                />
                <Label htmlFor="immediate" className="flex items-center gap-2 cursor-pointer">
                  <Send className="h-4 w-4" />
                  Publicar Ahora
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="scheduled"
                  id="scheduled"
                  checked={publishType === 'scheduled'}
                  onChange={() => setPublishType('scheduled')}
                  className="h-4 w-4"
                />
                <Label htmlFor="scheduled" className="flex items-center gap-2 cursor-pointer">
                  <Calendar className="h-4 w-4" />
                  Programar Publicación
                </Label>
              </div>
            </div>

            {/* Selector de fecha y hora */}
            {publishType === 'scheduled' && (
              <div className="mt-4 grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date" className="text-sm">Fecha</Label>
                  <Input
                    id="date"
                    type="date"
                    value={scheduledDate}
                    onChange={(e) => setScheduledDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                <div>
                  <Label htmlFor="time" className="text-sm">Hora</Label>
                  <Input
                    id="time"
                    type="time"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Caption personalizado */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-base font-medium">
                Caption Personalizado (Opcional)
              </Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={generateCaption}
                disabled={isGeneratingCaption}
              >
                {isGeneratingCaption ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Wand2 className="h-4 w-4 mr-2" />
                )}
                Generar
              </Button>
            </div>
            <Textarea
              placeholder="Escribe un caption personalizado o usa el botón 'Generar' para crear uno automáticamente..."
              value={customCaption}
              onChange={(e) => setCustomCaption(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              {customCaption.length}/500 caracteres
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isPublishing}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isPublishing || selectedAccounts.length === 0 || loadingAccounts}
          >
            {isPublishing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : publishType === 'immediate' ? (
              <Send className="h-4 w-4 mr-2" />
            ) : (
              <Calendar className="h-4 w-4 mr-2" />
            )}
            {isPublishing 
              ? 'Procesando...' 
              : publishType === 'immediate' 
                ? 'Publicar Ahora' 
                : 'Programar'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
