'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Loader2, 
  Calendar,
  Send,
  Wand2,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface PublicacionExterna {
  id: number;
  urlPublicacion: string;
  noticia: {
    titulo: string;
    contenido: string;
    imagenUrl?: string;
  };
}

interface PublishDialogProps {
  isOpen: boolean;
  onClose: () => void;
  publicacion: PublicacionExterna;
  onConfirm: (params: {
    platforms: string[];
    accountIds: number[];
    publishType: 'immediate' | 'scheduled';
    scheduledFor?: Date;
    customCaption?: string;
  }) => void;
  isPublishing: boolean;
}

export function PublishDialog({
  isOpen,
  onClose,
  publicacion,
  onConfirm,
  isPublishing
}: PublishDialogProps) {
  const [publishType, setPublishType] = useState<'immediate' | 'scheduled'>('immediate');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');

  // Función para establecer valores por defecto cuando se selecciona programación
  const handlePublishTypeChange = (type: 'immediate' | 'scheduled') => {
    setPublishType(type);

    if (type === 'scheduled' && !scheduledDate && !scheduledTime) {
      // Obtener fecha y hora actual en Argentina
      const nowInArgentina = new Date().toLocaleString('en-CA', {
        timeZone: 'America/Argentina/Buenos_Aires',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      const [argentinaDateStr, argentinaTimeStr] = nowInArgentina.split(', ');

      // Agregar 10 minutos a la hora argentina actual
      const [hours, minutes] = argentinaTimeStr.split(':').map(Number);
      const argentinaDateTime = new Date();
      argentinaDateTime.setHours(hours, minutes + 10, 0, 0);

      const futureTimeStr = argentinaDateTime.toTimeString().slice(0, 5);

      setScheduledDate(argentinaDateStr);
      setScheduledTime(futureTimeStr);

      console.log('📅 Valores por defecto establecidos (hora argentina):', {
        argentinaDate: argentinaDateStr,
        argentinaTime: argentinaTimeStr,
        futureTime: futureTimeStr,
        timezone: 'America/Argentina/Buenos_Aires'
      });
    }
  };
  const [customCaption, setCustomCaption] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [includeCallToAction, setIncludeCallToAction] = useState(false);

  const platforms = [
    { id: 'facebook', name: 'Facebook', icon: Facebook },
    { id: 'twitter', name: 'Twitter', icon: Twitter },
    { id: 'instagram', name: 'Instagram', icon: Instagram }
  ];

  // Generar caption automáticamente al abrir el diálogo
  const generateDefaultCaption = () => {
    // Validar que los datos existan para evitar "undefined"
    const titulo = publicacion.noticia?.titulo || 'Sin título';
    const url = publicacion.urlPublicacion || '';

    const baseCaption = `📰 ${titulo}`;
    const callToAction = includeCallToAction ? '\n\n¡Lee la noticia completa! 👆' : '';
    const urlSection = url ? `\n\n${url}` : '';
    const hashtags = '\n\n#Noticias #Actualidad #DiarioDelSur';

    return baseCaption + callToAction + urlSection + hashtags;
  };

  const generateCaption = () => {
    const caption = generateDefaultCaption();
    setCustomCaption(caption);
    toast.success('Caption generado exitosamente');
  };

  // Generar caption por defecto al abrir el diálogo
  React.useEffect(() => {
    if (isOpen && !customCaption.trim()) {
      const defaultCaption = generateDefaultCaption();
      setCustomCaption(defaultCaption);
    }
  }, [isOpen]);

  // Actualizar caption cuando cambie la opción de call-to-action
  React.useEffect(() => {
    if (isOpen && customCaption) {
      const newCaption = generateDefaultCaption();
      setCustomCaption(newCaption);
    }
  }, [includeCallToAction]);

  // Limpiar estado cuando se cierre el diálogo
  React.useEffect(() => {
    if (!isOpen) {
      setCustomCaption('');
      setIncludeCallToAction(false);
      setSelectedPlatforms([]);
      setPublishType('immediate');
      setScheduledDate('');
      setScheduledTime('');
    }
  }, [isOpen]);

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const handleConfirm = () => {
    if (selectedPlatforms.length === 0) {
      toast.error('Selecciona al menos una plataforma');
      return;
    }

    if (publishType === 'scheduled') {
      if (!scheduledDate || !scheduledTime) {
        toast.error('Selecciona fecha y hora para la programación');
        return;
      }

      // Construir fecha en hora local argentina para validación
      const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}:00`);
      const now = new Date();

      console.log('🕐 Validando programación (hora local argentina):', {
        scheduledDate,
        scheduledTime,
        scheduledDateTime: scheduledDateTime.toISOString(),
        scheduledLocal: scheduledDateTime.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
        now: now.toISOString(),
        nowLocal: now.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
        isFuture: scheduledDateTime > now,
        diffMinutes: Math.round((scheduledDateTime.getTime() - now.getTime()) / (1000 * 60))
      });

      // Validar que sea al menos 1 minuto en el futuro (en hora local)
      const minFutureTime = new Date(now.getTime() + 60000); // 1 minuto en el futuro
      if (scheduledDateTime <= minFutureTime) {
        toast.error('La fecha programada debe ser al menos 1 minuto en el futuro (hora argentina)');
        return;
      }

      // Validar que no sea más de 365 días en el futuro (límite de upload-post)
      const maxFutureTime = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000));
      if (scheduledDateTime > maxFutureTime) {
        toast.error('La fecha programada no puede ser más de 365 días en el futuro');
        return;
      }
    }

    // Construir fecha programada correctamente con conversión a UTC
    let scheduledForDate = undefined;
    if (publishType === 'scheduled') {
      // El usuario ingresa fecha y hora en Argentina, necesitamos convertir a UTC
      // Crear un objeto Date interpretando la entrada como hora argentina

      // Primero, crear la fecha como si fuera UTC (esto es lo que hace new Date() por defecto)
      const inputAsUTC = new Date(`${scheduledDate}T${scheduledTime}:00.000Z`);

      // Luego, ajustar por la diferencia de zona horaria
      // Argentina es UTC-3, así que cuando son las 22:31 en Argentina, son las 01:31 UTC del día siguiente
      const argentinaOffsetHours = -3; // UTC-3
      const argentinaOffsetMs = argentinaOffsetHours * 60 * 60 * 1000;

      // Para convertir de Argentina a UTC, restamos el offset (que es negativo, así que sumamos 3 horas)
      const utcDateTime = new Date(inputAsUTC.getTime() - argentinaOffsetMs);

      scheduledForDate = utcDateTime;

      console.log('📅 Conversión Argentina → UTC:', {
        userInput: `${scheduledDate} ${scheduledTime} (Argentina)`,
        inputAsUTC: inputAsUTC.toISOString(),
        argentinaOffset: `UTC${argentinaOffsetHours}`,
        offsetMs: argentinaOffsetMs,
        utcResult: utcDateTime.toISOString(),
        verification: {
          utcTime: utcDateTime.toLocaleString('es-ES', { timeZone: 'UTC' }),
          argentinaTime: utcDateTime.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
          shouldMatch: `${scheduledDate} ${scheduledTime}`,
          matches: utcDateTime.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }).includes(scheduledTime)
        }
      });
    }

    const params = {
      platforms: selectedPlatforms,
      accountIds: [1], // Mock account ID
      publishType,
      scheduledFor: scheduledForDate,
      customCaption: customCaption.trim() || undefined
    };

    onConfirm(params);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Publicar en Redes Sociales
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Vista previa de la noticia */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h3 className="font-medium text-sm text-gray-600 dark:text-gray-400 mb-2">
              Noticia a publicar:
            </h3>
            <h4 className="font-semibold mb-2">{publicacion.noticia.titulo}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {publicacion.noticia.contenido.substring(0, 150)}...
            </p>
            <p className="text-xs text-blue-600 break-all">
              {publicacion.urlPublicacion}
            </p>
          </div>

          {/* Selección de plataformas */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Seleccionar Plataformas
            </Label>
            <div className="grid grid-cols-1 gap-3">
              {platforms.map((platform) => {
                const IconComponent = platform.icon;
                return (
                  <div
                    key={platform.id}
                    className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                    onClick={() => handlePlatformToggle(platform.id)}
                  >
                    <input
                      type="checkbox"
                      checked={selectedPlatforms.includes(platform.id)}
                      onChange={() => handlePlatformToggle(platform.id)}
                      className="h-4 w-4"
                    />
                    <IconComponent className="h-5 w-5" />
                    <span className="font-medium">{platform.name}</span>
                    {platform.id === 'twitter' && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        ✅ Funcionando
                      </span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Tipo de publicación */}
          <div>
            <Label className="text-base font-medium mb-3 block">
              Tipo de Publicación
            </Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="immediate"
                  id="immediate"
                  checked={publishType === 'immediate'}
                  onChange={() => handlePublishTypeChange('immediate')}
                  className="h-4 w-4"
                />
                <Label htmlFor="immediate" className="flex items-center gap-2 cursor-pointer">
                  <Send className="h-4 w-4" />
                  Publicar Ahora
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="scheduled"
                  id="scheduled"
                  checked={publishType === 'scheduled'}
                  onChange={() => handlePublishTypeChange('scheduled')}
                  className="h-4 w-4"
                />
                <Label htmlFor="scheduled" className="flex items-center gap-2 cursor-pointer">
                  <Calendar className="h-4 w-4" />
                  Programar Publicación
                </Label>
              </div>
            </div>

            {/* Selector de fecha y hora */}
            {publishType === 'scheduled' && (
              <div className="mt-4 grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date" className="text-sm">Fecha</Label>
                  <Input
                    id="date"
                    type="date"
                    value={scheduledDate}
                    onChange={(e) => setScheduledDate(e.target.value)}
                    min={new Date().toLocaleDateString('en-CA', { timeZone: 'America/Argentina/Buenos_Aires' })}
                    placeholder="Selecciona fecha"
                  />
                </div>
                <div>
                  <Label htmlFor="time" className="text-sm">Hora</Label>
                  <Input
                    id="time"
                    type="time"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Caption personalizado */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-base font-medium">
                Caption de la Publicación
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="includeCallToAction"
                    checked={includeCallToAction}
                    onChange={(e) => setIncludeCallToAction(e.target.checked)}
                    className="h-4 w-4"
                  />
                  <Label htmlFor="includeCallToAction" className="text-sm">
                    Incluir "Lee la noticia completa"
                  </Label>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateCaption}
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  Regenerar
                </Button>
              </div>
            </div>
            <Textarea
              placeholder="El caption se genera automáticamente. Puedes editarlo aquí..."
              value={customCaption}
              onChange={(e) => setCustomCaption(e.target.value)}
              rows={6}
              className="resize-none"
            />
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-gray-500">
                {customCaption.length}/500 caracteres
              </p>
              <p className="text-xs text-blue-600">
                💡 Se incluye automáticamente: título, URL y hashtags
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isPublishing}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isPublishing || selectedPlatforms.length === 0}
          >
            {isPublishing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : publishType === 'immediate' ? (
              <Send className="h-4 w-4 mr-2" />
            ) : (
              <Calendar className="h-4 w-4 mr-2" />
            )}
            {isPublishing 
              ? 'Procesando...' 
              : publishType === 'immediate' 
                ? 'Publicar Ahora' 
                : 'Programar'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
