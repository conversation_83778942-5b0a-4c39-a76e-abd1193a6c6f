'use client';

import { useEffect, useRef } from 'react';

// Variable global para evitar múltiples instancias
let globalCronRunning = false;

export default function CronProvider({ children }: { children: React.ReactNode }) {
  const cronInitialized = useRef(false);

  useEffect(() => {
    // Solo ejecutar en el cliente después del montaje y una sola vez
    if (typeof window !== 'undefined' && !cronInitialized.current && !globalCronRunning) {
      cronInitialized.current = true;
      globalCronRunning = true;

      console.log('🚀 CRON: Iniciando sistema automático de programaciones...');

      let cronInterval: NodeJS.Timeout | null = null;
      let isExecuting = false;

      // Función para ejecutar programaciones pendientes
      const ejecutarProgramaciones = async () => {
        // Evitar ejecuciones concurrentes
        if (isExecuting) {
          console.log('⏳ CRON: Ejecución en progreso, saltando...');
          return;
        }

        isExecuting = true;

        try {
          // Obtener el secret desde el endpoint específico del cron
          const secretResponse = await fetch('/api/cron/secret');
          if (!secretResponse.ok) {
            console.error('❌ CRON: No se pudo obtener el secret');
            return;
          }

          const secretData = await secretResponse.json();
          const cronSecret = secretData.cronSecret;

          const response = await fetch('/api/cron/ejecutar-programaciones', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${cronSecret}`
            }
          });

          if (response.ok) {
            const result = await response.json();
            if (result.ejecutadas > 0) {
              console.log(`🎉 CRON: ${result.ejecutadas} programaciones ejecutadas`);
            } else {
              console.log('✅ CRON: Sin programaciones pendientes');
            }
          } else {
            console.error(`❌ CRON: Error ${response.status}: ${response.statusText}`);
          }
        } catch (error) {
          console.error('❌ CRON: Error ejecutando programaciones:', error);
        } finally {
          isExecuting = false;
        }
      };

      // Ejecutar cada 2 minutos (reducido para evitar spam)
      cronInterval = setInterval(ejecutarProgramaciones, 120000);

      // Ejecutar inmediatamente después de 15 segundos
      setTimeout(ejecutarProgramaciones, 15000);

      // Limpiar al desmontar
      return () => {
        console.log('🛑 CRON: Deteniendo sistema automático...');
        if (cronInterval) {
          clearInterval(cronInterval);
        }
        globalCronRunning = false;
        cronInitialized.current = false;
      };
    }
  }, []);

  return <>{children}</>;
}
