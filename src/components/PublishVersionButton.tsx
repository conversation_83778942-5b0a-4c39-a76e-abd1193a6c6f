'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ExternalLink, Upload, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { usePublicationStatus } from '@/hooks/usePublicationStatus';

interface PublishVersionButtonProps {
  noticiaId: number;
  versionId: number;
  diarioId: number;
  diarioNombre: string;
  estadoPublicacion?: 'PENDIENTE' | 'PUBLICANDO' | 'PUBLICADA' | 'ERROR';
  urlPublicacion?: string;
  onPublicationUpdate?: () => void;
}

export default function PublishVersionButton({
  noticiaId,
  versionId,
  diarioId,
  diarioNombre,
  estadoPublicacion = 'PENDIENTE',
  urlPublicacion,
  onPublicationUpdate
}: PublishVersionButtonProps) {
  const [isPublishing, setIsPublishing] = useState(false);
  const { isPublished, getPublicationInfo, refetch } = usePublicationStatus(noticiaId);

  // Verificar si ya está publicada esta versión específica
  const alreadyPublished = isPublished(diarioId, versionId);
  const publicationInfo = getPublicationInfo(diarioId, versionId);
  const [showRepublishOption, setShowRepublishOption] = useState(false);

  const handlePublish = async () => {
    // Si ya está publicado y tiene URL, abrir la publicación
    if (alreadyPublished && publicationInfo?.urlPublicacion) {
      window.open(publicationInfo.urlPublicacion, '_blank');
      return;
    }

    // Si ya está publicado pero no tiene URL, mostrar mensaje
    if (alreadyPublished) {
      toast.info('✅ Esta versión ya fue publicada exitosamente en este diario', {
        duration: 4000,
        description: 'La publicación ya está disponible en el sitio web'
      });
      return;
    }

    setIsPublishing(true);

    console.log('🔍 PublishVersionButton - Datos a enviar:', {
      noticiaId,
      versionId,
      diarioId,
      diarioNombre
    });

    try {
      const payload = {
        diarioExternoId: diarioId,
        versionId: versionId
      };

      console.log('📤 Enviando payload:', payload);

      const response = await fetch(`/api/noticias/${noticiaId}/publish-external`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('❌ Error parseando respuesta JSON:', jsonError);
        data = { error: 'Respuesta del servidor inválida' };
      }

      console.log('📥 Respuesta completa del servidor:', {
        status: response.status,
        statusText: response.statusText,
        data: data
      });

      if (response.ok) {
        console.log('✅ Publicación exitosa:', data);
        toast.success(`🎉 ¡Versión publicada exitosamente en ${diarioNombre}!`, {
          duration: 5000,
          description: data.data?.urlPublicacion ? `Ver publicación: ${data.data.urlPublicacion}` : 'La publicación está disponible en el sitio web'
        });
        onPublicationUpdate?.();
      } else if (response.status === 409) {
        // Noticia ya publicada
        console.log('⚠️ Noticia ya publicada:', data);

        if (data.data?.alreadyPublished) {
          toast.info(data.error || '✅ Esta versión ya fue publicada en este diario', {
            duration: 6000,
            description: data.data.urlPublicacion ?
              `Ya disponible en: ${data.data.urlPublicacion}` :
              'La publicación ya está disponible en el sitio web'
          });
        } else {
          toast.warning(data.error || 'Esta noticia ya fue publicada en este diario', {
            duration: 4000,
            description: 'El estado se actualizará automáticamente'
          });
        }
        refetch(); // Refrescar el estado de publicaciones
        onPublicationUpdate?.(); // Actualizar para reflejar el estado actual
      } else {
        const errorMessage = data?.error || data?.details || `Error HTTP ${response.status}: ${response.statusText}`;
        console.error('❌ Error en publicación:', {
          status: response.status,
          statusText: response.statusText,
          error: errorMessage,
          fullData: data
        });

        toast.error(errorMessage, {
          duration: 6000,
          description: data?.details ? 'Ver consola para más detalles' : 'Por favor, intenta nuevamente o contacta al administrador'
        });
      }
    } catch (error) {
      console.error('❌ Error de conexión al publicar:', {
        error: error,
        message: error instanceof Error ? error.message : 'Error desconocido',
        stack: error instanceof Error ? error.stack : undefined
      });

      const errorMessage = error instanceof Error ? error.message : 'Error de conexión desconocido';
      toast.error(`Error de conexión: ${errorMessage}`, {
        duration: 6000,
        description: 'Verifica tu conexión a internet e intenta nuevamente'
      });
    } finally {
      setIsPublishing(false);
    }
  };

  const getButtonContent = () => {
    if (isPublishing || estadoPublicacion === 'PUBLICANDO') {
      return (
        <>
          <Clock className="w-4 h-4 animate-spin" />
          Publicando en {diarioNombre}...
        </>
      );
    }

    // Si ya está publicada según nuestro hook
    if (alreadyPublished && publicationInfo) {
      return (
        <>
          <CheckCircle className="w-4 h-4 text-green-600" />
          ✅ Ya publicado en {diarioNombre}
        </>
      );
    }

    if (estadoPublicacion === 'PUBLICADA') {
      return (
        <>
          <CheckCircle className="w-4 h-4 text-green-600" />
          ✅ Publicado en {diarioNombre}
        </>
      );
    }

    if (estadoPublicacion === 'ERROR') {
      return (
        <>
          <AlertCircle className="w-4 h-4 text-red-600" />
          ❌ Error - Reintentar
        </>
      );
    }

    return (
      <>
        <ExternalLink className="w-4 h-4" />
        📤 Publicar en {diarioNombre}
      </>
    );
  };

  const getButtonVariant = () => {
    if (alreadyPublished && publicationInfo) return 'secondary';
    if (estadoPublicacion === 'PUBLICADA') return 'secondary';
    if (estadoPublicacion === 'ERROR') return 'destructive';
    return 'default';
  };

  const isDisabled = isPublishing || estadoPublicacion === 'PUBLICANDO' || (alreadyPublished && !publicationInfo?.urlPublicacion);

  return (
    <div className="flex items-center gap-2">
      <Button
        onClick={handlePublish}
        disabled={isDisabled}
        variant={getButtonVariant()}
        size="sm"
        className="flex items-center gap-2"
      >
        {getButtonContent()}
      </Button>
      
      {estadoPublicacion === 'PUBLICADA' && urlPublicacion && (
        <Button
          variant="outline"
          size="sm"
          asChild
          className="border-green-200 text-green-700 hover:bg-green-50"
        >
          <a
            href={urlPublicacion}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2"
          >
            <ExternalLink className="w-4 h-4" />
            🔗 Ver en {diarioNombre}
          </a>
        </Button>
      )}
    </div>
  );
}
