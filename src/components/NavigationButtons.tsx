'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { List, Globe, Shield, Users, Share2, Plus } from 'lucide-react';

export default function NavigationButtons() {
  const { data: session } = useSession();
  const router = useRouter();

  // Verificar si el usuario es admin
  const isAdmin = session?.user?.role === 'ADMIN';

  const navigateToNoticias = () => {
    router.push('/noticias');
  };

  const navigateToNoticiasNacionales = () => {
    router.push('/noticias-nacionales');
  };

  const navigateToNewNoticia = () => {
    router.push('/noticias/nueva');
  };

  const navigateToAdminUsuarios = () => {
    router.push('/admin/usuarios');
  };

  const navigateToAdmin = () => {
    router.push('/admin');
  };

  const navigateToRedesSociales = () => {
    router.push('/redes-sociales');
  };

  // Solo mostrar si hay sesión activa
  if (!session) {
    return null;
  }

  return (
    <div className="flex items-center space-x-1 sm:space-x-2 lg:space-x-3">
      {/* Ver Noticias */}
      <button
        onClick={navigateToNoticias}
        className="inline-flex items-center px-2 sm:px-3 py-2 border border-input text-xs sm:text-sm font-medium rounded-md text-foreground bg-background hover:bg-accent hover:text-accent-foreground transition-colors"
        title="Ver todas las noticias"
      >
        <List className="h-4 w-4 sm:mr-1 lg:mr-2" />
        <span className="hidden sm:inline lg:inline">Ver Noticias</span>
      </button>

      {/* Noticias Nacionales */}
      <button
        onClick={navigateToNoticiasNacionales}
        className="inline-flex items-center px-2 sm:px-3 py-2 border border-input text-xs sm:text-sm font-medium rounded-md text-foreground bg-background hover:bg-accent hover:text-accent-foreground transition-colors"
        title="Explorar noticias nacionales"
      >
        <Globe className="h-4 w-4 sm:mr-1 lg:mr-2" />
        <span className="hidden md:inline">Noticias Nacionales</span>
      </button>

      {/* Botones de Admin - Solo para administradores */}
      {isAdmin && (
        <>
          <button
            onClick={navigateToAdmin}
            className="inline-flex items-center px-2 sm:px-3 py-2 border border-input text-xs sm:text-sm font-medium rounded-md text-destructive-foreground bg-destructive hover:bg-destructive/90 transition-colors"
            title="Panel de administración"
          >
            <Shield className="h-4 w-4 sm:mr-1 lg:mr-2" />
            <span className="hidden lg:inline">Panel Admin</span>
          </button>
          <button
            onClick={navigateToAdminUsuarios}
            className="inline-flex items-center px-2 sm:px-3 py-2 border border-input text-xs sm:text-sm font-medium rounded-md text-foreground bg-background hover:bg-accent hover:text-accent-foreground transition-colors"
            title="Gestionar usuarios"
          >
            <Users className="h-4 w-4 sm:mr-1 lg:mr-2" />
            <span className="hidden lg:inline">Usuarios</span>
          </button>
        </>
      )}

      {/* Redes Sociales */}
      <button
        onClick={navigateToRedesSociales}
        className="inline-flex items-center px-2 sm:px-3 py-2 border border-input text-xs sm:text-sm font-medium rounded-md text-foreground bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"
        title="Gestionar redes sociales"
      >
        <Share2 className="h-4 w-4 sm:mr-1 lg:mr-2 text-blue-600" />
        <span className="hidden lg:inline">Redes Sociales</span>
      </button>

      {/* Nueva Noticia - Botón principal */}
      <button
        onClick={navigateToNewNoticia}
        className="inline-flex items-center px-2 sm:px-3 py-2 border border-transparent text-xs sm:text-sm font-medium rounded-md shadow-sm text-primary-foreground bg-primary hover:bg-primary/90 transition-colors"
        title="Crear nueva noticia"
      >
        <Plus className="h-4 w-4 sm:mr-1 lg:mr-2" />
        <span className="hidden sm:inline">Nueva Noticia</span>
      </button>
    </div>
  );
}
