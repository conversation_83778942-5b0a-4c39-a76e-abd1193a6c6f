'use client';

import { useState, useEffect } from 'react';
import { 
  Activity, 
  Database, 
  Cpu, 
  HardDrive, 
  Users, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

interface SystemHealth {
  database: {
    status: 'healthy' | 'warning' | 'error';
    connections: number;
    responseTime: number;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  storage: {
    used: number;
    total: number;
    percentage: number;
  };
  api: {
    status: 'healthy' | 'warning' | 'error';
    responseTime: number;
    errorRate: number;
  };
  users: {
    active: number;
    total: number;
    online: number;
  };
}

export default function SystemMonitor() {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const fetchSystemHealth = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/system-health');
      
      if (response.ok) {
        const data = await response.json();
        setHealth(data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Error al obtener estado del sistema:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemHealth();
    
    // Actualizar cada 30 segundos
    const interval = setInterval(fetchSystemHealth, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return AlertTriangle;
      default: return Activity;
    }
  };

  const MetricCard = ({ title, value, unit, status, icon: Icon, details }: any) => {
    const StatusIcon = getStatusIcon(status);
    
    return (
      <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Icon className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">{title}</h3>
          </div>
          <div className={`p-1 rounded-full ${getStatusColor(status)}`}>
            <StatusIcon className="h-4 w-4" />
          </div>
        </div>
        
        <div className="flex items-baseline">
          <span className="text-2xl font-bold text-gray-900">{value}</span>
          {unit && <span className="text-sm text-gray-500 ml-1">{unit}</span>}
        </div>
        
        {details && (
          <div className="mt-2 text-xs text-gray-600">
            {details}
          </div>
        )}
      </div>
    );
  };

  const ProgressBar = ({ percentage, color = 'blue' }: { percentage: number; color?: string }) => (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className={`bg-${color}-600 h-2 rounded-full transition-all duration-300`}
        style={{ width: `${Math.min(percentage, 100)}%` }}
      ></div>
    </div>
  );

  if (loading && !health) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Cargando estado del sistema...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          Monitor del Sistema
        </h2>
        <div className="flex items-center space-x-3">
          <span className="text-xs text-gray-500">
            Última actualización: {lastUpdate.toLocaleTimeString()}
          </span>
          <button
            onClick={fetchSystemHealth}
            disabled={loading}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Métricas Principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Base de Datos"
          value={health?.database.responseTime || 0}
          unit="ms"
          status={health?.database.status || 'healthy'}
          icon={Database}
          details={`${health?.database.connections || 0} conexiones activas`}
        />
        
        <MetricCard
          title="API"
          value={health?.api.responseTime || 0}
          unit="ms"
          status={health?.api.status || 'healthy'}
          icon={Cpu}
          details={`${health?.api.errorRate || 0}% tasa de error`}
        />
        
        <MetricCard
          title="Usuarios Activos"
          value={health?.users.active || 0}
          status="healthy"
          icon={Users}
          details={`${health?.users.online || 0} en línea`}
        />
        
        <MetricCard
          title="Total Noticias"
          value={health?.users.total || 0}
          status="healthy"
          icon={FileText}
          details="En el sistema"
        />
      </div>

      {/* Uso de Recursos */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Memoria */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Cpu className="h-5 w-5 mr-2" />
            Uso de Memoria
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Usado: {health?.memory.used || 0} MB</span>
              <span>Total: {health?.memory.total || 0} MB</span>
            </div>
            <ProgressBar 
              percentage={health?.memory.percentage || 0} 
              color={health?.memory.percentage && health.memory.percentage > 80 ? 'red' : 'blue'}
            />
            <div className="text-xs text-gray-600">
              {health?.memory.percentage || 0}% utilizado
            </div>
          </div>
        </div>

        {/* Almacenamiento */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <HardDrive className="h-5 w-5 mr-2" />
            Almacenamiento
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Usado: {health?.storage.used || 0} GB</span>
              <span>Total: {health?.storage.total || 0} GB</span>
            </div>
            <ProgressBar 
              percentage={health?.storage.percentage || 0}
              color={health?.storage.percentage && health.storage.percentage > 90 ? 'red' : 'green'}
            />
            <div className="text-xs text-gray-600">
              {health?.storage.percentage || 0}% utilizado
            </div>
          </div>
        </div>
      </div>

      {/* Estado General */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Estado General del Sistema</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-full ${getStatusColor(health?.database.status || 'healthy')} mr-3`}>
              <Database className="h-4 w-4" />
            </div>
            <div>
              <div className="font-medium">Base de Datos</div>
              <div className="text-sm text-gray-600 capitalize">{health?.database.status || 'healthy'}</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className={`p-2 rounded-full ${getStatusColor(health?.api.status || 'healthy')} mr-3`}>
              <Cpu className="h-4 w-4" />
            </div>
            <div>
              <div className="font-medium">API</div>
              <div className="text-sm text-gray-600 capitalize">{health?.api.status || 'healthy'}</div>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className="p-2 rounded-full text-green-600 bg-green-100 mr-3">
              <CheckCircle className="h-4 w-4" />
            </div>
            <div>
              <div className="font-medium">Sistema</div>
              <div className="text-sm text-gray-600">Operativo</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
