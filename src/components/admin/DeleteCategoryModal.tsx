'use client';

import { useState } from 'react';
import { X, Trash2, Alert<PERSON><PERSON>gle, Shield } from 'lucide-react';

interface Categoria {
  id: number;
  nombre: string;
  descripcion: string | null;
  color: string;
  isActive: boolean;
  orden: number;
  _count: {
    noticias: number;
  };
}

interface DeleteCategoryModalProps {
  categoria: Categoria;
  onClose: () => void;
  onSuccess: () => void;
}

export default function DeleteCategoryModal({ categoria, onClose, onSuccess }: DeleteCategoryModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');

  const canDelete = categoria._count.noticias === 0;
  const confirmationText = categoria.nombre.toUpperCase();

  const handleDelete = async () => {
    if (!canDelete) {
      setError('No se puede eliminar una categoría que tiene noticias asociadas');
      return;
    }

    if (confirmText !== confirmationText) {
      setError(`Debe escribir "${confirmationText}" para confirmar la eliminación`);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/categorias/${categoria.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        onSuccess();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Error al eliminar la categoría');
      }
    } catch (error) {
      console.error('Error al eliminar categoría:', error);
      setError('Error al eliminar la categoría');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-600" />
              </div>
            </div>
            <div className="ml-3">
              <h2 className="text-lg font-semibold text-gray-900">Eliminar Categoría</h2>
              <p className="text-sm text-gray-600">Esta acción no se puede deshacer</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Información de la categoría */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <div 
                className="w-4 h-4 rounded-full mr-3"
                style={{ backgroundColor: categoria.color }}
              ></div>
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {categoria.nombre}
                </div>
                <div className="text-xs text-gray-500">
                  ID: {categoria.id} | Orden: {categoria.orden}
                </div>
              </div>
            </div>
            {categoria.descripcion && (
              <div className="text-xs text-gray-600 mt-2">
                {categoria.descripcion}
              </div>
            )}
          </div>

          {/* Verificación de noticias asociadas */}
          {canDelete ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center text-green-800">
                <Shield className="w-5 h-5 mr-2" />
                <div>
                  <div className="text-sm font-medium">Categoría sin noticias asociadas</div>
                  <div className="text-xs">Es seguro eliminar esta categoría</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center text-red-800">
                <AlertTriangle className="w-5 h-5 mr-2" />
                <div>
                  <div className="text-sm font-medium">
                    No se puede eliminar
                  </div>
                  <div className="text-xs">
                    Esta categoría tiene {categoria._count.noticias} noticia{categoria._count.noticias !== 1 ? 's' : ''} asociada{categoria._count.noticias !== 1 ? 's' : ''}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-800">
              <AlertTriangle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}

          {/* Confirmación de eliminación */}
          {canDelete && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Para confirmar la eliminación, escriba el nombre de la categoría en mayúsculas:
              </label>
              <div className="mb-2">
                <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono">
                  {confirmationText}
                </code>
              </div>
              <input
                type="text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={`Escriba "${confirmationText}"`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Advertencias adicionales */}
          {canDelete && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">Advertencia:</div>
                  <ul className="text-xs space-y-1">
                    <li>• Esta acción eliminará permanentemente la categoría</li>
                    <li>• No se podrá recuperar la información</li>
                    <li>• Se registrará en el log de auditoría</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Sugerencias si no se puede eliminar */}
          {!canDelete && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-sm text-blue-800">
                <div className="font-medium mb-2">Opciones alternativas:</div>
                <ul className="text-xs space-y-1">
                  <li>• <strong>Desactivar la categoría:</strong> Las noticias existentes la mantendrán, pero no se podrán crear nuevas</li>
                  <li>• <strong>Reasignar noticias:</strong> Cambiar las noticias a otra categoría antes de eliminar</li>
                  <li>• <strong>Eliminar noticias:</strong> Eliminar todas las noticias asociadas primero</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          
          {canDelete ? (
            <button
              onClick={handleDelete}
              disabled={loading || confirmText !== confirmationText}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Eliminando...
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Eliminar Categoría
                </>
              )}
            </button>
          ) : (
            <button
              disabled
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed"
            >
              <Shield className="w-4 h-4 mr-2" />
              No se puede eliminar
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
