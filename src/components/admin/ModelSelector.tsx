'use client';

import { useState, useEffect } from 'react';
import { 
  Refresh<PERSON><PERSON>, 
  <PERSON>, 
  AlertTriangle, 
  CheckCircle, 
  Zap, 
  Star,
  Info,
  Loader2
} from 'lucide-react';
import { AIModel, ModelDiscoveryResult, ModelCache } from '@/lib/ai-model-discovery';

interface ModelSelectorProps {
  provider: 'OPENAI' | 'GEMINI';
  selectedModel: string;
  onModelChange: (model: string) => void;
  apiKey?: string;
  disabled?: boolean;
}

interface ModelsState {
  models: AIModel[];
  loading: boolean;
  error?: string;
  lastUpdated?: string;
  source?: 'api' | 'cache' | 'fallback';
}

export default function ModelSelector({
  provider,
  selectedModel,
  onModelChange,
  apiKey,
  disabled = false
}: ModelSelectorProps) {
  const [modelsState, setModelsState] = useState<ModelsState>({
    models: [],
    loading: true
  });
  const [refreshing, setRefreshing] = useState(false);

  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, [provider, apiKey]);

  const loadModels = async (forceRefresh = false) => {
    try {
      setModelsState(prev => ({ ...prev, loading: true, error: undefined }));

      const response = await fetch(
        `/api/admin/ai-config/models?provider=${provider}&refresh=${forceRefresh}&stats=true`
      );

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Error desconocido');
      }

      let models: AIModel[] = [];
      let lastUpdated: string | undefined;
      let source: 'api' | 'cache' | 'fallback' | undefined;

      if (result.data.openai && result.data.gemini) {
        // All providers response
        const providerData = provider === 'OPENAI' ? result.data.openai : result.data.gemini;
        models = providerData.models || [];
        lastUpdated = providerData.lastUpdated;
        source = providerData.source;
      } else {
        // Single provider response
        models = result.data.models || [];
        lastUpdated = result.data.lastUpdated;
        source = result.data.source;
      }

      setModelsState({
        models,
        loading: false,
        lastUpdated,
        source
      });

    } catch (error) {
      console.error('Error loading models:', error);
      setModelsState({
        models: [],
        loading: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadModels(true);
    setRefreshing(false);
  };

  const getStatusIcon = (status: AIModel['status']) => {
    switch (status) {
      case 'new':
        return <span title="Nuevo modelo"><Star className="h-3 w-3 text-blue-500" /></span>;
      case 'beta':
        return <span title="Modelo en beta"><Zap className="h-3 w-3 text-orange-500" /></span>;
      case 'deprecated':
        return <span title="Modelo obsoleto"><AlertTriangle className="h-3 w-3 text-red-500" /></span>;
      case 'active':
      default:
        return <span title="Modelo activo"><CheckCircle className="h-3 w-3 text-green-500" /></span>;
    }
  };

  const getStatusBadge = (status: AIModel['status']) => {
    const badges = {
      new: 'bg-blue-100 text-blue-800 border-blue-200',
      beta: 'bg-orange-100 text-orange-800 border-orange-200',
      deprecated: 'bg-red-100 text-red-800 border-red-200',
      active: 'bg-green-100 text-green-800 border-green-200'
    };

    const labels = {
      new: 'Nuevo',
      beta: 'Beta',
      deprecated: 'Obsoleto',
      active: 'Activo'
    };

    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${badges[status]}`}>
        {getStatusIcon(status)}
        <span className="ml-1">{labels[status]}</span>
      </span>
    );
  };

  const formatLastUpdated = (lastUpdated?: string) => {
    if (!lastUpdated) return 'Desconocido';
    
    const date = new Date(lastUpdated);
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffHours < 1) {
      return 'Hace menos de 1 hora';
    } else if (diffHours < 24) {
      return `Hace ${diffHours} hora${diffHours > 1 ? 's' : ''}`;
    } else {
      const diffDays = Math.floor(diffHours / 24);
      return `Hace ${diffDays} día${diffDays > 1 ? 's' : ''}`;
    }
  };

  const getSourceIcon = (source?: 'api' | 'cache' | 'fallback') => {
    switch (source) {
      case 'api':
        return <span title="Datos frescos de la API"><CheckCircle className="h-3 w-3 text-green-500" /></span>;
      case 'cache':
        return <span title="Datos del cache"><Clock className="h-3 w-3 text-blue-500" /></span>;
      case 'fallback':
        return <span title="Datos de respaldo"><AlertTriangle className="h-3 w-3 text-orange-500" /></span>;
      default:
        return <Info className="h-3 w-3 text-gray-500" />;
    }
  };

  if (modelsState.loading) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Modelo {provider === 'OPENAI' ? 'OpenAI' : 'Gemini'}
        </label>
        <div className="flex items-center space-x-2 p-3 border border-gray-300 rounded-md">
          <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
          <span className="text-sm text-gray-500">Cargando modelos disponibles...</span>
        </div>
      </div>
    );
  }

  if (modelsState.error) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Modelo {provider === 'OPENAI' ? 'OpenAI' : 'Gemini'}
        </label>
        <div className="space-y-2">
          <div className="flex items-center space-x-2 p-3 border border-red-300 rounded-md bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700">Error: {modelsState.error}</span>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-3 py-1 text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Reintentar</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          Modelo {provider === 'OPENAI' ? 'OpenAI' : 'Gemini'}
        </label>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            {getSourceIcon(modelsState.source)}
            <span>{formatLastUpdated(modelsState.lastUpdated)}</span>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing || disabled}
            className="flex items-center space-x-1 px-2 py-1 text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50 rounded"
            title="Actualizar lista de modelos"
          >
            <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Actualizar</span>
          </button>
        </div>
      </div>

      <select
        value={selectedModel}
        onChange={(e) => onModelChange(e.target.value)}
        disabled={disabled}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {modelsState.models.map((model) => (
          <option key={model.id} value={model.id}>
            {model.name} {model.status === 'new' ? '🆕' : model.status === 'beta' ? '🧪' : model.status === 'deprecated' ? '⚠️' : ''}
          </option>
        ))}
      </select>

      {/* Model details for selected model */}
      {selectedModel && (
        <div className="mt-2 p-3 bg-gray-50 rounded-md">
          {(() => {
            const model = modelsState.models.find(m => m.id === selectedModel);
            if (!model) return null;

            return (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900">{model.name}</h4>
                  {getStatusBadge(model.status)}
                </div>
                
                {model.description && (
                  <p className="text-xs text-gray-600">{model.description}</p>
                )}
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {model.maxTokens && (
                    <div>
                      <span className="font-medium">Max Tokens:</span> {model.maxTokens.toLocaleString()}
                    </div>
                  )}
                  {model.capabilities && model.capabilities.length > 0 && (
                    <div>
                      <span className="font-medium">Capacidades:</span> {model.capabilities.join(', ')}
                    </div>
                  )}
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Cache info */}
      {modelsState.source === 'cache' && (
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Clock className="h-3 w-3" />
          <span>Datos del cache - última actualización: {formatLastUpdated(modelsState.lastUpdated)}</span>
        </div>
      )}

      {modelsState.source === 'fallback' && (
        <div className="flex items-center space-x-2 text-xs text-orange-600">
          <AlertTriangle className="h-3 w-3" />
          <span>Usando modelos de respaldo - verifique la conexión a la API</span>
        </div>
      )}
    </div>
  );
}
