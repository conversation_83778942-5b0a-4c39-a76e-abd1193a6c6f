'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Newspaper, User, LogOut } from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';
import GlobalNotifications from '@/components/GlobalNotifications';
import NavigationButtons from '@/components/NavigationButtons';

interface AppHeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  backUrl?: string;
  backText?: string;
}

export default function AppHeader({ 
  title = "Panel Unificado V2", 
  subtitle,
  showBackButton = false,
  backUrl = "/dashboard",
  backText = "Volver al Dashboard"
}: AppHeaderProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' });
  };

  const handleLogoClick = () => {
    router.push('/dashboard');
  };

  const handleBackClick = () => {
    router.push(backUrl);
  };

  return (
    <header className="bg-background border-b border-border sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          {/* Left side - Logo and title */}
          <div className="flex items-center space-x-4 flex-shrink-0">
            {showBackButton && (
              <button
                onClick={handleBackClick}
                className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                {backText}
              </button>
            )}

            <div className="flex items-center cursor-pointer" onClick={handleLogoClick}>
              <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                <Newspaper className="h-5 w-5 text-primary-foreground" />
              </div>
              <div className="ml-3">
                <h1 className="text-lg lg:text-xl font-semibold text-foreground">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-xs lg:text-sm text-muted-foreground">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Center - Navigation Buttons */}
          <div className="flex-1 flex justify-center px-4">
            <NavigationButtons />
          </div>

          {/* Right side - Notifications, theme toggle, user info, logout */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            {/* Global Notifications */}
            {session && <GlobalNotifications />}

            {/* Theme Toggle */}
            <ThemeToggle />

            {/* User Info */}
            {session && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-foreground hidden sm:inline">
                  {session.user?.name || 'Usuario'}
                </span>
              </div>
            )}

            {/* Logout Button */}
            {session && (
              <button
                onClick={handleSignOut}
                className="p-1 rounded-full text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring focus:ring-offset-background transition-colors"
                aria-label="Cerrar sesión"
                title="Cerrar sesión"
              >
                <LogOut className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
