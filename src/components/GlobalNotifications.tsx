'use client';

import { useState, useEffect, useRef } from 'react';
import { Bell, X, Check, ExternalLink, Webhook } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Notificacion {
  id: number;
  tipo: string;
  titulo: string;
  mensaje?: string;
  leida: boolean;
  createdAt: string;
  noticia?: {
    id: number;
    titulo: string;
    periodista?: string;
    estado: string;
    categoria?: {
      nombre: string;
      color: string;
    };
  };
}

interface NotificationsData {
  notificaciones: Notificacion[];
  unreadCount: number;
  total: number;
}

export default function GlobalNotifications() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<NotificationsData>({
    notificaciones: [],
    unreadCount: 0,
    total: 0
  });
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Cargar notificaciones
  const loadNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notificaciones?limit=10');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data);
      }
    } catch (error) {
      console.error('Error al cargar notificaciones:', error);
    } finally {
      setLoading(false);
    }
  };

  // Marcar notificación como leída
  const markAsRead = async (notificationId: number) => {
    try {
      const response = await fetch(`/api/notificaciones/${notificationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ leida: true }),
      });

      if (response.ok) {
        // Actualizar estado local
        setNotifications(prev => ({
          ...prev,
          notificaciones: prev.notificaciones.map(n => 
            n.id === notificationId ? { ...n, leida: true } : n
          ),
          unreadCount: Math.max(0, prev.unreadCount - 1)
        }));
      }
    } catch (error) {
      console.error('Error al marcar notificación como leída:', error);
    }
  };

  // Marcar todas como leídas
  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notificaciones/mark-all-read', {
        method: 'POST',
      });

      if (response.ok) {
        setNotifications(prev => ({
          ...prev,
          notificaciones: prev.notificaciones.map(n => ({ ...n, leida: true })),
          unreadCount: 0
        }));
      }
    } catch (error) {
      console.error('Error al marcar todas las notificaciones como leídas:', error);
    }
  };

  // Manejar clic en notificación
  const handleNotificationClick = async (notification: Notificacion) => {
    // Marcar como leída si no lo está
    if (!notification.leida) {
      await markAsRead(notification.id);
    }

    // Navegar a la noticia si existe
    if (notification.noticia) {
      setIsOpen(false);
      router.push(`/noticias/${notification.noticia.id}/revision`);
    }
  };

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Cargar notificaciones al montar el componente
  useEffect(() => {
    loadNotifications();
  }, []);

  // Polling para actualizaciones en tiempo real (cada 30 segundos)
  useEffect(() => {
    const interval = setInterval(loadNotifications, 30000);
    return () => clearInterval(interval);
  }, []);

  // Formatear tiempo relativo
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Ahora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Botón de notificaciones */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors duration-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
        aria-label="Notificaciones"
      >
        <Bell className="h-6 w-6" />
        
        {/* Badge de contador */}
        {notifications.unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center min-w-[20px]">
            {notifications.unreadCount > 99 ? '99+' : notifications.unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown de notificaciones */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Notificaciones
            </h3>
            <div className="flex items-center space-x-2">
              {notifications.unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1"
                >
                  <Check className="h-4 w-4" />
                  <span>Marcar todas</span>
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Lista de notificaciones */}
          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                Cargando notificaciones...
              </div>
            ) : notifications.notificaciones.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                No hay notificaciones
              </div>
            ) : (
              notifications.notificaciones.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    !notification.leida ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icono */}
                    <div className="flex-shrink-0 mt-1">
                      {notification.tipo === 'WEBHOOK_NOTICIA' ? (
                        <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                          <Webhook className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                      ) : (
                        <div className="p-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                          <Bell className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Contenido */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className={`text-sm font-medium ${
                          !notification.leida 
                            ? 'text-gray-900 dark:text-white' 
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {notification.titulo}
                        </p>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                          {formatTimeAgo(notification.createdAt)}
                        </span>
                      </div>
                      
                      {notification.mensaje && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {notification.mensaje}
                        </p>
                      )}

                      {notification.noticia && (
                        <div className="mt-2 flex items-center space-x-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {notification.noticia.periodista && `Por: ${notification.noticia.periodista}`}
                          </span>
                          {notification.noticia.categoria && (
                            <span 
                              className="text-xs px-2 py-1 rounded-full text-white"
                              style={{ backgroundColor: notification.noticia.categoria.color }}
                            >
                              {notification.noticia.categoria.nombre}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Indicador de no leída */}
                      {!notification.leida && (
                        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-600 rounded-full"></div>
                      )}
                    </div>

                    {/* Icono de enlace */}
                    {notification.noticia && (
                      <ExternalLink className="h-4 w-4 text-gray-400 flex-shrink-0 mt-1" />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {notifications.total > 0 && (
            <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700 text-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Mostrando {notifications.notificaciones.length} de {notifications.total} notificaciones
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
