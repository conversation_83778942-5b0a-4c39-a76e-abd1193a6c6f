'use client';

import { areDevCredentialsEnabled, getDevCredentials } from '@/lib/dev-credentials';

/**
 * Componente para mostrar credenciales de desarrollo de forma segura
 * Solo se muestra en entorno de desarrollo y cuando está habilitado
 */
export function DevCredentialsDisplay() {
  // Verificar si las credenciales deben mostrarse
  if (!areDevCredentialsEnabled()) {
    return null;
  }

  const credentials = getDevCredentials();
  
  // Si no hay credenciales, no mostrar nada
  if (credentials.length === 0) {
    return null;
  }

  return (
    <div className="text-center mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
      <p className="text-xs text-yellow-700 font-medium mb-2">
        ⚠️ Credenciales de desarrollo (solo visible en desarrollo)
      </p>
      {credentials.map((cred, index) => (
        <p key={index} className="text-xs text-yellow-600">
          {cred.description}: {cred.email} / {cred.password}
        </p>
      ))}
      <p className="text-xs text-yellow-500 mt-1">
        Estas credenciales NO aparecen en producción
      </p>
    </div>
  );
}
