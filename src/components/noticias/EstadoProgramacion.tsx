'use client';

import { useState, useEffect } from 'react';
import { Clock, CheckCircle, AlertCircle } from 'lucide-react';

interface ProgramacionInfo {
  id: number;
  tipo: 'NOTICIA' | 'VERSION';
  fechaPublicacion: string;
  estado: 'PENDIENTE' | 'EJECUTADA' | 'ERROR';
  diarioExterno?: {
    nombre: string;
  };
}

interface EstadoProgramacionProps {
  noticiaId: number;
  className?: string;
}

export default function EstadoProgramacion({ noticiaId, className = '' }: EstadoProgramacionProps) {
  const [programaciones, setProgramaciones] = useState<ProgramacionInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchProgramaciones = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/noticias/${noticiaId}/programaciones`);
        
        if (response.ok) {
          const data = await response.json();
          setProgramaciones(data || []);
        }
      } catch (error) {
        console.error('Error fetching programaciones:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (noticiaId) {
      fetchProgramaciones();
    }
  }, [noticiaId]);

  if (isLoading) {
    return null; // No mostrar nada mientras carga
  }

  if (programaciones.length === 0) {
    return null; // No mostrar nada si no hay programaciones
  }

  const programacionesPendientes = programaciones.filter(p => p.estado === 'PENDIENTE');
  const programacionesEjecutadas = programaciones.filter(p => p.estado === 'EJECUTADA');
  const programacionesError = programaciones.filter(p => p.estado === 'ERROR');

  // Determinar el estado principal a mostrar
  let icono;
  let texto;
  let colorClass;

  if (programacionesError.length > 0) {
    icono = <AlertCircle className="h-3 w-3" />;
    texto = `${programacionesError.length} error${programacionesError.length > 1 ? 'es' : ''}`;
    colorClass = 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400';
  } else if (programacionesPendientes.length > 0) {
    icono = <Clock className="h-3 w-3" />;
    texto = `${programacionesPendientes.length} programada${programacionesPendientes.length > 1 ? 's' : ''}`;
    colorClass = 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400';
  } else if (programacionesEjecutadas.length > 0) {
    icono = <CheckCircle className="h-3 w-3" />;
    texto = `${programacionesEjecutadas.length} ejecutada${programacionesEjecutadas.length > 1 ? 's' : ''}`;
    colorClass = 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400';
  } else {
    return null;
  }

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium ${colorClass} ${className}`}>
      {icono}
      <span>{texto}</span>
    </div>
  );
}
