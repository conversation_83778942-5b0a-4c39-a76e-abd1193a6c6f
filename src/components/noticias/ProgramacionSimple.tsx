'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Send, Globe, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useProgramaciones } from '@/hooks/useProgramaciones';

interface Noticia {
  id: number;
  titulo: string;
  estado: string;
}

interface Version {
  id: number;
  titulo: string;
  diario: {
    id: number;
    nombre: string;
  };
  estado: string;
}

interface DiarioExterno {
  id: number;
  nombre: string;
  activo: boolean;
}

interface Programacion {
  id: number;
  tipo: string;
  fechaPublicacion: string;
  descripcion: string;
  estado: 'PENDIENTE' | 'EJECUTADO' | 'ERROR';
  diarioExterno?: {
    nombre: string;
  };
  version?: {
    diario: {
      nombre: string;
    };
  };
}

interface ProgramacionSimpleProps {
  noticia: Noticia;
  versiones: Version[];
}

export const ProgramacionSimple: React.FC<ProgramacionSimpleProps> = ({
  noticia,
  versiones
}) => {
  const [diariosExternos, setDiariosExternos] = useState<DiarioExterno[]>([]);
  const [loading, setLoading] = useState(false);
  const [mensaje, setMensaje] = useState<{tipo: 'success' | 'error', texto: string} | null>(null);

  // Hook para manejar programaciones
  const {
    programaciones,
    isLoading: loadingProgramaciones,
    refetch: refetchProgramaciones,
    getProgramacionesVersion,
    getProgramacionesNoticia
  } = useProgramaciones(noticia.id);

  // Estados para formularios
  const [showOriginalForm, setShowOriginalForm] = useState(false);
  const [showVersionForms, setShowVersionForms] = useState<{[key: number]: boolean}>({});
  
  // Estados para programación de noticia original
  const [originalDestino, setOriginalDestino] = useState<'especifico' | 'todos'>('especifico');
  const [originalDiarioId, setOriginalDiarioId] = useState<number | null>(null);
  const [originalFecha, setOriginalFecha] = useState('');
  const [originalHora, setOriginalHora] = useState('');

  // Estados para programación de versiones
  const [versionDestinos, setVersionDestinos] = useState<{[key: number]: 'especifico' | 'todos'}>({});
  const [versionDiarios, setVersionDiarios] = useState<{[key: number]: number | null}>({});
  const [versionFechas, setVersionFechas] = useState<{[key: number]: string}>({});
  const [versionHoras, setVersionHoras] = useState<{[key: number]: string}>({});

  useEffect(() => {
    cargarDiariosExternos();
  }, [noticia.id]);

  const cargarDiariosExternos = async () => {
    try {
      const response = await fetch('/api/diarios-externos?activo=true');
      if (response.ok) {
        const result = await response.json();
        // La API devuelve { success: true, data: diarios }
        const data = result.data || result;
        setDiariosExternos(Array.isArray(data) ? data : []);
      }
    } catch (error) {
      console.error('Error al cargar diarios externos:', error);
      setDiariosExternos([]);
    }
  };



  const programarOriginal = async () => {
    if (!originalFecha || !originalHora) {
      setMensaje({ tipo: 'error', texto: 'Fecha y hora son requeridas' });
      return;
    }

    if (originalDestino === 'especifico' && !originalDiarioId) {
      setMensaje({ tipo: 'error', texto: 'Selecciona un diario externo' });
      return;
    }

    setLoading(true);
    try {
      const fechaCompleta = new Date(`${originalFecha}T${originalHora}`);
      
      if (originalDestino === 'todos') {
        // Crear programación para cada diario externo
        for (const diario of (Array.isArray(diariosExternos) ? diariosExternos : [])) {
          await fetch('/api/programaciones', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              noticiaId: noticia.id,
              diarioExternoId: diario.id,
              tipo: 'ORIGINAL',
              fechaPublicacion: fechaCompleta.toISOString(),
              descripcion: `Noticia original → ${diario.nombre}`
            })
          });
        }
      } else {
        // Crear programación para diario específico
        await fetch('/api/programaciones', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            noticiaId: noticia.id,
            diarioExternoId: originalDiarioId,
            tipo: 'ORIGINAL',
            fechaPublicacion: fechaCompleta.toISOString(),
            descripcion: `Noticia original → ${Array.isArray(diariosExternos) ? diariosExternos.find(d => d.id === originalDiarioId)?.nombre : 'Diario externo'}`
          })
        });
      }

      setMensaje({ tipo: 'success', texto: 'Programación creada exitosamente' });
      setShowOriginalForm(false);
      setOriginalFecha('');
      setOriginalHora('');
      refetchProgramaciones();
    } catch (error) {
      setMensaje({ tipo: 'error', texto: 'Error al crear programación' });
    } finally {
      setLoading(false);
    }
  };

  const programarVersion = async (versionId: number) => {
    const fecha = versionFechas[versionId];
    const hora = versionHoras[versionId];
    const version = versiones.find(v => v.id === versionId);

    if (!fecha || !hora) {
      setMensaje({ tipo: 'error', texto: 'Fecha y hora son requeridas' });
      return;
    }

    if (!version) {
      setMensaje({ tipo: 'error', texto: 'Versión no encontrada' });
      return;
    }

    setLoading(true);
    try {
      const fechaCompleta = new Date(`${fecha}T${hora}`);

      // Para versiones, buscar el diario externo que corresponde al diario de la versión
      const diarioExternoCorrespondiente = Array.isArray(diariosExternos)
        ? diariosExternos.find(de => de.nombre.toLowerCase().includes(version.diario.nombre.toLowerCase().split(' ')[0]))
        : null;

      if (!diarioExternoCorrespondiente) {
        setMensaje({ tipo: 'error', texto: `No se encontró diario externo para ${version.diario.nombre}` });
        return;
      }
      
      // Para versiones, solo programar en el diario externo correspondiente
      await fetch('/api/programaciones', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          noticiaId: noticia.id,
          versionId: versionId,
          diarioExternoId: diarioExternoCorrespondiente.id,
          tipo: 'VERSION',
          fechaPublicacion: fechaCompleta.toISOString(),
          descripcion: `${version.diario.nombre} → ${diarioExternoCorrespondiente.nombre}`
        })
      });

      setMensaje({ tipo: 'success', texto: 'Programación creada exitosamente' });
      setShowVersionForms(prev => ({ ...prev, [versionId]: false }));
      setVersionFechas(prev => ({ ...prev, [versionId]: '' }));
      setVersionHoras(prev => ({ ...prev, [versionId]: '' }));
      refetchProgramaciones();
    } catch (error) {
      setMensaje({ tipo: 'error', texto: 'Error al crear programación' });
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
        <Calendar className="h-5 w-5 mr-2 text-blue-500" />
        Programar Publicación
      </h3>

      {mensaje && (
        <div className={`mb-4 p-3 rounded-md ${
          mensaje.tipo === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {mensaje.texto}
        </div>
      )}

      {/* Programaciones activas */}
      {programaciones.length > 0 && (
        <div className="mb-6 space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Programaciones activas:</h4>
          {programaciones.map(programacion => (
            <div key={programacion.id} className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-800 dark:text-blue-200">
                    {programacion.tipo === 'VERSION' ? 'Versión' : 'Original'} - {programacion.diarioExterno?.nombre}
                  </span>
                </div>
                <span className="text-xs text-blue-600 font-medium">
                  {new Date(programacion.fechaPublicacion).toLocaleString('es-ES', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              {programacion.descripcion && (
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  {programacion.descripcion}
                </p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Programar noticia original */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Noticia Original</h4>
          <button
            onClick={() => setShowOriginalForm(!showOriginalForm)}
            className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400"
          >
            {showOriginalForm ? 'Cancelar' : 'Programar'}
          </button>
        </div>

        {showOriginalForm && (
          <div className="space-y-3 p-3 bg-gray-50 dark:bg-gray-700 rounded border">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Destino
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="originalDestino"
                    value="especifico"
                    checked={originalDestino === 'especifico'}
                    onChange={(e) => setOriginalDestino(e.target.value as 'especifico')}
                    className="mr-2"
                  />
                  <span className="text-xs">Diario específico</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="originalDestino"
                    value="todos"
                    checked={originalDestino === 'todos'}
                    onChange={(e) => setOriginalDestino(e.target.value as 'todos')}
                    className="mr-2"
                  />
                  <span className="text-xs">Los 3 diarios simultáneamente</span>
                </label>
              </div>
            </div>

            {originalDestino === 'especifico' && (
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Diario externo
                </label>
                <select
                  value={originalDiarioId || ''}
                  onChange={(e) => setOriginalDiarioId(e.target.value ? parseInt(e.target.value) : null)}
                  className="w-full text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                >
                  <option value="">Seleccionar diario</option>
                  {Array.isArray(diariosExternos) && diariosExternos.map(diario => (
                    <option key={diario.id} value={diario.id}>{diario.nombre}</option>
                  ))}
                </select>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Fecha
                </label>
                <input
                  type="date"
                  value={originalFecha}
                  onChange={(e) => setOriginalFecha(e.target.value)}
                  className="w-full text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Hora
                </label>
                <input
                  type="time"
                  value={originalHora}
                  onChange={(e) => setOriginalHora(e.target.value)}
                  className="w-full text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                />
              </div>
            </div>

            <button
              onClick={programarOriginal}
              disabled={loading}
              className="w-full text-xs bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
            >
              {loading ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <Send className="h-3 w-3 mr-1" />
              )}
              Programar
            </button>
          </div>
        )}
      </div>

      {/* Programar versiones */}
      {versiones.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Versiones de IA</h4>
          <div className="space-y-3">
            {versiones.map(version => (
              <div key={version.id} className="border border-gray-200 dark:border-gray-600 rounded p-2">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    {version.diario.nombre}
                  </span>
                  <button
                    onClick={() => setShowVersionForms(prev => ({ 
                      ...prev, 
                      [version.id]: !prev[version.id] 
                    }))}
                    className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  >
                    {showVersionForms[version.id] ? 'Cancelar' : 'Programar'}
                  </button>
                </div>

                {showVersionForms[version.id] && (
                  <div className="space-y-3 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    {/* Mostrar destino fijo para versiones */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                          Se publicará en: {(() => {
                            const diarioExterno = Array.isArray(diariosExternos)
                              ? diariosExternos.find(de => de.nombre.toLowerCase().includes(version.diario.nombre.toLowerCase().split(' ')[0]))
                              : null;
                            return diarioExterno?.nombre || 'Diario correspondiente';
                          })()}
                        </span>
                      </div>
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        Esta versión está asociada a {version.diario.nombre}
                      </p>
                    </div>



                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Fecha
                        </label>
                        <input
                          type="date"
                          value={versionFechas[version.id] || ''}
                          onChange={(e) => setVersionFechas(prev => ({ 
                            ...prev, 
                            [version.id]: e.target.value 
                          }))}
                          className="w-full text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Hora
                        </label>
                        <input
                          type="time"
                          value={versionHoras[version.id] || ''}
                          onChange={(e) => setVersionHoras(prev => ({ 
                            ...prev, 
                            [version.id]: e.target.value 
                          }))}
                          className="w-full text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                        />
                      </div>
                    </div>

                    <button
                      onClick={() => programarVersion(version.id)}
                      disabled={loading}
                      className="w-full text-xs bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
                    >
                      {loading ? (
                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      ) : (
                        <Send className="h-3 w-3 mr-1" />
                      )}
                      Programar
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
