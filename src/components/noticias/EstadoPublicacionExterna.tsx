'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Clock, ExternalLink, RefreshCw, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

interface PublicacionDetalle {
  id: number;
  diario: string;
  estado: string;
  urlPublicacion?: string;
  fechaCreacion: string;
  fechaPublicacion?: string;
  error?: string;
}

interface EstadoPublicacion {
  noticia: {
    id: number;
    titulo: string;
    estado: string;
    publicada: boolean;
    fechaPublicacion?: string;
  };
  publicaciones: {
    total: number;
    exitosas: number;
    error: number;
    pendientes: number;
  };
  analisis: {
    puedeActualizarAutomaticamente: boolean;
    deberiaEstarPublicada: boolean;
    razon: string;
  };
  publicacionesDetalle: PublicacionDetalle[];
}

interface EstadoPublicacionExternaProps {
  noticiaId: number;
}

export default function EstadoPublicacionExterna({ noticiaId }: EstadoPublicacionExternaProps) {
  const [estado, setEstado] = useState<EstadoPublicacion | null>(null);
  const [loading, setLoading] = useState(true);
  const [actualizando, setActualizando] = useState(false);

  useEffect(() => {
    cargarEstado();
  }, [noticiaId]);

  const cargarEstado = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/noticias/${noticiaId}/update-status-on-publish`);
      const data = await response.json();
      
      if (data.success) {
        setEstado(data.data);
      } else {
        toast.error('Error al cargar estado de publicaciones');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar estado de publicaciones');
    } finally {
      setLoading(false);
    }
  };

  const actualizarEstado = async (force = false) => {
    try {
      setActualizando(true);
      const response = await fetch(`/api/noticias/${noticiaId}/update-status-on-publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ force })
      });

      const data = await response.json();
      
      if (response.ok) {
        toast.success('Estado de noticia actualizado exitosamente');
        await cargarEstado(); // Recargar estado
      } else {
        toast.error(data.error || 'Error al actualizar estado');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al actualizar estado');
    } finally {
      setActualizando(false);
    }
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'EXITOSO':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'PENDIENTE':
      case 'SUBIENDO_IMAGEN':
      case 'PUBLICANDO_ARTICULO':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-red-600 bg-red-50 border-red-200';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'EXITOSO':
        return <CheckCircle className="h-4 w-4" />;
      case 'PENDIENTE':
      case 'SUBIENDO_IMAGEN':
      case 'PUBLICANDO_ARTICULO':
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ExternalLink className="h-5 w-5 mr-2" />
            Estado de Publicaciones Externas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!estado) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ExternalLink className="h-5 w-5 mr-2" />
            Estado de Publicaciones Externas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">No se pudo cargar el estado de las publicaciones</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <ExternalLink className="h-5 w-5 mr-2" />
            Estado de Publicaciones Externas
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => cargarEstado()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Resumen de Estado */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {estado.publicaciones.exitosas}
            </div>
            <div className="text-xs text-muted-foreground">Exitosas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {estado.publicaciones.pendientes}
            </div>
            <div className="text-xs text-muted-foreground">Pendientes</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {estado.publicaciones.error}
            </div>
            <div className="text-xs text-muted-foreground">Con Error</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {estado.publicaciones.total}
            </div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
        </div>

        {/* Estado de la Noticia */}
        <div className="border rounded-lg p-4 bg-muted/50">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium">Estado de la Noticia</h4>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              estado.noticia.estado === 'PUBLICADA' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {estado.noticia.estado}
            </span>
          </div>
          
          {estado.analisis.deberiaEstarPublicada && (
            <div className="flex items-start space-x-3 mb-3">
              <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm text-amber-700 mb-2">
                  {estado.analisis.razon}
                </p>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{estado.noticia.estado}</span>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-green-600">PUBLICADA</span>
                </div>
              </div>
            </div>
          )}

          {estado.analisis.deberiaEstarPublicada && (
            <div className="flex space-x-2">
              <Button
                size="sm"
                onClick={() => actualizarEstado(false)}
                disabled={actualizando || !estado.analisis.puedeActualizarAutomaticamente}
                className="flex-1"
              >
                {actualizando ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Actualizar a PUBLICADA
              </Button>
              
              {!estado.analisis.puedeActualizarAutomaticamente && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => actualizarEstado(true)}
                  disabled={actualizando}
                >
                  Forzar
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Lista de Publicaciones */}
        {estado.publicacionesDetalle.length > 0 && (
          <div>
            <h4 className="font-medium mb-3">Publicaciones por Diario</h4>
            <div className="space-y-2">
              {estado.publicacionesDetalle.map((pub) => (
                <div key={pub.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{pub.diario}</span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getEstadoColor(pub.estado)}`}>
                        {getEstadoIcon(pub.estado)}
                        <span className="ml-1">{pub.estado}</span>
                      </span>
                    </div>
                    {pub.urlPublicacion && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(pub.urlPublicacion, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>Creado: {formatearFecha(pub.fechaCreacion)}</div>
                    {pub.fechaPublicacion && (
                      <div>Publicado: {formatearFecha(pub.fechaPublicacion)}</div>
                    )}
                    {pub.error && (
                      <div className="text-red-600">Error: {pub.error}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {estado.publicacionesDetalle.length === 0 && (
          <div className="text-center py-6 text-muted-foreground">
            <ExternalLink className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No hay publicaciones externas para esta noticia</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
