'use client';

import { useState, useEffect } from 'react';
import { Share2, TrendingUp, ExternalLink, Calendar, CheckCircle, AlertCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface PublicacionReciente {
  id: number;
  titulo: string;
  diario: string;
  categoria: string;
  categoriaColor: string;
  fecha: string;
  url: string;
  esVersion?: boolean;
  versionDiario?: string;
}

interface EstadisticasRedes {
  general: {
    total: number;
    exitosas: number;
    error: number;
    pendientes: number;
    tasaExito: number;
  };
  porDiario: Array<{
    diarioId: number;
    diarioNombre: string;
    publicaciones: number;
  }>;
  recientes: PublicacionReciente[];
}

interface RedesSocialesStatsProps {
  onNavigateToRedes: () => void;
}

export default function RedesSocialesStats({ onNavigateToRedes }: RedesSocialesStatsProps) {
  const [stats, setStats] = useState<EstadisticasRedes | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    cargarEstadisticas();
  }, []);

  const cargarEstadisticas = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/publicaciones-externas/stats');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.estadisticas);
        }
      }
    } catch (error) {
      console.error('Error al cargar estadísticas de redes sociales:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const abrirEnlace = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Share2 className="h-5 w-5 mr-2 text-blue-600" />
            Redes Sociales
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Share2 className="h-5 w-5 mr-2 text-blue-600" />
            Redes Sociales
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            No se pudieron cargar las estadísticas
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Share2 className="h-5 w-5 mr-2 text-blue-600" />
            Redes Sociales
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={onNavigateToRedes}
            className="text-blue-600 border-blue-600 hover:bg-blue-50"
          >
            Ver Todo
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Estadísticas Generales */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {stats.general.exitosas}
            </div>
            <div className="text-xs text-muted-foreground">
              Publicaciones Exitosas
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {stats.general.tasaExito}%
            </div>
            <div className="text-xs text-muted-foreground">
              Tasa de Éxito
            </div>
          </div>
        </div>

        {/* Estadísticas por Estado */}
        <div className="flex justify-between text-xs">
          <div className="flex items-center">
            <CheckCircle className="h-3 w-3 text-green-600 mr-1" />
            <span>{stats.general.exitosas} Exitosas</span>
          </div>
          {stats.general.error > 0 && (
            <div className="flex items-center">
              <AlertCircle className="h-3 w-3 text-red-600 mr-1" />
              <span>{stats.general.error} Errores</span>
            </div>
          )}
          {stats.general.pendientes > 0 && (
            <div className="flex items-center">
              <Calendar className="h-3 w-3 text-yellow-600 mr-1" />
              <span>{stats.general.pendientes} Pendientes</span>
            </div>
          )}
        </div>

        {/* Top Diarios */}
        {stats.porDiario.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Top Diarios</h4>
            <div className="space-y-1">
              {stats.porDiario.slice(0, 3).map((diario) => (
                <div key={diario.diarioId} className="flex justify-between items-center text-xs">
                  <span className="truncate">{diario.diarioNombre}</span>
                  <span className="inline-flex items-center rounded-full bg-secondary text-secondary-foreground px-2.5 py-0.5 text-xs font-semibold">
                    {diario.publicaciones}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Publicaciones Recientes */}
        {stats.recientes.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">Publicaciones Recientes</h4>
            <div className="space-y-2">
              {stats.recientes.slice(0, 3).map((pub) => (
                <div key={pub.id} className="border rounded-lg p-2 hover:bg-accent/50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium truncate">
                        {pub.titulo}
                      </p>
                      {pub.esVersion && (
                        <div className="mt-1">
                          <span className="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                            🤖 IA
                          </span>
                        </div>
                      )}
                      <div className="flex items-center mt-1 space-x-2">
                        <span
                          className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold"
                          style={{
                            backgroundColor: pub.categoriaColor + '20',
                            color: pub.categoriaColor
                          }}
                        >
                          {pub.categoria}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {pub.diario}
                        </span>
                      </div>
                      <div className="flex items-center mt-1">
                        <Calendar className="h-3 w-3 text-muted-foreground mr-1" />
                        <span className="text-xs text-muted-foreground">
                          {formatearFecha(pub.fecha)}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => abrirEnlace(pub.url)}
                      className="ml-2 h-6 w-6 p-0"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Mensaje si no hay publicaciones */}
        {stats.general.total === 0 && (
          <div className="text-center py-4">
            <Share2 className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No hay publicaciones externas aún
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
