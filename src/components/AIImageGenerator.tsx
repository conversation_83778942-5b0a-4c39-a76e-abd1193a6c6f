'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Loader2, <PERSON><PERSON><PERSON>, Setting<PERSON>, History } from 'lucide-react';

interface AIImageGeneratorProps {
  titulo: string;
  resumen?: string;
  contenido?: string;
  onImageGenerated: (imageUrl: string) => void;
  disabled?: boolean;
}

interface ConfiguracionIA {
  id: number;
  nombre: string;
  modelo: string;
  proveedor: string;
  activo: boolean;
}

interface GeneracionResult {
  id: number;
  imagenUrl?: string;
  estado: string;
  error?: string;
  tiempoGeneracion?: number;
}

export default function AIImageGenerator({
  titulo,
  resumen,
  contenido,
  onImageGenerated,
  disabled = false,
}: AIImageGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [configuraciones, setConfiguraciones] = useState<ConfiguracionIA[]>([]);
  const [configuracionSeleccionada, setConfiguracionSeleccionada] = useState<number | null>(null);
  const [estilo, setEstilo] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [lastGeneration, setLastGeneration] = useState<GeneracionResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Cargar configuraciones disponibles
  useEffect(() => {
    loadConfiguraciones();
  }, []);

  const loadConfiguraciones = async () => {
    try {
      const response = await fetch('/api/ai/configuraciones');
      if (response.ok) {
        const data = await response.json();
        setConfiguraciones(data.data);

        // Seleccionar la primera configuración por defecto
        if (data.data.length > 0) {
          setConfiguracionSeleccionada(data.data[0].id);
        }
      } else {
        console.error('Error al cargar configuraciones:', response.status);
      }
    } catch (error) {
      console.error('Error al cargar configuraciones:', error);
    }
  };

  const handleGenerate = async () => {
    if (!titulo.trim()) {
      setError('El título es requerido para generar la imagen');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/generar-imagen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          titulo,
          resumen,
          contenido,
          estilo: estilo || undefined,
          configuracionId: configuracionSeleccionada,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al generar imagen');
      }

      const resultado = data.data as GeneracionResult;
      setLastGeneration(resultado);

      if (resultado.imagenUrl) {
        onImageGenerated(resultado.imagenUrl);
      } else if (resultado.error) {
        setError(resultado.error);
      }

    } catch (error) {
      console.error('Error al generar imagen:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setIsGenerating(false);
    }
  };

  const estilosPredefindos = [
    { value: '', label: 'Por defecto' },
    { value: 'photorealistic, professional photography, high quality', label: 'Fotorrealista' },
    { value: 'illustration, digital art, detailed', label: 'Ilustración Digital' },
    { value: 'minimalist, clean, simple composition', label: 'Minimalista' },
    { value: 'dramatic lighting, cinematic, professional', label: 'Cinematográfico' },
    { value: 'journalistic, news photography, documentary style', label: 'Periodístico' },
    { value: 'artistic, creative, stylized', label: 'Artístico' },
  ];

  return (
    <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
            <Sparkles className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Generador de Imágenes IA
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Genera imágenes con datos de la noticia
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          title="Configuración avanzada"
        >
          <Settings className="h-5 w-5" />
        </button>
      </div>

      {/* Configuración avanzada */}
      {showAdvanced && (
        <div className="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Selección de configuración */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Modelo IA
              </label>
              <select
                value={configuracionSeleccionada || ''}
                onChange={(e) => setConfiguracionSeleccionada(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {configuraciones.map((config) => (
                  <option key={config.id} value={config.id}>
                    {config.nombre}
                    {config.modelo.includes('imagen-4') && ' (Ultra Calidad)'}
                    {config.modelo.includes('imagen-3') && ' (Estándar)'}
                    {config.modelo.includes('gemini-2') && ' (Conversacional)'}
                  </option>
                ))}
              </select>
            </div>

            {/* Selección de estilo */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Estilo
              </label>
              <select
                value={estilo}
                onChange={(e) => setEstilo(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {estilosPredefindos.map((estilo) => (
                  <option key={estilo.value} value={estilo.value}>
                    {estilo.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Botón de generación */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={handleGenerate}
          disabled={disabled || isGenerating || !titulo.trim() || configuraciones.length === 0}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg font-medium transition-all duration-200 disabled:cursor-not-allowed"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Generando...</span>
            </>
          ) : (
            <>
              <Bot className="h-5 w-5" />
              <span>Generar Imagen</span>
            </>
          )}
        </button>

        {configuraciones.length === 0 && (
          <div className="text-sm text-amber-600 dark:text-amber-400">
            <p className="font-medium">No hay configuraciones de IA disponibles</p>
            <p className="text-xs mt-1">
              El administrador debe configurar los modelos de generación de imágenes en el panel de administración.
              Se utilizará la API key de Gemini configurada globalmente.
            </p>
          </div>
        )}
      </div>

      {/* Error */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Resultado de la última generación */}
      {lastGeneration && (
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Última generación
            </h4>
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              lastGeneration.estado === 'COMPLETADA' 
                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                : lastGeneration.estado === 'ERROR'
                ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
            }`}>
              {lastGeneration.estado}
            </span>
          </div>
          
          {lastGeneration.imagenUrl && (
            <div className="mt-2">
              <img
                src={lastGeneration.imagenUrl}
                alt="Imagen generada por IA"
                className="w-full max-w-xs h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
              />
            </div>
          )}
          
          {lastGeneration.tiempoGeneracion && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Generada en {(lastGeneration.tiempoGeneracion / 1000).toFixed(1)}s
            </p>
          )}
        </div>
      )}

      {/* Información de ayuda */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <div className="flex items-start space-x-2">
          
          <div className="text-sm text-blue-700 dark:text-blue-300">
            
            <ul className="text-xs space-y-1 list-disc list-inside">
              
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
