'use client';

import { useState, useEffect } from 'react';
import { ExternalLink, CheckCircle, XCircle, Clock, AlertCircle, RefreshCw, Globe, Calendar, Link } from 'lucide-react';

interface PublicacionExterna {
  id: number;
  estado: 'PENDIENTE' | 'PROCESANDO' | 'COMPLETADO' | 'ERROR';
  urlPublicacion?: string;
  imagenExternaId?: string;
  articuloExternoId?: string;
  errorMensaje?: string;
  metadatos?: any;
  fechaCreacion: string;
  fechaActualizacion: string;
  diario: {
    id: number;
    nombre: string;
    urlBase: string;
    descripcion?: string;
  };
}

interface PublicacionesExternasProps {
  noticiaId: number;
}

export default function PublicacionesExternas({ noticiaId }: PublicacionesExternasProps) {
  const [publicaciones, setPublicaciones] = useState<PublicacionExterna[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPublicaciones = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/noticias/${noticiaId}/publicaciones-externas`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al cargar publicaciones');
      }

      setPublicaciones(data.publicaciones || []);
    } catch (err) {
      console.error('Error al cargar publicaciones externas:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (noticiaId) {
      fetchPublicaciones();
    }
  }, [noticiaId]);

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'COMPLETADO':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'ERROR':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'PROCESANDO':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'PENDIENTE':
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'COMPLETADO':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'ERROR':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'PROCESANDO':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'PENDIENTE':
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-l-4 border-purple-500 dark:border-purple-400">
        <div className="px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-100 dark:border-purple-800">
          <h2 className="text-xl font-bold text-purple-900 dark:text-purple-100 flex items-center">
            <Globe className="h-6 w-6 mr-2" />
            Notas Publicadas
          </h2>
        </div>
        <div className="p-6 text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
          <p className="mt-2 text-gray-600 dark:text-gray-400">Cargando publicaciones...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-l-4 border-red-500 dark:border-red-400">
        <div className="px-6 py-4 bg-red-50 dark:bg-red-900/20 border-b border-red-100 dark:border-red-800">
          <h2 className="text-xl font-bold text-red-900 dark:text-red-100 flex items-center">
            <AlertCircle className="h-6 w-6 mr-2" />
            Error al cargar publicaciones
          </h2>
        </div>
        <div className="p-6">
          <p className="text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={fetchPublicaciones}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-l-4 border-purple-500 dark:border-purple-400">
      <div className="px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-100 dark:border-purple-800">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-purple-900 dark:text-purple-100 flex items-center">
            <Globe className="h-6 w-6 mr-2" />
            Notas Publicadas
          </h2>
          <button
            onClick={fetchPublicaciones}
            className="p-2 text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200 transition-colors"
            title="Actualizar"
          >
            <RefreshCw className="h-5 w-5" />
          </button>
        </div>
      </div>

      <div className="p-6">
        {publicaciones.length === 0 ? (
          <div className="text-center py-8">
            <Globe className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Esta noticia aún no ha sido publicada en diarios externos.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {publicaciones.map((publicacion) => (
              <div
                key={publicacion.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getEstadoIcon(publicacion.estado)}
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">
                        {publicacion.diario.nombre}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoColor(publicacion.estado)}`}>
                        {publicacion.estado}
                      </span>
                    </div>

                    <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>Publicado: {formatearFecha(publicacion.fechaCreacion)}</span>
                      </div>
                      
                      {publicacion.fechaActualizacion !== publicacion.fechaCreacion && (
                        <div className="flex items-center space-x-2">
                          <RefreshCw className="h-4 w-4" />
                          <span>Actualizado: {formatearFecha(publicacion.fechaActualizacion)}</span>
                        </div>
                      )}
                    </div>

                    {publicacion.errorMensaje && (
                      <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-300">
                        <strong>Error:</strong> {publicacion.errorMensaje}
                      </div>
                    )}
                  </div>

                  {publicacion.urlPublicacion && (
                    <div className="ml-4">
                      <a
                        href={publicacion.urlPublicacion}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>Ver Publicación</span>
                      </a>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
