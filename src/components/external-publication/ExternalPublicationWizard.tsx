'use client';

import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  ExternalLink,
  ArrowLeft,
  ArrowRight,
  Globe,
  Image,
  FileText
} from 'lucide-react';
import { DiarioExterno, PublicationWizardData, WizardStep } from '@/types/external-publication';

interface ExternalPublicationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  noticiaId: number;
  noticia: {
    titulo: string;
    imagenUrl?: string;
    categoriaId?: number;
    categoria?: {
      nombre: string;
    };
  };
}

interface StepConfig {
  id: WizardStep;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const STEPS: StepConfig[] = [
  {
    id: 'select-diario',
    title: 'Selecci<PERSON>r Diario',
    description: 'Elige el diario donde publicar',
    icon: <Globe className="h-5 w-5" />
  },
  {
    id: 'confirm-categories',
    title: 'Confirmar Categorías',
    description: 'Verificar mapeo de categorías',
    icon: <FileText className="h-5 w-5" />
  },
  {
    id: 'publish',
    title: 'Publicar',
    description: 'Subir imagen y publicar artículo',
    icon: <Image className="h-5 w-5" />
  },
  {
    id: 'result',
    title: 'Resultado',
    description: 'Ver resultado de la publicación',
    icon: <CheckCircle className="h-5 w-5" />
  }
];

export default function ExternalPublicationWizard({
  isOpen,
  onClose,
  noticiaId,
  noticia
}: ExternalPublicationWizardProps) {
  const [currentStep, setCurrentStep] = useState<WizardStep>('select-diario');
  const [wizardData, setWizardData] = useState<PublicationWizardData>({
    categoriasMapeadas: []
  });
  const [diarios, setDiarios] = useState<DiarioExterno[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [publishResult, setPublishResult] = useState<any>(null);

  // Cargar diarios activos al abrir
  useEffect(() => {
    if (isOpen) {
      loadDiarios();
    }
  }, [isOpen]);

  const loadDiarios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/diarios-externos?activo=true&includeMapeos=true');
      const data = await response.json();
      
      if (data.success) {
        setDiarios(data.data);
      } else {
        setError('Error al cargar diarios externos');
      }
    } catch (error) {
      setError('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStepIndex = () => {
    return STEPS.findIndex(step => step.id === currentStep);
  };

  const getProgress = () => {
    return ((getCurrentStepIndex() + 1) / STEPS.length) * 100;
  };

  const canGoNext = () => {
    switch (currentStep) {
      case 'select-diario':
        return !!wizardData.diarioExternoId;
      case 'confirm-categories':
        return wizardData.categoriasMapeadas.length > 0;
      case 'publish':
        return false; // Se maneja automáticamente
      case 'result':
        return false;
      default:
        return false;
    }
  };

  const canGoPrevious = () => {
    return currentStep !== 'select-diario' && currentStep !== 'result';
  };

  const handleNext = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex < STEPS.length - 1) {
      setCurrentStep(STEPS[currentIndex + 1].id);
    }
  };

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(STEPS[currentIndex - 1].id);
    }
  };

  const handlePublish = async () => {
    if (!wizardData.diarioExternoId) return;

    try {
      setLoading(true);
      setCurrentStep('publish');
      
      const response = await fetch(`/api/noticias/${noticiaId}/publish-external`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioExternoId: wizardData.diarioExternoId
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        setPublishResult(result.data);
        setCurrentStep('result');
      } else {
        setError(result.error || 'Error al publicar');
      }
    } catch (error) {
      setError('Error de conexión al publicar');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setCurrentStep('select-diario');
    setWizardData({ categoriasMapeadas: [] });
    setError(null);
    setPublishResult(null);
    onClose();
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'select-diario':
        return (
          <SelectDiarioStep
            diarios={diarios}
            selectedDiarioId={wizardData.diarioExternoId}
            onSelect={(diarioId) => setWizardData(prev => ({ ...prev, diarioExternoId: diarioId }))}
            loading={loading}
          />
        );
      
      case 'confirm-categories':
        return (
          <ConfirmCategoriesStep
            diarioId={wizardData.diarioExternoId!}
            noticiaCategoria={noticia.categoria}
            onCategoriesConfirmed={(categorias) => 
              setWizardData(prev => ({ ...prev, categoriasMapeadas: categorias }))
            }
          />
        );
      
      case 'publish':
        return (
          <PublishStep
            loading={loading}
            error={error}
          />
        );
      
      case 'result':
        return (
          <ResultStep
            result={publishResult}
            onClose={handleClose}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" aria-describedby="external-publication-description">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Publicar Externamente: {noticia.titulo}
          </DialogTitle>
          <DialogDescription id="external-publication-description">
            Selecciona el diario externo y confirma las categorías para publicar esta noticia.
          </DialogDescription>
        </DialogHeader>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Paso {getCurrentStepIndex() + 1} de {STEPS.length}</span>
            <span>{Math.round(getProgress())}%</span>
          </div>
          <Progress value={getProgress()} className="h-2" />
        </div>

        {/* Steps Indicator */}
        <div className="flex justify-between">
          {STEPS.map((step, index) => {
            const isActive = step.id === currentStep;
            const isCompleted = getCurrentStepIndex() > index;
            
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center space-y-2 ${
                  isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'
                }`}
              >
                <div className={`p-2 rounded-full border-2 ${
                  isActive 
                    ? 'border-primary bg-primary/10' 
                    : isCompleted 
                    ? 'border-green-600 bg-green-50' 
                    : 'border-muted'
                }`}>
                  {isCompleted ? <CheckCircle className="h-5 w-5" /> : step.icon}
                </div>
                <div className="text-center">
                  <div className="text-sm font-medium">{step.title}</div>
                  <div className="text-xs">{step.description}</div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step Content */}
        <div className="min-h-[300px]">
          {renderStepContent()}
        </div>

        {/* Navigation Buttons */}
        {currentStep !== 'result' && (
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={!canGoPrevious() || loading}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Anterior
            </Button>

            {currentStep === 'confirm-categories' ? (
              <Button
                onClick={handlePublish}
                disabled={!canGoNext() || loading}
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <ExternalLink className="h-4 w-4 mr-2" />
                )}
                Publicar
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canGoNext() || loading}
              >
                Siguiente
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// ========================================
// COMPONENTES DE PASOS DEL WIZARD
// ========================================

interface SelectDiarioStepProps {
  diarios: DiarioExterno[];
  selectedDiarioId?: number;
  onSelect: (diarioId: number) => void;
  loading: boolean;
}

function SelectDiarioStep({ diarios, selectedDiarioId, onSelect, loading }: SelectDiarioStepProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Cargando diarios...</span>
      </div>
    );
  }

  if (diarios.length === 0) {
    return (
      <div className="text-center py-12">
        <Globe className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No hay diarios configurados</h3>
        <p className="text-muted-foreground">
          Contacta al administrador para configurar diarios externos.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium mb-2">Selecciona el diario destino</h3>
        <p className="text-muted-foreground">
          Elige dónde quieres publicar esta noticia
        </p>
      </div>

      <div className="grid gap-4">
        {diarios.map((diario) => (
          <div
            key={diario.id}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedDiarioId === diario.id
                ? 'border-primary bg-primary/5'
                : 'border-border hover:border-primary/50'
            }`}
            onClick={() => onSelect(diario.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  diario.activo ? 'bg-green-500' : 'bg-red-500'
                }`} />
                <div>
                  <h4 className="font-medium">{diario.nombre}</h4>
                  <p className="text-sm text-muted-foreground">{diario.urlBase}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-muted-foreground">
                  {(diario as any)._count?.categoriaMapeos || 0} categorías mapeadas
                </div>
                <div className="text-sm text-muted-foreground">
                  {(diario as any)._count?.publicacionesExternas || 0} publicaciones
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface ConfirmCategoriesStepProps {
  diarioId: number;
  noticiaCategoria?: { nombre: string };
  onCategoriesConfirmed: (categorias: number[]) => void;
}

function ConfirmCategoriesStep({ diarioId, noticiaCategoria, onCategoriesConfirmed }: ConfirmCategoriesStepProps) {
  const [mapeos, setMapeos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMapeos();
  }, [diarioId]);

  const loadMapeos = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/diarios-externos/${diarioId}/mapeos`);
      const data = await response.json();

      if (data.success) {
        setMapeos(data.data);
        // Auto-confirmar categorías mapeadas
        const categoriaIds = data.data.map((m: any) => m.categoriaExternaId);
        onCategoriesConfirmed(categoriaIds);
      }
    } catch (error) {
      console.error('Error loading mapeos:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Verificando categorías...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium mb-2">Confirmar Categorías</h3>
        <p className="text-muted-foreground">
          Verifica el mapeo de categorías para la publicación
        </p>
      </div>

      {noticiaCategoria && (
        <div className="bg-muted p-4 rounded-lg">
          <h4 className="font-medium mb-2">Categoría de la noticia:</h4>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-primary rounded-full" />
            <span>{noticiaCategoria.nombre}</span>
          </div>
        </div>
      )}

      <div className="space-y-3">
        <h4 className="font-medium">Categorías mapeadas:</h4>
        {mapeos.length > 0 ? (
          mapeos.map((mapeo) => (
            <div key={mapeo.id} className="flex items-center justify-between p-3 border rounded">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span>{mapeo.categoriaLocal.nombre}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                ID Externa: {mapeo.categoriaExternaId}
              </div>
            </div>
          ))
        ) : (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No hay categorías mapeadas para este diario. La noticia se publicará sin categoría específica.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}

interface PublishStepProps {
  loading: boolean;
  error: string | null;
}

function PublishStep({ loading, error }: PublishStepProps) {
  return (
    <div className="text-center py-12">
      {loading ? (
        <div className="space-y-4">
          <Loader2 className="h-12 w-12 mx-auto animate-spin text-primary" />
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Publicando noticia...</h3>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>• Subiendo imagen al diario externo</div>
              <div>• Preparando contenido del artículo</div>
              <div>• Publicando en el diario seleccionado</div>
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="space-y-4">
          <AlertCircle className="h-12 w-12 mx-auto text-destructive" />
          <div>
            <h3 className="text-lg font-medium text-destructive mb-2">Error en la publicación</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <CheckCircle className="h-12 w-12 mx-auto text-green-500" />
          <h3 className="text-lg font-medium">Preparando publicación...</h3>
        </div>
      )}
    </div>
  );
}

interface ResultStepProps {
  result: any;
  onClose: () => void;
}

function ResultStep({ result, onClose }: ResultStepProps) {
  return (
    <div className="text-center py-8">
      <CheckCircle className="h-16 w-16 mx-auto text-green-500 mb-4" />
      <h3 className="text-xl font-medium mb-2">¡Publicación exitosa!</h3>
      <p className="text-muted-foreground mb-6">
        La noticia ha sido publicada correctamente en el diario externo.
      </p>

      {result?.urlPublicacion && (
        <div className="bg-muted p-4 rounded-lg mb-6">
          <h4 className="font-medium mb-2">Enlace de publicación:</h4>
          <a
            href={result.urlPublicacion}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-primary hover:underline"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Ver noticia publicada
          </a>
        </div>
      )}

      <div className="space-y-2 text-sm text-muted-foreground mb-6">
        <div>ID de imagen: {result?.imagenExternaId}</div>
        <div>ID de artículo: {result?.articuloExternoId}</div>
        <div>Estado: {result?.estado}</div>
      </div>

      <Button onClick={onClose} className="w-full">
        Cerrar
      </Button>
    </div>
  );
}
