/* Estilos para editores de contenido */

.content-editor {
  /* Mejorar la estructura visual del texto */
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.content-editor p {
  margin-bottom: 1rem;
  margin-top: 0;
}

.content-editor p:last-child {
  margin-bottom: 0;
}

.content-editor h1,
.content-editor h2,
.content-editor h3,
.content-editor h4,
.content-editor h5,
.content-editor h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.25;
}

.content-editor h1:first-child,
.content-editor h2:first-child,
.content-editor h3:first-child,
.content-editor h4:first-child,
.content-editor h5:first-child,
.content-editor h6:first-child {
  margin-top: 0;
}

.content-editor ul,
.content-editor ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.content-editor li {
  margin-bottom: 0.25rem;
}

.content-editor blockquote {
  margin: 1rem 0;
  padding-left: 1rem;
  border-left: 4px solid #e5e7eb;
  font-style: italic;
  color: #6b7280;
}

.dark .content-editor blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.content-editor a {
  color: #2563eb;
  text-decoration: underline;
}

.dark .content-editor a {
  color: #60a5fa;
}

.content-editor strong {
  font-weight: 600;
}

.content-editor em {
  font-style: italic;
}

.content-editor code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

.dark .content-editor code {
  background-color: #374151;
}

/* Placeholder styling */
.content-editor:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

.dark .content-editor:empty:before {
  color: #6b7280;
}

/* Focus styling */
.content-editor:focus {
  outline: none;
}

/* Evitar que el contenido se desborde */
.content-editor {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Mejorar la selección de texto */
.content-editor::selection {
  background-color: #dbeafe;
}

.dark .content-editor::selection {
  background-color: #1e40af;
}

/* Estilos para listas */
.content-editor ul li {
  list-style-type: disc;
}

.content-editor ol li {
  list-style-type: decimal;
}

/* Espaciado entre elementos */
.content-editor > *:not(:last-child) {
  margin-bottom: 1rem;
}

/* Evitar márgenes dobles */
.content-editor > *:first-child {
  margin-top: 0;
}

.content-editor > *:last-child {
  margin-bottom: 0;
}
