/**
 * AI Model Discovery Service
 * Automatically discovers and caches available models from OpenAI and Google Gemini
 */

export interface AIModel {
  id: string;
  name: string;
  provider: 'OPENAI' | 'GEMINI';
  status: 'active' | 'deprecated' | 'beta' | 'new';
  description?: string;
  maxTokens?: number;
  costPer1kTokens?: number;
  capabilities?: string[];
  releaseDate?: string;
  deprecationDate?: string;
}

export interface ModelDiscoveryResult {
  models: AIModel[];
  lastUpdated: string;
  source: 'api' | 'cache' | 'fallback';
  error?: string;
}

export interface ModelCache {
  openai: ModelDiscoveryResult;
  gemini: ModelDiscoveryResult;
  lastGlobalUpdate: string;
}

// Fallback models in case API discovery fails
const FALLBACK_OPENAI_MODELS: AIModel[] = [
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OPENAI',
    status: 'active',
    description: 'Fast and efficient model for most tasks',
    maxTokens: 4096,
    capabilities: ['text-generation', 'conversation']
  },
  {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'OPENAI',
    status: 'active',
    description: 'Most capable model for complex tasks',
    maxTokens: 8192,
    capabilities: ['text-generation', 'conversation', 'reasoning']
  },
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OPENAI',
    status: 'active',
    description: 'Faster GPT-4 with updated knowledge',
    maxTokens: 128000,
    capabilities: ['text-generation', 'conversation', 'reasoning']
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OPENAI',
    status: 'active',
    description: 'Omni-modal model with vision capabilities',
    maxTokens: 128000,
    capabilities: ['text-generation', 'conversation', 'vision', 'reasoning']
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OPENAI',
    status: 'active',
    description: 'Smaller, faster version of GPT-4o',
    maxTokens: 128000,
    capabilities: ['text-generation', 'conversation', 'vision']
  },
  {
    id: 'o1-preview',
    name: 'o1 Preview',
    provider: 'OPENAI',
    status: 'beta',
    description: 'Advanced reasoning model (preview)',
    maxTokens: 32768,
    capabilities: ['reasoning', 'problem-solving']
  },
  {
    id: 'o1-mini',
    name: 'o1 Mini',
    provider: 'OPENAI',
    status: 'beta',
    description: 'Smaller reasoning model',
    maxTokens: 65536,
    capabilities: ['reasoning', 'problem-solving']
  }
];

const FALLBACK_GEMINI_MODELS: AIModel[] = [
  {
    id: 'gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash (Experimental)',
    provider: 'GEMINI',
    status: 'beta',
    description: 'Latest experimental Gemini model',
    maxTokens: 1000000,
    capabilities: ['text-generation', 'conversation', 'multimodal']
  },
  {
    id: 'gemini-2.0-flash',
    name: 'Gemini 2.0 Flash',
    provider: 'GEMINI',
    status: 'new',
    description: 'Fast and efficient Gemini 2.0 model',
    maxTokens: 1000000,
    capabilities: ['text-generation', 'conversation', 'multimodal']
  },
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    provider: 'GEMINI',
    status: 'active',
    description: 'Most capable Gemini 1.5 model',
    maxTokens: 2000000,
    capabilities: ['text-generation', 'conversation', 'multimodal', 'long-context']
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    provider: 'GEMINI',
    status: 'active',
    description: 'Fast and efficient Gemini 1.5 model',
    maxTokens: 1000000,
    capabilities: ['text-generation', 'conversation', 'multimodal']
  },
  {
    id: 'gemini-1.5-flash-8b',
    name: 'Gemini 1.5 Flash 8B',
    provider: 'GEMINI',
    status: 'active',
    description: 'Smaller, faster Gemini 1.5 model',
    maxTokens: 1000000,
    capabilities: ['text-generation', 'conversation']
  }
];

/**
 * Discovers available OpenAI models
 */
export async function discoverOpenAIModels(apiKey?: string): Promise<ModelDiscoveryResult> {
  try {
    if (!apiKey) {
      apiKey = process.env.OPENAI_API_KEY;
    }

    if (!apiKey) {
      return {
        models: FALLBACK_OPENAI_MODELS,
        lastUpdated: new Date().toISOString(),
        source: 'fallback',
        error: 'No API key provided'
      };
    }

    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Filter and map OpenAI models to our format
    const models: AIModel[] = data.data
      .filter((model: any) => {
        // Only include GPT and o1 models
        return model.id.includes('gpt') || model.id.includes('o1');
      })
      .map((model: any) => {
        const aiModel: AIModel = {
          id: model.id,
          name: formatModelName(model.id),
          provider: 'OPENAI',
          status: determineModelStatus(model.id),
          description: getModelDescription(model.id),
          capabilities: getModelCapabilities(model.id)
        };

        // Add max tokens based on model
        if (model.id.includes('gpt-4o') || model.id.includes('gpt-4-turbo')) {
          aiModel.maxTokens = 128000;
        } else if (model.id.includes('gpt-4')) {
          aiModel.maxTokens = 8192;
        } else if (model.id.includes('gpt-3.5')) {
          aiModel.maxTokens = 4096;
        } else if (model.id.includes('o1')) {
          aiModel.maxTokens = model.id.includes('o1-mini') ? 65536 : 32768;
        }

        return aiModel;
      })
      .sort((a: any, b: any) => {
        // Sort by status priority and then by name
        const statusPriority: any = { 'new': 0, 'active': 1, 'beta': 2, 'deprecated': 3 };
        const aPriority = statusPriority[a.status] ?? 4;
        const bPriority = statusPriority[b.status] ?? 4;
        
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }
        
        return a.name.localeCompare(b.name);
      });

    return {
      models,
      lastUpdated: new Date().toISOString(),
      source: 'api'
    };

  } catch (error) {
    console.error('Error discovering OpenAI models:', error);
    
    return {
      models: FALLBACK_OPENAI_MODELS,
      lastUpdated: new Date().toISOString(),
      source: 'fallback',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Discovers available Gemini models
 */
export async function discoverGeminiModels(apiKey?: string): Promise<ModelDiscoveryResult> {
  try {
    if (!apiKey) {
      apiKey = process.env.GEMINI_API_KEY;
    }

    if (!apiKey) {
      return {
        models: FALLBACK_GEMINI_MODELS,
        lastUpdated: new Date().toISOString(),
        source: 'fallback',
        error: 'No API key provided'
      };
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`);

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Filter and map Gemini models to our format
    const models: AIModel[] = data.models
      .filter((model: any) => {
        // Only include generative models
        return model.name.includes('gemini') && 
               model.supportedGenerationMethods?.includes('generateContent');
      })
      .map((model: any) => {
        const modelId = model.name.replace('models/', '');
        
        const aiModel: AIModel = {
          id: modelId,
          name: formatModelName(modelId),
          provider: 'GEMINI',
          status: determineModelStatus(modelId),
          description: model.description || getModelDescription(modelId),
          capabilities: getModelCapabilities(modelId)
        };

        // Add max tokens based on model
        if (model.inputTokenLimit) {
          aiModel.maxTokens = model.inputTokenLimit;
        } else if (modelId.includes('1.5-pro')) {
          aiModel.maxTokens = 2000000;
        } else if (modelId.includes('1.5-flash') || modelId.includes('2.0')) {
          aiModel.maxTokens = 1000000;
        }

        return aiModel;
      })
      .sort((a: any, b: any) => {
        // Sort by status priority and then by name
        const statusPriority: any = { 'new': 0, 'active': 1, 'beta': 2, 'deprecated': 3 };
        const aPriority = statusPriority[a.status] ?? 4;
        const bPriority = statusPriority[b.status] ?? 4;
        
        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }
        
        return a.name.localeCompare(b.name);
      });

    return {
      models,
      lastUpdated: new Date().toISOString(),
      source: 'api'
    };

  } catch (error) {
    console.error('Error discovering Gemini models:', error);
    
    return {
      models: FALLBACK_GEMINI_MODELS,
      lastUpdated: new Date().toISOString(),
      source: 'fallback',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Helper functions
function formatModelName(modelId: string): string {
  return modelId
    .replace(/^gpt-/, 'GPT-')
    .replace(/^gemini-/, 'Gemini ')
    .replace(/^o1-/, 'o1 ')
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}

function determineModelStatus(modelId: string): AIModel['status'] {
  if (modelId.includes('preview') || modelId.includes('exp') || modelId.includes('beta')) {
    return 'beta';
  }
  
  if (modelId.includes('2.0') || modelId.includes('o1')) {
    return 'new';
  }
  
  if (modelId.includes('gpt-3.5') || modelId.includes('gemini-pro') || modelId.includes('text-')) {
    return 'deprecated';
  }
  
  return 'active';
}

function getModelDescription(modelId: string): string {
  const descriptions: Record<string, string> = {
    'gpt-4o': 'Omni-modal model with vision capabilities',
    'gpt-4o-mini': 'Smaller, faster version of GPT-4o',
    'gpt-4-turbo': 'Faster GPT-4 with updated knowledge',
    'gpt-4': 'Most capable model for complex tasks',
    'gpt-3.5-turbo': 'Fast and efficient model for most tasks',
    'o1-preview': 'Advanced reasoning model (preview)',
    'o1-mini': 'Smaller reasoning model',
    'gemini-2.0-flash': 'Fast and efficient Gemini 2.0 model',
    'gemini-1.5-pro': 'Most capable Gemini 1.5 model',
    'gemini-1.5-flash': 'Fast and efficient Gemini 1.5 model'
  };
  
  return descriptions[modelId] || 'AI language model';
}

function getModelCapabilities(modelId: string): string[] {
  const capabilities: string[] = ['text-generation', 'conversation'];
  
  if (modelId.includes('gpt-4o') || modelId.includes('gemini')) {
    capabilities.push('multimodal');
  }
  
  if (modelId.includes('gpt-4') || modelId.includes('o1') || modelId.includes('gemini-1.5-pro')) {
    capabilities.push('reasoning');
  }
  
  if (modelId.includes('o1')) {
    capabilities.push('problem-solving');
  }
  
  if (modelId.includes('gpt-4o')) {
    capabilities.push('vision');
  }
  
  if (modelId.includes('gemini-1.5-pro')) {
    capabilities.push('long-context');
  }
  
  return capabilities;
}
