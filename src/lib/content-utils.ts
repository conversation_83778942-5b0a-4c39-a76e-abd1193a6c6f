/**
 * Utilidades para procesamiento de contenido de noticias
 */

/**
 * Limpia el contenido removiendo cualquier texto de crédito de imagen
 * 
 * Esta función elimina completamente cualquier referencia a créditos de imagen
 * que puedan aparecer concatenados al final del contenido o en cualquier parte del texto.
 * 
 * @param content - El contenido original de la noticia
 * @returns El contenido limpio sin créditos de imagen
 */
export function cleanContentFromImageCredits(content: string): string {
  if (!content) return content;
  
  // Patrones para detectar y remover créditos de imagen
  const creditPatterns = [
    // Patrón principal: "Crédito: [nombre]" al final del texto (sin punto final)
    /Crédito:\s*[^.\n]*$/gi,
    // Variaciones comunes al final
    /Créditos:\s*[^.\n]*$/gi,
    /Foto:\s*[^.\n]*$/gi,
    /Fotografía:\s*[^.\n]*$/gi,
    /Imagen:\s*[^.\n]*$/gi,
    // Patrones con separadores al final
    /\s*-\s*Crédito:\s*[^.\n]*$/gi,
    /\s*\|\s*Crédito:\s*[^.\n]*$/gi,
    /\s*\/\s*Crédito:\s*[^.\n]*$/gi,
    // Patrones entre paréntesis
    /\s*\(Crédito:\s*[^)]*\)\s*$/gi,
    /\s*\(Foto:\s*[^)]*\)\s*$/gi,
    /\s*\(Fotografía:\s*[^)]*\)\s*$/gi,
    // Patrones en cualquier parte del texto (más agresivos)
    /\bCrédito:\s*[A-Za-z\s]+(?=\s|$)/gi,
    /\bFoto:\s*[A-Za-z\s]+(?=\s|$)/gi,
    // Patrones específicos que pueden aparecer concatenados
    /([a-z])Crédito:/gi, // Detecta texto pegado como "binomiosCrédito:"
    // Patrones adicionales para casos específicos
    /\s*\.\s*Crédito:\s*[^.\n]*$/gi, // Con punto antes del crédito
    /\s*,\s*Crédito:\s*[^.\n]*$/gi, // Con coma antes del crédito
    // Patrones para créditos en líneas separadas
    /\n\s*Crédito:\s*[^\n]*$/gi,
    /\n\s*Foto:\s*[^\n]*$/gi,
  ];
  
  let cleanedContent = content;
  const originalLength = content.length;
  let totalRemoved = '';
  
  // Aplicar cada patrón para limpiar el contenido
  creditPatterns.forEach((pattern, index) => {
    const beforeClean = cleanedContent;
    cleanedContent = cleanedContent.replace(pattern, (match, p1) => {
      // Si es el patrón de texto pegado, mantener la letra anterior
      if (pattern.source.includes('([a-z])Crédito:')) {
        totalRemoved += match.substring(1); // Guardar lo que se removió (sin la letra)
        return p1 || '';
      }
      totalRemoved += match;
      return '';
    });
    
    if (beforeClean !== cleanedContent) {
      console.log(`🧹 Patrón ${index + 1} aplicado - removido texto de crédito`);
    }
  });
  
  // Limpiar espacios en blanco extra y saltos de línea múltiples
  cleanedContent = cleanedContent
    .replace(/\s+$/g, '') // Espacios al final
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Múltiples saltos de línea
    .replace(/\s*-\s*$/g, '') // Guiones sueltos al final
    .replace(/\s*\|\s*$/g, '') // Pipes sueltos al final
    .replace(/\s*\(\s*$/g, '') // Paréntesis abiertos sueltos al final
    .replace(/\s*,\s*$/g, '') // Comas sueltas al final
    .trim();
  
  // Log para debugging si se removió algo
  if (cleanedContent !== content) {
    console.log('🧹 CONTENIDO LIMPIADO - Créditos de imagen removidos');
    console.log('📝 Longitud original:', originalLength);
    console.log('✨ Longitud limpia:', cleanedContent.length);
    console.log('🗑️ Texto removido:', totalRemoved);
    console.log('📄 Contenido final:', cleanedContent.substring(Math.max(0, cleanedContent.length - 100)));
  }
  
  return cleanedContent;
}

/**
 * Valida que el contenido no contenga créditos de imagen
 * 
 * @param content - El contenido a validar
 * @returns true si el contenido está limpio, false si contiene créditos
 */
export function validateContentIsClean(content: string): boolean {
  if (!content) return true;
  
  const creditIndicators = [
    /Crédito:/gi,
    /Créditos:/gi,
    /Foto:\s*[A-Za-z]/gi,
    /Fotografía:\s*[A-Za-z]/gi,
  ];
  
  return !creditIndicators.some(pattern => pattern.test(content));
}

/**
 * Extrae información de crédito del contenido sin removerla
 *
 * @param content - El contenido original
 * @returns Información del crédito encontrado o null
 */
export function extractImageCredit(content: string): { credit: string; position: number } | null {
  if (!content) return null;

  const creditPattern = /Crédito:\s*([^.\n]+)/gi;
  const match = creditPattern.exec(content);

  if (match) {
    return {
      credit: match[1].trim(),
      position: match.index
    };
  }

  return null;
}

/**
 * Limpia el contenido HTML y lo convierte a texto plano
 *
 * Esta función procesa contenido que puede venir del editor WYSIWYG
 * y lo convierte a texto plano adecuado para publicación externa.
 *
 * @param content - El contenido que puede contener HTML
 * @returns El contenido limpio en texto plano
 */
export function cleanHtmlContent(content: string): string {
  if (!content) return content;

  let cleanedContent = content;

  // Convertir elementos HTML a texto plano
  cleanedContent = cleanedContent
    // Convertir párrafos a saltos de línea
    .replace(/<\/p>\s*<p[^>]*>/gi, '\n\n')
    .replace(/<p[^>]*>/gi, '')
    .replace(/<\/p>/gi, '\n')

    // Convertir saltos de línea
    .replace(/<br\s*\/?>/gi, '\n')

    // Convertir listas
    .replace(/<\/li>\s*<li[^>]*>/gi, '\n• ')
    .replace(/<li[^>]*>/gi, '• ')
    .replace(/<\/li>/gi, '')
    .replace(/<\/?[uo]l[^>]*>/gi, '\n')

    // Mantener formato básico
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '$1')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '$1')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '$1')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '$1')

    // Remover otros tags HTML
    .replace(/<[^>]+>/g, '')

    // Decodificar entidades HTML
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")

    // Limpiar espacios y saltos de línea excesivos
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .replace(/[ \t]+/g, ' ')
    .trim();

  return cleanedContent;
}

/**
 * Valida y corrige HTML malformado
 *
 * @param content - El contenido HTML a validar
 * @returns El contenido HTML corregido
 */
export function validateAndFixHtml(content: string): string {
  if (!content) return content;

  let fixedContent = content;

  try {
    // Paso 1: Limpiar caracteres problemáticos
    fixedContent = fixedContent
      // Remover caracteres de control excepto saltos de línea y tabs
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Normalizar espacios en blanco
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n');

    // Paso 2: Corregir estructura básica de HTML
    fixedContent = fixedContent
      // Asegurar que no haya tags anidados incorrectamente
      .replace(/<p([^>]*)>\s*<p([^>]*)>/gi, '<p$1>')
      .replace(/<\/p>\s*<\/p>/gi, '</p>')

      // Corregir párrafos sin cierre
      .replace(/<p([^>]*)>([^<]*?)(?=<p[^>]*>|$)/gi, (_match, attrs, content) => {
        if (content.trim()) {
          return `<p${attrs}>${content.trim()}</p>`;
        }
        return '';
      })

      // Remover párrafos vacíos
      .replace(/<p[^>]*>\s*<\/p>/gi, '')

      // Corregir strong tags
      .replace(/<strong([^>]*)>([^<]*?)(?=<(?!\/strong)|$)/gi, '<strong$1>$2</strong>')
      .replace(/<\/strong>\s*<strong[^>]*>/gi, ' ')

      // Corregir em tags
      .replace(/<em([^>]*)>([^<]*?)(?=<(?!\/em)|$)/gi, '<em$1>$2</em>')
      .replace(/<\/em>\s*<em[^>]*>/gi, ' ')

      // Corregir br tags
      .replace(/<br([^>]*)>/gi, '<br$1/>')

      // Limpiar espacios múltiples pero preservar estructura
      .replace(/[ \t]+/g, ' ')
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .trim();

    // Paso 3: Validación final - si hay tags sin cerrar, convertir a texto plano
    const openTags = (fixedContent.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (fixedContent.match(/<\/[^>]*>/g) || []).length;

    if (Math.abs(openTags - closeTags) > 2) {
      console.warn('⚠️ HTML muy malformado, convirtiendo a texto plano');
      return cleanHtmlContent(fixedContent);
    }

    return fixedContent;

  } catch (error) {
    console.error('❌ Error validando HTML, fallback a texto plano:', error);
    return cleanHtmlContent(content);
  }
}

/**
 * Procesa el contenido para publicación externa manteniendo HTML completo
 *
 * Elimina créditos de imagen pero mantiene TODO el formato HTML
 * incluyendo <strong>, <p>, <em>, etc.
 *
 * @param content - El contenido original con HTML
 * @returns El contenido procesado con HTML completo pero sin créditos
 */
export function processContentForExternalPublication(content: string): string {
  if (!content) return content;

  console.log('🔧 DEBUG - Contenido original antes de procesar:', content.substring(0, 200));

  // Paso 1: Remover créditos de imagen
  let processedContent = cleanContentFromImageCredits(content);
  console.log('🧹 DEBUG - Después de limpiar créditos:', processedContent.substring(0, 200));

  // Paso 2: Validar y corregir HTML malformado
  processedContent = validateAndFixHtml(processedContent);
  console.log('✅ DEBUG - Después de validar HTML:', processedContent.substring(0, 200));

  // Paso 3: Mínima limpieza final
  processedContent = processedContent.trim();

  console.log('🎯 DEBUG - Contenido final para publicación:', processedContent.substring(0, 200));

  return processedContent;
}

/**
 * Procesa el contenido para publicación externa sin HTML (texto plano)
 *
 * Combina la limpieza de HTML y la eliminación de créditos de imagen
 *
 * @param content - El contenido original
 * @returns El contenido procesado en texto plano
 */
export function processContentForExternalPublicationPlainText(content: string): string {
  if (!content) return content;

  // Primero limpiar HTML
  let processedContent = cleanHtmlContent(content);

  // Luego remover créditos de imagen
  processedContent = cleanContentFromImageCredits(processedContent);

  return processedContent;
}
