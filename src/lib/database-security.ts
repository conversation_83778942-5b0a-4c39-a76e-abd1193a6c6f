/**
 * Configuración de seguridad para la base de datos
 * Incluye validaciones, sanitización y configuraciones seguras
 */

import { PrismaClient } from '@prisma/client';

// Configuración de conexión segura para Prisma
export const getDatabaseConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    // Configuración de logging segura
    log: isProduction
      ? ['error' as const] // Solo errores en producción
      : ['query' as const, 'info' as const, 'warn' as const, 'error' as const], // Más detalle en desarrollo
    
    // Configuración de errores
    errorFormat: isProduction ? 'minimal' as const : 'pretty' as const,
    
    // Configuración de conexión
    datasources: {
      db: {
        url: process.env.DATABASE_URL
      }
    }
  };
};

// Validaciones de seguridad para queries
export class DatabaseSecurity {
  
  /**
   * Sanitiza strings para prevenir inyección SQL
   */
  static sanitizeString(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    // Remover caracteres peligrosos
    return input
      .replace(/[<>]/g, '') // Remover < y >
      .replace(/['";]/g, '') // Remover comillas y punto y coma
      .replace(/--/g, '') // Remover comentarios SQL
      .replace(/\/\*/g, '') // Remover comentarios de bloque
      .replace(/\*\//g, '')
      .trim();
  }
  
  /**
   * Valida que un ID sea un número entero positivo
   */
  static validateId(id: any): number | null {
    const numId = parseInt(id);
    if (isNaN(numId) || numId <= 0) {
      return null;
    }
    return numId;
  }
  
  /**
   * Valida formato de email
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  /**
   * Valida que un enum sea válido
   */
  static validateEnum<T>(value: any, enumObject: T): value is T[keyof T] {
    return Object.values(enumObject as any).includes(value);
  }
  
  /**
   * Limita el tamaño de resultados para prevenir DoS
   */
  static limitResults(limit?: number): number {
    const maxLimit = 1000; // Límite máximo
    const defaultLimit = 50; // Límite por defecto
    
    if (!limit || limit <= 0) {
      return defaultLimit;
    }
    
    return Math.min(limit, maxLimit);
  }
  
  /**
   * Valida parámetros de paginación
   */
  static validatePagination(page?: number, limit?: number) {
    const validPage = Math.max(1, page || 1);
    const validLimit = this.limitResults(limit);
    const skip = (validPage - 1) * validLimit;
    
    return {
      skip,
      take: validLimit,
      page: validPage
    };
  }
  
  /**
   * Valida y sanitiza parámetros de búsqueda
   */
  static validateSearchParams(search?: string) {
    if (!search || typeof search !== 'string') {
      return undefined;
    }
    
    const sanitized = this.sanitizeString(search);
    
    // Mínimo 2 caracteres para búsqueda
    if (sanitized.length < 2) {
      return undefined;
    }
    
    // Máximo 100 caracteres
    if (sanitized.length > 100) {
      return sanitized.substring(0, 100);
    }
    
    return sanitized;
  }
  
  /**
   * Valida parámetros de ordenamiento
   */
  static validateOrderBy(orderBy?: string, allowedFields: string[] = []) {
    if (!orderBy || !allowedFields.includes(orderBy)) {
      return 'createdAt'; // Campo por defecto
    }
    
    return orderBy;
  }
  
  /**
   * Valida dirección de ordenamiento
   */
  static validateOrderDirection(direction?: string): 'asc' | 'desc' {
    return direction === 'asc' ? 'asc' : 'desc';
  }
}

// Configuración de timeouts para queries
export const QUERY_TIMEOUTS = {
  short: 5000,   // 5 segundos para queries simples
  medium: 15000, // 15 segundos para queries complejas
  long: 30000    // 30 segundos para operaciones pesadas
};

// Límites de rate limiting para operaciones de base de datos
export const DB_RATE_LIMITS = {
  reads: {
    perMinute: 1000,
    perHour: 10000
  },
  writes: {
    perMinute: 100,
    perHour: 1000
  },
  deletes: {
    perMinute: 10,
    perHour: 100
  }
};

// Configuración de auditoría
export interface AuditLogData {
  action: string;
  entityType: string;
  entityId: string;
  userId: number;
  details?: any;
}

export class AuditLogger {
  static async log(prisma: PrismaClient, data: AuditLogData) {
    try {
      await prisma.auditLog.create({
        data: {
          action: DatabaseSecurity.sanitizeString(data.action),
          entityType: DatabaseSecurity.sanitizeString(data.entityType),
          entityId: DatabaseSecurity.sanitizeString(data.entityId),
          userId: data.userId,
          details: data.details || {}
        }
      });
    } catch (error) {
      // Log error pero no fallar la operación principal
      console.error('Error logging audit:', error);
    }
  }
}

// Configuración de backup y mantenimiento
export const MAINTENANCE_CONFIG = {
  // Limpiar logs de auditoría más antiguos que esto
  auditLogRetentionDays: 90,
  
  // Limpiar notificaciones leídas más antiguas que esto
  notificationRetentionDays: 30,
  
  // Archivar noticias más antiguas que esto
  newsArchiveDays: 365
};

// Validador de configuración de base de datos
export function validateDatabaseConfig() {
  const errors: string[] = [];
  
  if (!process.env.DATABASE_URL) {
    errors.push('DATABASE_URL environment variable is required');
  }
  
  // En producción, verificar que no sea SQLite
  if (process.env.NODE_ENV === 'production' &&
      process.env.DATABASE_URL?.includes('file:')) {
    // Durante el build, solo mostrar warning, no fallar
    if (process.env.NEXT_PHASE === 'phase-production-build') {
      console.warn('⚠️ Warning: SQLite is not recommended for production. Use PostgreSQL or MySQL.');
    } else {
      errors.push('SQLite is not recommended for production. Use PostgreSQL or MySQL.');
    }
  }

  if (errors.length > 0) {
    throw new Error(`Database configuration errors:\n${errors.join('\n')}`);
  }
  
  return true;
}
