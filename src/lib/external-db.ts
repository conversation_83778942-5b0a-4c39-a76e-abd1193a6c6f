/**
 * Configuración y conexión a la base de datos externa de noticias nacionales
 */

import { Pool } from 'pg';

// Configuración de la base de datos externa
const externalDbConfig = {
  host: 'noticia-infobae-n8n.sv1.diarios.live',
  port: 5932,
  user: 'postgres',
  password: 'd86f586a509c5b0e',
  database: 'noticias', // Base de datos correcta que contiene las tablas
  ssl: false, // Ajustar según la configuración del servidor
  max: 10, // Máximo número de conexiones en el pool
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// Pool de conexiones para la base de datos externa
let externalDbPool: Pool | null = null;

/**
 * Obtiene el pool de conexiones a la base de datos externa
 */
export function getExternalDbPool(): Pool {
  if (!externalDbPool) {
    externalDbPool = new Pool(externalDbConfig);
    
    // Manejar errores de conexión
    externalDbPool.on('error', (err) => {
      console.error('Error en el pool de conexiones externas:', err);
    });
  }
  
  return externalDbPool;
}

/**
 * Interfaz para las noticias de la base de datos externa (tabla noticias_original)
 */
export interface NoticiaExterna {
  id: number;
  original_title: string;
  original_description?: string;
  original_content?: string;
  original_author?: string;
  original_source?: string;
  original_url?: string;
  image_url?: string;
  publish_date?: Date;
  original_category?: string;
  category_id?: number;
  categories?: number[];
  created_at: Date;
}

/**
 * Interfaz mapeada para compatibilidad con el sistema existente
 */
export interface NoticiaExternaMapeada {
  id: number;
  titulo: string;
  contenido: string;
  resumen?: string;
  imagen_url?: string;
  fecha_publicacion?: Date;
  autor?: string;
  categoria?: string;
  url_original?: string;
  fuente?: string;
  created_at: Date;
}

/**
 * Interfaz para los filtros de búsqueda
 */
export interface FiltrosNoticiasExternas {
  busqueda?: string;
  fechaDesde?: Date;
  fechaHasta?: Date;
  categoria?: string;
  autor?: string;
  limite?: number;
  offset?: number;
}

/**
 * Mapea una noticia externa a la estructura esperada por el frontend
 */
export function mapearNoticiaExterna(noticia: NoticiaExterna): NoticiaExternaMapeada {
  return {
    id: noticia.id,
    titulo: noticia.original_title || 'Sin título',
    contenido: noticia.original_content || '',
    resumen: noticia.original_description,
    imagen_url: noticia.image_url,
    fecha_publicacion: noticia.publish_date,
    autor: noticia.original_author,
    categoria: noticia.original_category,
    url_original: noticia.original_url,
    created_at: noticia.created_at
  };
}

/**
 * Obtiene noticias de la base de datos externa con filtros y paginación
 */
export async function obtenerNoticiasExternas(
  filtros: FiltrosNoticiasExternas = {}
): Promise<{ noticias: NoticiaExternaMapeada[]; total: number }> {
  const pool = getExternalDbPool();
  
  try {
    // Construir la consulta SQL con filtros
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;
    
    if (filtros.busqueda) {
      whereClause += ` AND (original_title ILIKE $${paramIndex} OR original_content ILIKE $${paramIndex} OR original_description ILIKE $${paramIndex})`;
      params.push(`%${filtros.busqueda}%`);
      paramIndex++;
    }

    if (filtros.fechaDesde) {
      whereClause += ` AND publish_date >= $${paramIndex}`;
      params.push(filtros.fechaDesde);
      paramIndex++;
    }

    if (filtros.fechaHasta) {
      whereClause += ` AND publish_date <= $${paramIndex}`;
      params.push(filtros.fechaHasta);
      paramIndex++;
    }

    if (filtros.categoria) {
      whereClause += ` AND original_category = $${paramIndex}`;
      params.push(filtros.categoria);
      paramIndex++;
    }

    if (filtros.autor) {
      whereClause += ` AND original_author = $${paramIndex}`;
      params.push(filtros.autor);
      paramIndex++;
    }
    
    // Consulta para contar el total
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM noticias_original 
      ${whereClause}
    `;
    
    const countResult = await pool.query(countQuery, params);
    const total = parseInt(countResult.rows[0].total);
    
    // Consulta para obtener las noticias con paginación
    const limite = filtros.limite || 20;
    const offset = filtros.offset || 0;
    
    const noticiasQuery = `
      SELECT
        id,
        original_title,
        original_description,
        original_content,
        original_author,
        original_source,
        original_url,
        image_url,
        publish_date,
        original_category,
        category_id,
        categories,
        created_at
      FROM noticias_original
      ${whereClause}
      ORDER BY publish_date DESC, created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limite, offset);
    
    const noticiasResult = await pool.query(noticiasQuery, params);

    // Mapear las noticias a la estructura esperada
    const noticiasMapeadas = noticiasResult.rows.map(mapearNoticiaExterna);

    return {
      noticias: noticiasMapeadas,
      total
    };
    
  } catch (error) {
    console.error('Error al obtener noticias externas:', error);
    throw new Error('Error al conectar con la base de datos externa');
  }
}

/**
 * Obtiene una noticia específica por ID de la base de datos externa
 */
export async function obtenerNoticiaExternaPorId(id: number): Promise<NoticiaExternaMapeada | null> {
  const pool = getExternalDbPool();
  
  try {
    const query = `
      SELECT
        id,
        original_title,
        original_description,
        original_content,
        original_author,
        original_source,
        original_url,
        image_url,
        publish_date,
        original_category,
        category_id,
        categories,
        created_at
      FROM noticias_original
      WHERE id = $1
    `;
    
    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    // Mapear la noticia a la estructura esperada
    return mapearNoticiaExterna(result.rows[0]);
    
  } catch (error) {
    console.error('Error al obtener noticia externa por ID:', error);
    throw new Error('Error al conectar con la base de datos externa');
  }
}

/**
 * Prueba la conexión a la base de datos externa
 */
export async function probarConexionExterna(): Promise<boolean> {
  const pool = getExternalDbPool();
  
  try {
    const result = await pool.query('SELECT 1 as test');
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error al probar conexión externa:', error);
    return false;
  }
}

/**
 * Cierra el pool de conexiones externas
 */
export async function cerrarConexionExterna(): Promise<void> {
  if (externalDbPool) {
    await externalDbPool.end();
    externalDbPool = null;
  }
}
