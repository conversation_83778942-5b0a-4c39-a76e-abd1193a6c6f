/**
 * Sistema de programación automática de tareas
 * Ejecuta programaciones pendientes cada minuto
 */

let cronInterval: NodeJS.Timeout | null = null;

export function startCronScheduler() {
  // Solo ejecutar en el servidor
  if (typeof window !== 'undefined') {
    return;
  }

  // Evitar múltiples instancias
  if (cronInterval) {
    console.log('🤖 CRON: Scheduler ya está ejecutándose');
    return;
  }

  console.log('🚀 CRON: Iniciando scheduler automático...');

  // Ejecutar cada minuto (60000 ms)
  cronInterval = setInterval(async () => {
    try {
      await ejecutarProgramacionesPendientes();
    } catch (error) {
      console.error('❌ CRON: Error en scheduler:', error);
    }
  }, 60000); // 1 minuto

  // Ejecutar inmediatamente al iniciar
  setTimeout(() => {
    ejecutarProgramacionesPendientes().catch(error => {
      console.error('❌ CRON: Error en ejecución inicial:', error);
    });
  }, 5000); // Esperar 5 segundos después del inicio

  console.log('✅ CRON: Scheduler iniciado exitosamente');
}

export function stopCronScheduler() {
  if (cronInterval) {
    clearInterval(cronInterval);
    cronInterval = null;
    console.log('🛑 CRON: Scheduler detenido');
  }
}

async function ejecutarProgramacionesPendientes() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3018';
    const cronSecret = process.env.CRON_SECRET || 'dev-secret-key';

    console.log('🔄 CRON: Verificando programaciones pendientes...');

    const response = await fetch(`${baseUrl}/api/cron/ejecutar-programaciones`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${cronSecret}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.ejecutadas > 0) {
      console.log(`🎉 CRON: ${result.ejecutadas} programaciones ejecutadas exitosamente`);
    } else {
      console.log('📭 CRON: No hay programaciones pendientes');
    }

    return result;

  } catch (error) {
    console.error('❌ CRON: Error ejecutando programaciones:', error);
    throw error;
  }
}

// Función para verificar el estado sin ejecutar
export async function verificarEstadoProgramaciones() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3018';

    const response = await fetch(`${baseUrl}/api/cron/ejecutar-programaciones`, {
      method: 'GET'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();

  } catch (error) {
    console.error('❌ CRON: Error verificando estado:', error);
    throw error;
  }
}

// Función para ejecutar manualmente (para testing)
export async function ejecutarManualmente() {
  console.log('🔧 CRON: Ejecución manual solicitada');
  return await ejecutarProgramacionesPendientes();
}
