import { prisma } from '@/lib/prisma';
import { createExternalPublicationService, ExternalPublicationService } from '@/lib/external-publication-service';
import { EstadoPublicacion } from '@prisma/client';
import { cleanContentFromImageCredits, processContentForExternalPublication } from '@/lib/content-utils';

interface PublicationPayload {
  diarioExternoId: number;
  versionId?: number;
}

interface PublicationResult {
  success: boolean;
  urlPublicacion?: string;
  error?: string;
}

export async function executeExternalPublication(
  noticiaId: number,
  payload: PublicationPayload
): Promise<PublicationResult> {
  console.log('🤖 CRON: Ejecutando publicación externa directa');
  console.log('Payload:', payload);

  try {
    const { diarioExternoId, versionId } = payload;

    // Validaciones básicas
    if (!diarioExternoId) {
      return { success: false, error: 'diarioExternoId es requerido' };
    }

    // Obtener la noticia
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      include: {
        categoria: true,
        user: true
      }
    });

    if (!noticia) {
      return { success: false, error: 'Noticia no encontrada' };
    }

    // Obtener el diario externo
    const diarioExterno = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diarioExterno) {
      return { success: false, error: 'Diario externo no encontrado' };
    }

    if (!diarioExterno.activo) {
      return { success: false, error: 'El diario externo no está activo' };
    }

    // Obtener la versión si se especifica
    let version = null;
    if (versionId) {
      version = await prisma.versionNoticia.findFirst({
        where: {
          id: versionId,
          noticiaId: noticiaId
        },
        include: {
          diario: true
        }
      });

      if (!version) {
        return { success: false, error: 'Versión no encontrada o no pertenece a esta noticia' };
      }
    }

    // Verificar si ya existe una publicación externa para esta combinación
    // Nota: Por ahora omitimos la verificación de versionId ya que el campo no existe en el esquema actual
    const publicacionExistente = await prisma.publicacionExterna.findFirst({
      where: {
        noticiaId: noticiaId,
        diarioExternoId: diarioExternoId
      }
    });

    // Solo verificamos duplicados para la noticia original (sin versión)
    if (publicacionExistente && !versionId) {
      return { success: false, error: 'Ya existe una publicación externa para esta noticia' };
    }

    // Crear el servicio de publicación externa
    const publicationService: ExternalPublicationService = createExternalPublicationService({
      ...diarioExterno,
      configuracion: diarioExterno.configuracion || undefined,
      descripcion: diarioExterno.descripcion || undefined
    });

    // Determinar qué contenido usar
    const contenidoParaPublicar = version || noticia;
    const tituloFinal = contenidoParaPublicar.titulo;
    const volantaFinal = contenidoParaPublicar.volanta;
    const contenidoRaw = contenidoParaPublicar.contenido;
    const resumenFinal = contenidoParaPublicar.resumen;
    const imagenUrl = contenidoParaPublicar.imagenUrl;

    // Procesar el contenido completo (HTML + créditos de imagen)
    const contenidoFinal = processContentForExternalPublication(contenidoRaw || '');

    // Crear header: usar volanta si existe, sino el título
    const headerFinal = volantaFinal || tituloFinal;

    console.log('📝 Contenido a publicar:', {
      titulo: tituloFinal,
      volanta: volantaFinal,
      header: headerFinal,
      tieneContenido: !!contenidoFinal,
      tieneResumen: !!resumenFinal,
      tieneImagen: !!imagenUrl,
      esVersion: !!version
    });

    // Crear registro de publicación externa
    const publicacionExterna = await prisma.publicacionExterna.create({
      data: {
        noticiaId: noticiaId,
        diarioExternoId: diarioExternoId,
        estado: EstadoPublicacion.PENDIENTE,
        metadatos: JSON.stringify({
          tituloOriginal: tituloFinal,
          fechaCreacion: new Date().toISOString(),
          esVersion: !!version,
          versionId: versionId || null, // Guardamos el versionId en metadatos por ahora
          versionDiario: version?.diario?.nombre || null
        })
      }
    });

    console.log('📋 Publicación externa creada:', publicacionExterna.id);

    // Subir imagen si existe
    let imagenExternaId = null;
    if (imagenUrl) {
      try {
        console.log('📸 Subiendo imagen...');

        // Descargar imagen desde URL
        const imageDownload = await publicationService.downloadImage(imagenUrl);
        if (!imageDownload.success) {
          console.log('⚠️ Error descargando imagen:', imageDownload.error);
        } else {
          // Preparar datos de imagen
          const imageRequest = {
            title: headerFinal,
            summary: headerFinal, // Usar header completo como summary
            date: ExternalPublicationService.formatDateForAPI(new Date()).split(' ')[0], // Formato YYYY-MM-DD en zona horaria Argentina
            category_id: diarioExterno.categoriaImagenId || 1, // Usar categoría por defecto
            file: imageDownload.data!
          };

          const resultadoImagen = await publicationService.uploadImage(imageRequest);

          if (resultadoImagen.success && (resultadoImagen.data?.image_id || resultadoImagen.data?.id)) {
            const imageId = resultadoImagen.data.image_id || resultadoImagen.data.id;
            imagenExternaId = imageId ? parseInt(imageId.toString()) : null;
            console.log('✅ Imagen subida exitosamente:', imagenExternaId);

            // Actualizar el registro con el ID de la imagen
            await prisma.publicacionExterna.update({
              where: { id: publicacionExterna.id },
              data: { imagenExternaId: imagenExternaId }
            });
          } else {
            console.log('⚠️ No se pudo subir la imagen:', resultadoImagen.error);
          }
        }
      } catch (error) {
        console.error('❌ Error subiendo imagen:', error);
      }
    }

    // Obtener mapeo de categoría
    let categoriaExternaId = null;
    if (noticia.categoriaId) {
      const mapeoCategoria = await prisma.categoriaMapeo.findFirst({
        where: {
          diarioExternoId: diarioExternoId,
          categoriaLocalId: noticia.categoriaId
        }
      });

      if (mapeoCategoria) {
        categoriaExternaId = mapeoCategoria.categoriaExternaId;
        console.log('📂 Categoría mapeada:', categoriaExternaId);
      } else {
        console.log('⚠️ No se encontró mapeo de categoría para:', noticia.categoriaId);
      }
    }

    // Publicar artículo
    console.log('📰 Publicando artículo...');
    const resultadoArticulo = await publicationService.publishArticle({
      header: headerFinal,
      created_at: ExternalPublicationService.formatDateForAPI(new Date()), // yyyy-mm-dd hh:mm:ss en zona horaria Argentina
      deferred_publication: false,
      publish: true,
      title: tituloFinal,
      summary: resumenFinal || '',
      content: contenidoFinal || '',
      images: imagenExternaId ? [imagenExternaId] : [],
      categories: categoriaExternaId ? [categoriaExternaId] : []
    });

    if (resultadoArticulo.success && ((resultadoArticulo.data as any)?.article_id || resultadoArticulo.data?.id)) {
      const articuloId = (resultadoArticulo.data as any).article_id || resultadoArticulo.data?.id;
      console.log('✅ Artículo publicado exitosamente:', articuloId);

      // Actualizar el registro con el resultado exitoso
      const metadatosExistentes = publicacionExterna.metadatos ? JSON.parse(publicacionExterna.metadatos) : {};
      const publicacionActualizada = await prisma.publicacionExterna.update({
        where: { id: publicacionExterna.id },
        data: {
          articuloExternoId: parseInt(articuloId),
          urlPublicacion: resultadoArticulo.data?.url || null,
          estado: EstadoPublicacion.EXITOSO,
          metadatos: JSON.stringify({
            ...metadatosExistentes,
            articuloId: articuloId,
            urlPublicacion: resultadoArticulo.data?.url,
            fechaPublicacion: new Date().toISOString(),
            respuestaCompleta: resultadoArticulo.data
          })
        }
      });

      // 🚀 ACTUALIZAR ESTADO DE LA NOTICIA CUANDO SE PUBLICA EXITOSAMENTE
      console.log(`📰 Actualizando estado de noticia ${noticiaId} a PUBLICADA`);

      // Solo actualizar si la noticia está en BORRADOR, EN_REVISION o APROBADA
      const estadosPermitidos = ['BORRADOR', 'EN_REVISION', 'APROBADA'];
      if (estadosPermitidos.includes(noticia.estado)) {
        await prisma.noticia.update({
          where: { id: noticiaId },
          data: {
            estado: 'PUBLICADA',
            fechaPublicacion: new Date(),
            publicada: true
          }
        });
        console.log(`✅ Estado de noticia ${noticiaId} actualizado a PUBLICADA`);
      } else {
        console.log(`⚠️ Noticia ${noticiaId} tiene estado ${noticia.estado}, no se actualiza automáticamente`);
      }

      console.log('🎉 Publicación externa completada exitosamente');

      return {
        success: true,
        urlPublicacion: resultadoArticulo.data?.url
      };
    } else {
      console.log('❌ Error publicando artículo:', resultadoArticulo.error);
      
      // Actualizar el registro con el error
      const metadatosExistentes = publicacionExterna.metadatos ? JSON.parse(publicacionExterna.metadatos) : {};
      await prisma.publicacionExterna.update({
        where: { id: publicacionExterna.id },
        data: {
          estado: EstadoPublicacion.ERROR_ARTICULO,
          errorMensaje: resultadoArticulo.error || 'Error desconocido al publicar artículo',
          metadatos: JSON.stringify({
            ...metadatosExistentes,
            error: resultadoArticulo.error,
            fechaError: new Date().toISOString()
          })
        }
      });

      return {
        success: false,
        error: resultadoArticulo.error || 'Error al publicar artículo'
      };
    }

  } catch (error) {
    console.error('❌ Error en publicación externa:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}
