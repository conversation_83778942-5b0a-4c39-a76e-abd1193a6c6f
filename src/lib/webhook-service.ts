import { prisma } from '@/lib/prisma';

// Interfaz para los datos del webhook
export interface WebhookNoticiaData {
  titulo: string;
  contenido: string;
  periodista: string;
  volanta?: string;
  subtitulo?: string;
  resumen?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  categoria?: string;
  destacada?: boolean;
}

// Interfaz para el resultado del procesamiento
export interface WebhookProcessResult {
  success: boolean;
  noticia?: any;
  error?: string;
  message: string;
}

// Servicio para procesar noticias recibidas vía webhook
export class WebhookService {
  
  // Procesar noticia recibida desde WhatsApp
  static async processWebhookNoticia(data: WebhookNoticiaData): Promise<WebhookProcessResult> {
    try {
      console.log('🔄 Procesando noticia de webhook:', { 
        titulo: data.titulo, 
        periodista: data.periodista 
      });

      // 1. Validar datos requeridos
      const validation = this.validateRequiredFields(data);
      if (!validation.isValid) {
        return {
          success: false,
          error: 'Campos requeridos faltantes',
          message: `Faltan campos: ${validation.missingFields.join(', ')}`
        };
      }

      // 2. Obtener o crear usuario del sistema para webhooks
      const webhookUser = await this.getOrCreateWebhookUser();
      if (!webhookUser) {
        return {
          success: false,
          error: 'Error del sistema',
          message: 'No se pudo crear usuario del sistema para webhook'
        };
      }

      // 3. Resolver categoría
      const categoriaId = await this.resolveCategoriaId(data.categoria);

      // 4. Preparar datos de la noticia
      const noticiaData = this.prepareNoticiaData(data, webhookUser.id, categoriaId);

      // 5. Crear noticia en base de datos
      const noticia = await prisma.noticia.create({
        data: noticiaData,
        include: {
          categoria: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      console.log(`✅ Noticia procesada exitosamente: ID ${noticia.id}`);

      // 6. Crear notificación del sistema (opcional)
      await this.createSystemNotification(noticia);

      return {
        success: true,
        noticia: {
          id: noticia.id,
          titulo: noticia.titulo,
          periodista: noticia.periodista,
          estado: noticia.estado,
          categoria: noticia.categoria?.nombre || 'Sin categoría',
          createdAt: noticia.createdAt,
        },
        message: 'Noticia procesada exitosamente'
      };

    } catch (error) {
      console.error('❌ Error al procesar noticia de webhook:', error);
      return {
        success: false,
        error: 'Error interno del servidor',
        message: 'No se pudo procesar la noticia'
      };
    }
  }

  // Validar campos requeridos
  private static validateRequiredFields(data: WebhookNoticiaData): { isValid: boolean; missingFields: string[] } {
    const requiredFields = ['titulo', 'contenido', 'periodista'];
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      if (!data[field as keyof WebhookNoticiaData] || 
          String(data[field as keyof WebhookNoticiaData]).trim() === '') {
        missingFields.push(field);
      }
    }

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }

  // Obtener o crear usuario del sistema para webhooks
  private static async getOrCreateWebhookUser() {
    try {
      let webhookUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });

      if (!webhookUser) {
        console.log('📝 Creando usuario webhook del sistema...');
        const bcrypt = require('bcrypt');
        const hashedPassword = await bcrypt.hash('webhook-system-2025', 10);
        
        webhookUser = await prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'Sistema Webhook',
            password: hashedPassword,
            role: 'EDITOR',
            isActive: true,
          }
        });
      }

      return webhookUser;
    } catch (error) {
      console.error('❌ Error al obtener/crear usuario webhook:', error);
      return null;
    }
  }

  // Resolver ID de categoría por nombre o usar defecto
  private static async resolveCategoriaId(categoriaNombre?: string): Promise<number | null> {
    try {
      let categoriaId: number | null = null;
      
      // Buscar categoría por nombre si se proporciona
      if (categoriaNombre) {
        const categoria = await prisma.categoria.findFirst({
          where: { 
            nombre: {
              contains: categoriaNombre
            },
            isActive: true 
          }
        });
        categoriaId = categoria?.id || null;
      }

      // Si no se encontró categoría, usar la primera activa como defecto
      if (!categoriaId) {
        const categoriaDefecto = await prisma.categoria.findFirst({
          where: { isActive: true },
          orderBy: { orden: 'asc' }
        });
        categoriaId = categoriaDefecto?.id || null;
      }

      return categoriaId;
    } catch (error) {
      console.error('❌ Error al resolver categoría:', error);
      return null;
    }
  }

  // Preparar datos de la noticia para inserción
  private static prepareNoticiaData(data: WebhookNoticiaData, userId: number, categoriaId: number | null) {
    const webhookMetadata = {
      receivedAt: new Date().toISOString(),
      source: 'n8n',
      originalData: data,
      processedBy: 'webhook-service'
    };

    return {
      titulo: data.titulo,
      subtitulo: data.subtitulo || null,
      volanta: data.volanta || null,
      contenido: data.contenido,
      resumen: data.resumen || null,
      imagenUrl: data.imagenUrl || null,
      imagenAlt: data.imagenAlt || null,
      autor: data.autor || data.periodista,
      fuente: data.fuente || null,
      urlFuente: data.urlFuente || null,
      estado: 'EN_REVISION' as const, // Estado automático para revisión editorial
      destacada: data.destacada || false,
      publicada: false,
      
      // Campos específicos del webhook
      periodista: data.periodista,
      origen: 'WEBHOOK' as const,
      webhookData: JSON.stringify(webhookMetadata),
      
      // Relaciones
      categoriaId: categoriaId,
      userId: userId,
    };
  }

  // Crear notificación del sistema (placeholder para futuro sistema de notificaciones)
  private static async createSystemNotification(noticia: any) {
    try {
      // TODO: Implementar sistema de notificaciones
      // Por ahora solo logueamos
      console.log(`📢 Notificación: Nueva noticia recibida de ${noticia.periodista}: "${noticia.titulo}"`);
      
      // En el futuro, aquí se podría:
      // - Crear registro en tabla de notificaciones
      // - Enviar email a editores
      // - Crear evento en tiempo real para el dashboard
      
    } catch (error) {
      console.error('❌ Error al crear notificación del sistema:', error);
      // No fallar el proceso principal por error en notificaciones
    }
  }

  // Obtener estadísticas de noticias recibidas vía webhook
  static async getWebhookStats() {
    try {
      const stats = await prisma.noticia.groupBy({
        by: ['estado'],
        where: {
          origen: 'WEBHOOK'
        },
        _count: {
          id: true
        }
      });

      const total = await prisma.noticia.count({
        where: { origen: 'WEBHOOK' }
      });

      const recientes = await prisma.noticia.findMany({
        where: { origen: 'WEBHOOK' },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          titulo: true,
          periodista: true,
          estado: true,
          createdAt: true,
        }
      });

      return {
        total,
        porEstado: stats,
        recientes
      };
    } catch (error) {
      console.error('❌ Error al obtener estadísticas de webhook:', error);
      return null;
    }
  }
}
