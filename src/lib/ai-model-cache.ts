/**
 * AI Model Cache Service
 * Manages caching of discovered AI models with configurable refresh intervals
 */

import { prisma } from './prisma';
import { ModelCache, ModelDiscoveryResult, discoverOpenAIModels, discoverGeminiModels } from './ai-model-discovery';

// Cache configuration
const CACHE_DURATION_HOURS = 24; // Refresh models daily
const FORCE_REFRESH_THRESHOLD_HOURS = 168; // Force refresh after 1 week
const CACHE_KEY_PREFIX = 'ai_models_cache_';

export interface CacheConfig {
  refreshIntervalHours: number;
  forceRefreshThresholdHours: number;
  enableAutoRefresh: boolean;
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  refreshIntervalHours: CACHE_DURATION_HOURS,
  forceRefreshThresholdHours: FORCE_REFRESH_THRESHOLD_HOURS,
  enableAutoRefresh: true
};

/**
 * Get cached models or fetch fresh ones if cache is stale
 */
export async function getCachedModels(
  provider: 'OPENAI' | 'GEMINI' | 'ALL' = 'ALL',
  forceRefresh: boolean = false,
  apiKeys?: { openai?: string; gemini?: string }
): Promise<ModelDiscoveryResult | ModelCache> {
  try {
    const config = await getCacheConfig();
    
    if (provider === 'ALL') {
      return await getAllCachedModels(forceRefresh, config, apiKeys);
    } else {
      return await getProviderCachedModels(provider, forceRefresh, config, apiKeys);
    }
  } catch (error) {
    console.error('Error getting cached models:', error);
    throw error;
  }
}

/**
 * Get all cached models for both providers
 */
async function getAllCachedModels(
  forceRefresh: boolean,
  config: CacheConfig,
  apiKeys?: { openai?: string; gemini?: string }
): Promise<ModelCache> {
  const [openaiResult, geminiResult] = await Promise.all([
    getProviderCachedModels('OPENAI', forceRefresh, config, apiKeys),
    getProviderCachedModels('GEMINI', forceRefresh, config, apiKeys)
  ]);

  return {
    openai: openaiResult as ModelDiscoveryResult,
    gemini: geminiResult as ModelDiscoveryResult,
    lastGlobalUpdate: new Date().toISOString()
  };
}

/**
 * Get cached models for a specific provider
 */
async function getProviderCachedModels(
  provider: 'OPENAI' | 'GEMINI',
  forceRefresh: boolean,
  config: CacheConfig,
  apiKeys?: { openai?: string; gemini?: string }
): Promise<ModelDiscoveryResult> {
  const cacheKey = `${CACHE_KEY_PREFIX}${provider.toLowerCase()}`;
  
  try {
    // Try to get from database cache first
    if (!forceRefresh) {
      const cachedData = await getCacheFromDatabase(cacheKey);
      
      if (cachedData && !isCacheStale(cachedData.lastUpdated, config.refreshIntervalHours)) {
        console.log(`Using cached ${provider} models from database`);
        return {
          ...cachedData,
          source: 'cache'
        };
      }
    }

    // Cache is stale or force refresh requested - fetch fresh data
    console.log(`Fetching fresh ${provider} models from API`);
    
    let freshData: ModelDiscoveryResult;
    
    if (provider === 'OPENAI') {
      freshData = await discoverOpenAIModels(apiKeys?.openai);
    } else {
      freshData = await discoverGeminiModels(apiKeys?.gemini);
    }

    // Save to database cache
    await saveCacheToDatabase(cacheKey, freshData);
    
    return freshData;

  } catch (error) {
    console.error(`Error getting ${provider} models:`, error);
    
    // Try to return stale cache as fallback
    const staleCache = await getCacheFromDatabase(cacheKey);
    if (staleCache) {
      console.log(`Using stale ${provider} cache as fallback`);
      return {
        ...staleCache,
        source: 'cache',
        error: `Fresh data unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
    
    // If no cache available, return fallback models
    if (provider === 'OPENAI') {
      return await discoverOpenAIModels();
    } else {
      return await discoverGeminiModels();
    }
  }
}

/**
 * Get cache configuration from database or use defaults
 */
async function getCacheConfig(): Promise<CacheConfig> {
  try {
    // Try to get from database (you could store this in a settings table)
    // For now, return defaults
    return DEFAULT_CACHE_CONFIG;
  } catch (error) {
    console.error('Error getting cache config:', error);
    return DEFAULT_CACHE_CONFIG;
  }
}

/**
 * Check if cache is stale based on last update time
 */
function isCacheStale(lastUpdated: string, refreshIntervalHours: number): boolean {
  const lastUpdateTime = new Date(lastUpdated);
  const now = new Date();
  const hoursSinceUpdate = (now.getTime() - lastUpdateTime.getTime()) / (1000 * 60 * 60);
  
  return hoursSinceUpdate >= refreshIntervalHours;
}

/**
 * Get cached data from database using Prisma ORM
 */
async function getCacheFromDatabase(cacheKey: string): Promise<ModelDiscoveryResult | null> {
  try {
    const cacheEntry = await prisma.cacheEntry.findUnique({
      where: { key: cacheKey }
    });

    if (!cacheEntry) {
      return null;
    }

    // Prisma automatically handles JSON parsing
    const data = cacheEntry.data as unknown as ModelDiscoveryResult;

    return {
      ...data,
      lastUpdated: cacheEntry.createdAt.toISOString()
    };
  } catch (error) {
    console.error('Error getting cache from database:', error);
    return null;
  }
}

/**
 * Save cached data to database using Prisma ORM
 */
async function saveCacheToDatabase(cacheKey: string, data: ModelDiscoveryResult): Promise<void> {
  try {
    // Use Prisma's upsert to handle insert/update
    await prisma.cacheEntry.upsert({
      where: { key: cacheKey },
      update: {
        data: data as any, // Prisma handles JSON conversion automatically
        updatedAt: new Date()
      },
      create: {
        key: cacheKey,
        data: data as any, // Prisma handles JSON conversion automatically
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`Cache saved successfully for key: ${cacheKey}`);
  } catch (error) {
    console.error('Error saving cache to database:', error);
    throw error;
  }
}


/**
 * Clear cache for a specific provider or all providers using Prisma ORM
 */
export async function clearModelCache(provider?: 'OPENAI' | 'GEMINI'): Promise<void> {
  try {
    if (provider) {
      const cacheKey = `${CACHE_KEY_PREFIX}${provider.toLowerCase()}`;
      await prisma.cacheEntry.deleteMany({
        where: { key: cacheKey }
      });
    } else {
      await prisma.cacheEntry.deleteMany({
        where: {
          key: {
            startsWith: CACHE_KEY_PREFIX
          }
        }
      });
    }

    console.log(`Cache cleared for ${provider || 'all providers'}`);
  } catch (error) {
    console.error('Error clearing cache:', error);
    throw error;
  }
}

/**
 * Get cache statistics
 */
export async function getCacheStats(): Promise<{
  openaiCacheAge?: number;
  geminiCacheAge?: number;
  totalCacheEntries: number;
  lastRefresh?: string;
}> {
  try {
    // Get all cache entries for AI models
    const cacheEntries = await prisma.cacheEntry.findMany({
      where: {
        key: {
          startsWith: CACHE_KEY_PREFIX
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    const result: any = {
      totalCacheEntries: cacheEntries.length
    };

    const now = new Date();

    for (const entry of cacheEntries) {
      // Calculate age in hours
      const ageMs = now.getTime() - entry.updatedAt.getTime();
      const ageHours = ageMs / (1000 * 60 * 60);

      if (entry.key.includes('openai')) {
        result.openaiCacheAge = Math.round(ageHours * 100) / 100;
      } else if (entry.key.includes('gemini')) {
        result.geminiCacheAge = Math.round(ageHours * 100) / 100;
      }

      if (!result.lastRefresh || entry.updatedAt > new Date(result.lastRefresh)) {
        result.lastRefresh = entry.updatedAt.toISOString();
      }
    }

    return result;
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return { totalCacheEntries: 0 };
  }
}

/**
 * Background job to refresh model cache
 * This should be called by a cron job or similar scheduler
 */
export async function refreshModelCacheBackground(): Promise<void> {
  try {
    console.log('Starting background model cache refresh...');
    
    const config = await getCacheConfig();
    
    if (!config.enableAutoRefresh) {
      console.log('Auto refresh is disabled');
      return;
    }

    // Refresh both providers
    await Promise.all([
      getProviderCachedModels('OPENAI', false, config),
      getProviderCachedModels('GEMINI', false, config)
    ]);
    
    console.log('Background model cache refresh completed');
  } catch (error) {
    console.error('Error in background model cache refresh:', error);
  }
}
