import OpenAI from 'openai';
import { CaptionGenerationParams, GeneratedCaption, Platform } from './types';

export class CaptionGenerator {
  private openai: OpenAI | null = null;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (apiKey) {
      this.openai = new OpenAI({ apiKey });
    }
  }

  /**
   * Determines if a model uses the new max_completion_tokens parameter
   */
  private usesMaxCompletionTokens(model: string): boolean {
    // Models that require max_completion_tokens instead of max_tokens
    const newModels = [
      'gpt-4o',
      'gpt-4o-mini',
      'gpt-4o-2024',
      'gpt-5',        // GPT-5 series
      'o1-preview',
      'o1-mini',
      'chatgpt-4o-latest'
    ];

    // Also check for any model with "2024" or "2025" in the name (likely newer models)
    const hasNewYear = model.includes('2024') || model.includes('2025');

    return newModels.some(newModel => model.includes(newModel)) || hasNewYear;
  }

  /**
   * Determines if a model requires default temperature (1) and cannot use custom temperature
   */
  private requiresDefaultTemperature(model: string): boolean {
    // Models that only support temperature: 1 (default)
    const restrictedModels = [
      'gpt-5',        // GPT-5 series
      'o1-preview',   // o1 series
      'o1-mini'
    ];

    // Also check for any model with "2025" in the name (likely newer restricted models)
    const hasNewYear = model.includes('2025');

    return restrictedModels.some(restrictedModel => model.includes(restrictedModel)) || hasNewYear;
  }

  /**
   * Generar caption para una plataforma específica
   */
  async generateCaption(params: CaptionGenerationParams): Promise<GeneratedCaption> {
    console.log(`🤖 Generando caption para ${params.platform}:`, {
      title: params.title.substring(0, 50) + '...',
      platform: params.platform,
      tone: params.tone || 'professional'
    });

    try {
      if (!this.openai) {
        // Fallback sin IA
        return this.generateFallbackCaption(params);
      }

      const prompt = this.buildPrompt(params);
      
      const model = 'gpt-3.5-turbo';
      const maxTokens = params.platform === 'twitter' ? 150 : 300;

      // Prepare the request parameters based on the model
      const requestParams: any = {
        model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPrompt(params.platform)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
      };

      // Use the correct temperature based on the model
      if (!this.requiresDefaultTemperature(model)) {
        requestParams.temperature = 0.7;
      }

      // Use the correct token parameter based on the model
      if (this.usesMaxCompletionTokens(model)) {
        requestParams.max_completion_tokens = maxTokens;
      } else {
        requestParams.max_tokens = maxTokens;
      }

      const response = await this.openai.chat.completions.create(requestParams);

      const generatedText = response.choices[0]?.message?.content || '';
      const caption = this.processGeneratedCaption(generatedText, params);

      console.log(`✅ Caption generado para ${params.platform}:`, {
        length: caption.caption.length,
        hashtags: caption.hashtags.length
      });

      return caption;
    } catch (error) {
      console.error('❌ Error generando caption con IA:', error);
      // Fallback en caso de error
      return this.generateFallbackCaption(params);
    }
  }

  /**
   * Generar múltiples captions para diferentes plataformas
   */
  async generateMultipleCaptions(
    params: Omit<CaptionGenerationParams, 'platform'>,
    platforms: Platform[]
  ): Promise<Record<Platform, GeneratedCaption>> {
    console.log('🤖 Generando captions para múltiples plataformas:', platforms);

    const results: Record<string, GeneratedCaption> = {};

    for (const platform of platforms) {
      try {
        results[platform] = await this.generateCaption({
          ...params,
          platform
        });
      } catch (error) {
        console.error(`❌ Error generando caption para ${platform}:`, error);
        results[platform] = this.generateFallbackCaption({
          ...params,
          platform
        });
      }
    }

    return results as Record<Platform, GeneratedCaption>;
  }

  /**
   * Construir prompt para IA
   */
  private buildPrompt(params: CaptionGenerationParams): string {
    const { title, content, platform, url, tone = 'professional' } = params;
    
    // Extraer primeros párrafos del contenido
    const contentPreview = content
      .replace(/<[^>]*>/g, '') // Remover HTML
      .substring(0, 500)
      .trim();

    return `
Título de la noticia: "${title}"

Contenido (extracto): "${contentPreview}"

URL de la noticia: ${url}

Instrucciones:
- Crea un caption ${tone} para ${platform}
- ${platform === 'twitter' ? 'Máximo 280 caracteres' : 'Entre 100-300 caracteres'}
- Incluye call-to-action para leer la noticia completa
- ${params.includeHashtags !== false ? 'Incluye hashtags relevantes' : 'No incluyas hashtags'}
- Usa un tono ${tone}
- Haz que sea atractivo y genere engagement

Formato de respuesta:
CAPTION: [tu caption aquí]
HASHTAGS: [hashtags separados por espacios, máximo 5]
    `.trim();
  }

  /**
   * Obtener prompt del sistema para cada plataforma
   */
  private getSystemPrompt(platform: Platform): string {
    const prompts = {
      facebook: `Eres un experto en marketing de contenido para Facebook. Creas captions que generan engagement, son informativos y profesionales. Usas emojis moderadamente y siempre incluyes call-to-action claros.`,
      
      twitter: `Eres un experto en Twitter/X. Creas tweets concisos, impactantes y que generan conversación. Usas hashtags estratégicamente y aprovechas las tendencias. Máximo 280 caracteres.`,
      
      instagram: `Eres un experto en Instagram. Creas captions visuales, atractivos y que cuentan historias. Usas emojis creativamente y hashtags relevantes para alcance orgánico.`
    };

    return prompts[platform] || prompts.facebook;
  }

  /**
   * Procesar caption generado por IA
   */
  private processGeneratedCaption(
    generatedText: string, 
    params: CaptionGenerationParams
  ): GeneratedCaption {
    const lines = generatedText.split('\n').filter(line => line.trim());
    
    let caption = '';
    let hashtags: string[] = [];

    for (const line of lines) {
      if (line.startsWith('CAPTION:')) {
        caption = line.replace('CAPTION:', '').trim();
      } else if (line.startsWith('HASHTAGS:')) {
        const hashtagLine = line.replace('HASHTAGS:', '').trim();
        hashtags = hashtagLine
          .split(/\s+/)
          .filter(tag => tag.startsWith('#'))
          .slice(0, 5); // Máximo 5 hashtags
      }
    }

    // Si no se encontró formato específico, usar todo como caption
    if (!caption) {
      caption = generatedText.trim();
    }

    // Aplicar límites de longitud
    const maxLength = params.maxLength || (params.platform === 'twitter' ? 280 : 500);
    if (caption.length > maxLength) {
      caption = caption.substring(0, maxLength - 3) + '...';
    }

    return {
      platform: params.platform,
      caption,
      hashtags,
      length: caption.length,
      estimatedEngagement: this.estimateEngagement(caption, hashtags)
    };
  }

  /**
   * Generar caption de fallback sin IA
   */
  private generateFallbackCaption(params: CaptionGenerationParams): GeneratedCaption {
    const { title, platform, url } = params;
    
    const templates = {
      facebook: {
        caption: `📰 ${title}\n\n¡Lee la noticia completa en el enlace! 👆\n\n#Noticias #Actualidad`,
        hashtags: ['#Noticias', '#Actualidad', '#Información']
      },
      twitter: {
        caption: `📰 ${title}\n\nLee más: ${url}\n\n#Noticias #Breaking`,
        hashtags: ['#Noticias', '#Breaking', '#Actualidad']
      },
      instagram: {
        caption: `📰 ${title}\n\n¡Desliza para leer más! 👆\n\n#Noticias #Actualidad #Información`,
        hashtags: ['#Noticias', '#Actualidad', '#Información', '#Breaking']
      }
    };

    const template = templates[platform] || templates.facebook;
    
    return {
      platform,
      caption: template.caption,
      hashtags: template.hashtags,
      length: template.caption.length,
      estimatedEngagement: 'medium'
    };
  }

  /**
   * Estimar nivel de engagement del caption
   */
  private estimateEngagement(caption: string, hashtags: string[]): 'low' | 'medium' | 'high' {
    let score = 0;

    // Factores que aumentan engagement
    if (caption.includes('?')) score += 1; // Preguntas
    if (caption.includes('!')) score += 1; // Exclamaciones
    if (caption.match(/[📰🔥💥⚡🎯]/)) score += 1; // Emojis relevantes
    if (hashtags.length >= 3) score += 1; // Hashtags suficientes
    if (caption.length > 50 && caption.length < 200) score += 1; // Longitud óptima
    if (caption.toLowerCase().includes('lee')) score += 1; // Call to action

    if (score >= 4) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  /**
   * Optimizar caption para una plataforma específica
   */
  optimizeForPlatform(caption: string, platform: Platform): string {
    switch (platform) {
      case 'twitter':
        // Acortar para Twitter
        if (caption.length > 280) {
          return caption.substring(0, 277) + '...';
        }
        return caption;
        
      case 'facebook':
        // Agregar más contexto para Facebook
        if (caption.length < 100) {
          return caption + '\n\n¡Comparte tu opinión en los comentarios! 💬';
        }
        return caption;
        
      case 'instagram':
        // Agregar más emojis para Instagram
        return caption.replace(/\./g, ' ✨');
        
      default:
        return caption;
    }
  }
}
