// Investigador avanzado para upload-post API
export class UploadPostInvestigator {
  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.UPLOAD_POST_API_KEY || '';
  }

  async investigateAPI(): Promise<any> {
    console.log('🔍 [INVESTIGATOR] Iniciando investigación completa...');
    
    const results = {
      apiKeyInfo: this.analyzeAPIKey(),
      baseURLTests: await this.testBaseURLs(),
      authMethodTests: await this.testAuthMethods(),
      headerTests: await this.testHeaders(),
      recommendations: [] as string[]
    };

    results.recommendations = this.generateRecommendations(results);
    return results;
  }

  private analyzeAPIKey() {
    console.log('🔑 [INVESTIGATOR] Analizando API key...');
    
    if (!this.apiKey) {
      return { error: 'No API key provided' };
    }

    // Analizar JWT
    const parts = this.apiKey.split('.');
    let decoded = null;
    
    if (parts.length === 3) {
      try {
        // Decodificar header
        const header = JSON.parse(atob(parts[0]));
        // Decodificar payload
        const payload = JSON.parse(atob(parts[1]));
        
        decoded = { header, payload };
      } catch (e) {
        console.log('⚠️ [INVESTIGATOR] Error decodificando JWT:', e);
      }
    }

    return {
      length: this.apiKey.length,
      prefix: this.apiKey.substring(0, 20) + '...',
      isJWT: parts.length === 3,
      decoded,
      expirationDate: decoded?.payload?.exp ? new Date(decoded.payload.exp * 1000) : null,
      email: decoded?.payload?.email || null
    };
  }

  private async testBaseURLs() {
    console.log('🌐 [INVESTIGATOR] Probando URLs base...');
    
    const baseURLs = [
      'https://api.upload-post.com',
      'https://app.upload-post.com/api',
      'https://upload-post.com/api',
      'https://app.upload-post.com/api/v1',
      'https://api.upload-post.com/v1',
      'https://upload-post.com/api/v1'
    ];

    const results = [];

    for (const baseURL of baseURLs) {
      const url = `${baseURL}/api/uploadposts/users`;
      console.log(`🧪 [INVESTIGATOR] Probando: ${url}`);
      
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Apikey ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        });

        const contentType = response.headers.get('content-type');
        const responseText = await response.text();
        
        results.push({
          baseURL,
          url,
          status: response.status,
          statusText: response.statusText,
          contentType,
          isJson: contentType?.includes('application/json'),
          isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html'),
          responsePreview: responseText.substring(0, 100)
        });

      } catch (error) {
        results.push({
          baseURL,
          url,
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    return results;
  }

  private async testAuthMethods() {
    console.log('🔐 [INVESTIGATOR] Probando métodos de autenticación...');
    
    const authMethods = [
      { name: 'Apikey (Correcto)', headers: { 'Authorization': `Apikey ${this.apiKey}` } },
      { name: 'Bearer Token', headers: { 'Authorization': `Bearer ${this.apiKey}` } },
      { name: 'Token', headers: { 'Authorization': `Token ${this.apiKey}` } },
      { name: 'API-Key Header', headers: { 'X-API-Key': this.apiKey } },
      { name: 'API-Key Alt', headers: { 'API-Key': this.apiKey } },
      { name: 'Authorization Basic', headers: { 'Authorization': `Basic ${btoa(this.apiKey + ':')}` } }
    ];

    const results = [];
    const testURL = 'https://api.upload-post.com/api/uploadposts/users';

    for (const method of authMethods) {
      console.log(`🔑 [INVESTIGATOR] Probando: ${method.name}`);
      
      try {
        const response = await fetch(testURL, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...method.headers
          } as unknown as HeadersInit,
        });

        const contentType = response.headers.get('content-type');
        const responseText = await response.text();
        
        results.push({
          method: method.name,
          status: response.status,
          statusText: response.statusText,
          contentType,
          isJson: contentType?.includes('application/json'),
          isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html'),
          responsePreview: responseText.substring(0, 100)
        });

      } catch (error) {
        results.push({
          method: method.name,
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    return results;
  }

  private async testHeaders() {
    console.log('📋 [INVESTIGATOR] Probando diferentes headers...');
    
    const headerSets = [
      {
        name: 'Standard JSON',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      },
      {
        name: 'Accept JSON',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      },
      {
        name: 'User Agent',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'Upload-Post-Client/1.0'
        }
      },
      {
        name: 'CORS Headers',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    ];

    const results = [];
    const testURL = 'https://api.upload-post.com/api/uploadposts/users';

    for (const headerSet of headerSets) {
      console.log(`📋 [INVESTIGATOR] Probando: ${headerSet.name}`);
      
      try {
        const response = await fetch(testURL, {
          method: 'GET',
          headers: headerSet.headers as unknown as HeadersInit,
        });

        const contentType = response.headers.get('content-type');
        const responseText = await response.text();
        
        results.push({
          headerSet: headerSet.name,
          status: response.status,
          statusText: response.statusText,
          contentType,
          isJson: contentType?.includes('application/json'),
          isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html'),
          responsePreview: responseText.substring(0, 100)
        });

      } catch (error) {
        results.push({
          headerSet: headerSet.name,
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    return results;
  }

  private generateRecommendations(results: any): string[] {
    const recommendations = [];

    // Analizar API key
    if (results.apiKeyInfo.error) {
      recommendations.push('❌ Configura una API key válida');
    } else if (results.apiKeyInfo.expirationDate && results.apiKeyInfo.expirationDate < new Date()) {
      recommendations.push('❌ La API key ha expirado, genera una nueva');
    } else {
      recommendations.push('✅ API key válida y no expirada');
    }

    // Analizar URLs
    const jsonResponses = results.baseURLTests.filter((r: any) => r.isJson);
    if (jsonResponses.length > 0) {
      recommendations.push(`✅ URL que funciona: ${jsonResponses[0].baseURL}`);
    } else {
      recommendations.push('❌ Ninguna URL base devuelve JSON');
      recommendations.push('🔍 Verifica la documentación de upload-post.com');
    }

    // Analizar autenticación
    const workingAuth = results.authMethodTests.filter((r: any) => r.isJson);
    if (workingAuth.length > 0) {
      recommendations.push(`✅ Método de auth que funciona: ${workingAuth[0].method}`);
    } else {
      recommendations.push('❌ Ningún método de autenticación funciona');
      recommendations.push('🔍 Contacta soporte de upload-post.com');
    }

    // Si todo devuelve HTML
    const allHTML = results.baseURLTests.every((r: any) => r.isHtml);
    if (allHTML) {
      recommendations.push('🚨 PROBLEMA: Todos los endpoints devuelven HTML');
      recommendations.push('💡 Posibles causas:');
      recommendations.push('   - API no disponible públicamente');
      recommendations.push('   - Requiere autenticación web previa');
      recommendations.push('   - URL de API incorrecta');
      recommendations.push('   - Servicio temporalmente no disponible');
    }

    return recommendations;
  }
}
