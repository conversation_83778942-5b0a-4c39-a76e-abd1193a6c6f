import { 
  UploadPostResponse, 
  SchedulePostResponse, 
  UploadPostProfile, 
  UploadStatus, 
  ScheduledPost 
} from './types';

export class UploadPostClient {
  private apiKey: string;
  private baseUrl = 'https://api.upload-post.com';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.UPLOAD_POST_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('Upload-Post API key is required');
    }
  }

  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Apikey ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload-Post API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Publicar inmediatamente en redes sociales
   */
  async publishPost(params: {
    text: string;
    url?: string;
    platforms: string[];
    profiles: string[];
    media?: string[]; // URLs de imágenes
    scheduledDate?: string; // ISO-8601 date string para programación nativa
    imageUrl?: string; // URL de imagen principal para la noticia
  }): Promise<UploadPostResponse> {
    console.log('📤 Publicando post inmediatamente:', {
      platforms: params.platforms,
      profiles: params.profiles,
      textLength: params.text.length
    });

    try {
      // Obtener Page ID real si se necesita para Facebook
      let facebookPageId = null;
      if (params.platforms.includes('facebook')) {
        facebookPageId = await this.getFacebookPageId(params.profiles[0]);
        console.log('📘 Facebook Page ID obtenido:', facebookPageId);
      }

      // Mapear plataformas al formato correcto de upload-post PRIMERO
      const mappedPlatforms = params.platforms.map(platform => {
        // Twitter debe ser 'x' según la documentación
        if (platform === 'twitter') return 'x';
        return platform;
      });

      // Usar siempre upload_text con parámetros específicos por plataforma
      const endpoint = '/api/upload_text';

      console.log(`📤 Usando endpoint: ${endpoint} con parámetros por plataforma`, {
        url: params.url,
        originalPlatforms: params.platforms,
        mappedPlatforms: mappedPlatforms,
        facebookPreview: params.url && mappedPlatforms.includes('facebook'),
        xSupport: mappedPlatforms.includes('x')
      });

      // Construir parámetros base
      const baseParams: Record<string, string> = {
        user: params.profiles[0],
        title: params.text
      };

      // Usar el formato correcto: platform[]
      // Para múltiples plataformas, necesitamos hacer llamadas separadas o usar el formato correcto
      if (mappedPlatforms.length === 1) {
        // Una sola plataforma: usar platform[]
        baseParams['platform[]'] = mappedPlatforms[0];
      } else {
        // Múltiples plataformas: usar platform[] con string separado por comas
        baseParams['platform[]'] = mappedPlatforms.join(',');
      }

      // Parámetros específicos para Facebook
      if (mappedPlatforms.includes('facebook')) {
        if (facebookPageId) {
          baseParams.facebook_page_id = facebookPageId;
        }
        // Vista previa automática de enlace para Facebook (detecta og:image automáticamente)
        if (params.url) {
          baseParams.facebook_link_url = params.url;
        }
      }

      // Parámetros específicos para Twitter (ahora 'x')
      if (mappedPlatforms.includes('x')) {
        // X (Twitter) maneja enlaces automáticamente, solo necesitamos incluir la URL en el texto si es necesario
        // La URL ya está en el texto del caption, X la detectará automáticamente
        console.log('🐦 X (Twitter) detectará enlaces automáticamente del texto');
      }

      // Agregar programación nativa si se especifica
      if (params.scheduledDate) {
        baseParams.scheduled_date = params.scheduledDate;
        console.log('📅 Programación nativa configurada:', params.scheduledDate);
      }

      const formData = new URLSearchParams(baseParams);

      console.log('📤 Datos enviados a upload-post:', Object.fromEntries(formData));

      const response = await this.makeRequest<UploadPostResponse>(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString(),
      });

      console.log('✅ Post publicado exitosamente con vista previa automática:', response);
      return response;
    } catch (error) {
      console.error('❌ Error publicando post:', error);
      throw error;
    }
  }

  /**
   * Programar publicación en redes sociales
   */
  async schedulePost(params: {
    text: string;
    url?: string;
    platforms: string[];
    profiles: string[];
    scheduledTime: Date;
    media?: string[];
  }): Promise<SchedulePostResponse> {
    console.log('⏰ Programando post:', {
      platforms: params.platforms,
      profiles: params.profiles,
      scheduledTime: params.scheduledTime.toISOString(),
      textLength: params.text.length
    });

    try {
      const response = await this.makeRequest<SchedulePostResponse>('/posts/schedule', {
        method: 'POST',
        body: JSON.stringify({
          text: params.text,
          url: params.url,
          platforms: params.platforms,
          profiles: params.profiles,
          scheduled_time: params.scheduledTime.toISOString(),
          media: params.media || []
        }),
      });

      console.log('✅ Post programado exitosamente:', response);
      return response;
    } catch (error) {
      console.error('❌ Error programando post:', error);
      throw error;
    }
  }

  /**
   * Cancelar publicación programada
   */
  async cancelScheduledPost(scheduleId: string): Promise<void> {
    console.log('🚫 Cancelando post programado:', scheduleId);

    try {
      await this.makeRequest(`/posts/schedule/${scheduleId}`, {
        method: 'DELETE',
      });

      console.log('✅ Post programado cancelado exitosamente');
    } catch (error) {
      console.error('❌ Error cancelando post programado:', error);
      throw error;
    }
  }

  /**
   * Obtener publicaciones programadas
   */
  async getScheduledPosts(): Promise<ScheduledPost[]> {
    console.log('📋 Obteniendo posts programados...');

    try {
      const response = await this.makeRequest<{ data: ScheduledPost[] }>('/posts/scheduled');
      console.log(`✅ ${response.data.length} posts programados obtenidos`);
      return response.data;
    } catch (error) {
      console.error('❌ Error obteniendo posts programados:', error);
      throw error;
    }
  }

  /**
   * Obtener perfiles conectados
   */
  async getProfiles(): Promise<UploadPostProfile[]> {
    console.log('👥 Obteniendo perfiles conectados...');

    try {
      const response = await this.makeRequest<{ success: boolean; profiles: any[] }>('/api/uploadposts/users');
      console.log(`✅ ${response.profiles.length} usuarios obtenidos`);

      // Convertir estructura de upload-post a nuestro formato
      const profiles: UploadPostProfile[] = [];

      for (const user of response.profiles) {
        const socialAccounts = user.social_accounts || {};

        // Agregar Facebook si existe
        if (socialAccounts.facebook && !socialAccounts.facebook.reauth_required) {
          profiles.push({
            id: `${user.username}_facebook`,
            platform: 'facebook',
            name: socialAccounts.facebook.display_name || user.username,
            is_connected: true,
            username: user.username
          });
        }

        // Agregar Instagram si existe
        if (socialAccounts.instagram && !socialAccounts.instagram.reauth_required) {
          profiles.push({
            id: `${user.username}_instagram`,
            platform: 'instagram',
            name: socialAccounts.instagram.display_name || user.username,
            is_connected: true,
            username: user.username
          });
        }

        // Agregar X/Twitter si existe
        if (socialAccounts.x && !socialAccounts.x.reauth_required) {
          profiles.push({
            id: `${user.username}_twitter`,
            platform: 'twitter',
            name: socialAccounts.x.display_name || user.username,
            is_connected: true,
            username: user.username
          });
        }

        // Agregar TikTok si existe
        if (socialAccounts.tiktok && socialAccounts.tiktok !== "" && !socialAccounts.tiktok.reauth_required) {
          profiles.push({
            id: `${user.username}_tiktok`,
            platform: 'tiktok',
            name: socialAccounts.tiktok.display_name || user.username,
            is_connected: true,
            username: user.username
          });
        }
      }

      console.log(`✅ ${profiles.length} cuentas de redes sociales encontradas`);
      return profiles;
    } catch (error) {
      console.error('❌ Error obteniendo perfiles:', error);
      throw error;
    }
  }

  /**
   * Obtener estado de una publicación
   */
  async getUploadStatus(uploadId: string): Promise<UploadStatus> {
    console.log('📊 Obteniendo estado de publicación:', uploadId);

    try {
      const response = await this.makeRequest<{ data: UploadStatus }>(`/posts/${uploadId}/status`);
      console.log('✅ Estado obtenido:', response.data.status);
      return response.data;
    } catch (error) {
      console.error('❌ Error obteniendo estado:', error);
      throw error;
    }
  }

  /**
   * Obtener estadísticas de la cuenta
   */
  async getAccountStats(): Promise<any> {
    console.log('📈 Obteniendo estadísticas de cuenta...');

    try {
      const response = await this.makeRequest<any>('/stats');
      console.log('✅ Estadísticas obtenidas');
      return response;
    } catch (error) {
      console.error('❌ Error obteniendo estadísticas:', error);
      throw error;
    }
  }

  /**
   * Verificar conectividad con la API
   */
  async testConnection(): Promise<boolean> {
    console.log('🔍 Verificando conexión con Upload-Post API...');

    try {
      await this.makeRequest('/api/uploadposts/users');
      console.log('✅ Conexión exitosa con Upload-Post API');
      return true;
    } catch (error) {
      console.error('❌ Error de conexión con Upload-Post API:', error);
      return false;
    }
  }

  async getFacebookPageId(username: string): Promise<string | null> {
    try {
      console.log('🔍 Obteniendo Facebook Page ID para:', username);

      // Usar endpoint oficial para obtener páginas de Facebook
      const response = await this.makeRequest<{ success: boolean; pages: any[] }>('/api/uploadposts/facebook/pages', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
        }
      });

      console.log('📋 Páginas de Facebook obtenidas:', response);

      // La respuesta tiene estructura { success: true, pages: [...] }
      if (response.success && Array.isArray(response.pages) && response.pages.length > 0) {
        const firstPage = response.pages[0];
        const pageId = firstPage.id;

        console.log('✅ Facebook Page ID encontrado:', pageId);
        console.log('📄 Página:', firstPage.name);

        return pageId;
      } else {
        console.warn('⚠️ No se encontraron páginas de Facebook');
        console.log('📋 Estructura de respuesta:', response);
        return null;
      }

    } catch (error) {
      console.error('❌ Error obteniendo Facebook Page ID:', error);
      return null;
    }
  }

  /**
   * Obtener límites de la cuenta
   */
  async getAccountLimits(): Promise<any> {
    console.log('📊 Obteniendo límites de cuenta...');

    try {
      const response = await this.makeRequest<any>('/account/limits');
      console.log('✅ Límites obtenidos:', response);
      return response;
    } catch (error) {
      console.error('❌ Error obteniendo límites:', error);
      throw error;
    }
  }
}
