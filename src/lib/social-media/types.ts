// Tipos para el sistema de redes sociales

export interface SocialMediaAccount {
  id: number;
  platform: 'facebook' | 'twitter' | 'instagram';
  accountName: string;
  profileId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SocialMediaPublication {
  id: number;
  publicacionExternaId: number;
  socialMediaAccountId: number;
  platform: 'facebook' | 'twitter' | 'instagram';
  caption: string;
  status: PublicationStatus;
  scheduledFor?: Date;
  publishedAt?: Date;
  externalPostId?: string;
  errorMessage?: string;
  retryCount: number;
  createdAt: Date;
  createdBy: string;
  
  // Relaciones opcionales
  publicacionExterna?: {
    id: number;
    noticiaTitulo: string;
    urlPublicacion: string;
    noticia: {
      titulo: string;
      contenido: string;
      imagenUrl?: string;
    };
  };
  socialMediaAccount?: SocialMediaAccount;
}

export type PublicationStatus = 
  | 'SCHEDULED'   // Programada
  | 'PENDING'     // En proceso
  | 'SUCCESS'     // Exitosa
  | 'ERROR'       // Error
  | 'CANCELLED'   // Cancelada
  | 'EXPIRED';    // Expirada

export type Platform = 'facebook' | 'twitter' | 'instagram';

// Parámetros para publicar
export interface PublishParams {
  publicacionExternaId: number;
  platforms: Platform[];
  accountIds: number[];
  publishType: 'immediate' | 'scheduled';
  scheduledFor?: Date;
  customCaption?: string;
}

// Parámetros para programar
export interface ScheduleParams {
  publicacionExternaId: number;
  platforms: Platform[];
  accountIds: number[];
  scheduledFor: Date;
  customCaption?: string;
}

// Resultado de publicación
export interface PublishResult {
  platform: Platform;
  accountId: number;
  success: boolean;
  publicationId?: number;
  externalPostId?: string;
  error?: string;
}

// Resultado de programación
export interface ScheduleResult {
  platform: Platform;
  accountId: number;
  success: boolean;
  scheduledPublicationId?: number;
  scheduledFor: Date;
  error?: string;
}

// Respuesta de la API upload-post.com
export interface UploadPostResponse {
  success: boolean;
  data?: {
    id: string;
    status: string;
    platforms: {
      platform: string;
      status: string;
      post_id?: string;
      error?: string;
    }[];
  };
  error?: string;
}

// Respuesta de programación de upload-post.com
export interface SchedulePostResponse {
  success: boolean;
  data?: {
    schedule_id: string;
    scheduled_time: string;
    platforms: string[];
    status: 'scheduled';
  };
  error?: string;
}

// Perfil de upload-post.com
export interface UploadPostProfile {
  id: string;
  platform: string;
  name: string;
  username?: string;
  avatar?: string;
  is_connected: boolean;
}

// Estado de upload en upload-post.com
export interface UploadStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  platforms: {
    platform: string;
    status: string;
    post_id?: string;
    post_url?: string;
    error?: string;
  }[];
  created_at: string;
  completed_at?: string;
}

// Post programado en upload-post.com
export interface ScheduledPost {
  schedule_id: string;
  text: string;
  platforms: string[];
  profiles: string[];
  scheduled_time: string;
  status: 'scheduled' | 'published' | 'failed' | 'cancelled';
  created_at: string;
}

// Parámetros para generar caption
export interface CaptionGenerationParams {
  title: string;
  content: string;
  platform: Platform;
  url: string;
  maxLength?: number;
  includeHashtags?: boolean;
  tone?: 'professional' | 'casual' | 'engaging';
}

// Caption generado
export interface GeneratedCaption {
  platform: Platform;
  caption: string;
  hashtags: string[];
  length: number;
  estimatedEngagement?: 'low' | 'medium' | 'high';
}

// Estadísticas de publicaciones
export interface PublicationStats {
  total: number;
  byStatus: Record<PublicationStatus, number>;
  byPlatform: Record<Platform, number>;
  today: number;
  thisWeek: number;
  thisMonth: number;
  scheduled: number;
  upcoming24h: number;
}

// Configuración de cuenta
export interface AccountConfig {
  id: number;
  platform: Platform;
  accountName: string;
  profileId: string;
  isActive: boolean;
  defaultHashtags?: string[];
  defaultTone?: 'professional' | 'casual' | 'engaging';
  timezone?: string;
  preferredTimes?: string[]; // ['09:00', '14:00', '18:00']
}

// Error de publicación
export interface PublicationError {
  code: string;
  message: string;
  platform: Platform;
  accountId: number;
  retryable: boolean;
  details?: any;
}

// Configuración global
export interface SocialMediaConfig {
  uploadPostApiKey: string;
  openaiApiKey?: string;
  defaultTimezone: string;
  maxRetries: number;
  retryDelay: number; // en minutos
  enableNotifications: boolean;
  notificationEmail?: string;
}
