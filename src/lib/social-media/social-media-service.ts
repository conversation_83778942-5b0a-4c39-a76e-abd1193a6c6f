import { PrismaClient } from '@prisma/client';
import { UploadPostClient } from './upload-post-client';
import { CaptionGenerator } from './caption-generator';
import {
  PublishParams,
  ScheduleParams,
  PublishResult,
  ScheduleResult,
  SocialMediaPublication,
  Platform,
  PublicationStatus
} from './types';

const prisma = new PrismaClient();

export class SocialMediaService {
  private uploadPostClient: UploadPostClient;
  private captionGenerator: CaptionGenerator;

  constructor() {
    this.uploadPostClient = new UploadPostClient();
    this.captionGenerator = new CaptionGenerator();
  }

  /**
   * Publicar inmediatamente en redes sociales
   */
  async publishToSocialMedia(params: PublishParams): Promise<PublishResult[]> {
    console.log('🚀 Iniciando publicación inmediata:', {
      publicacionExternaId: params.publicacionExternaId,
      platforms: params.platforms,
      accountIds: params.accountIds
    });

    const results: PublishResult[] = [];

    try {
      // Obtener datos de la publicación externa
      const publicacionExterna = await this.getPublicacionExterna(params.publicacionExternaId);
      if (!publicacionExterna) {
        throw new Error('Publicación externa no encontrada');
      }

      // Obtener cuentas de redes sociales
      const accounts = await this.getSocialMediaAccounts(params.accountIds);
      
      // Generar captions para cada plataforma
      const captions = await this.generateCaptions(publicacionExterna, params.platforms, params.customCaption);

      // Publicar en cada cuenta
      for (const account of accounts) {
        if (!params.platforms.includes(account.platform as Platform)) {
          continue;
        }

        try {
          const result = await this.publishToAccount(
            publicacionExterna,
            account,
            captions[account.platform as Platform],
            'immediate'
          );
          results.push(result);
        } catch (error) {
          console.error(`❌ Error publicando en ${account.platform}:`, error);
          results.push({
            platform: account.platform as Platform,
            accountId: account.id,
            success: false,
            error: error instanceof Error ? error.message : 'Error desconocido'
          });
        }
      }

      console.log(`✅ Publicación completada: ${results.filter(r => r.success).length}/${results.length} exitosas`);
      return results;
    } catch (error) {
      console.error('❌ Error en publicación inmediata:', error);
      throw error;
    }
  }

  /**
   * Programar publicación en redes sociales
   */
  async schedulePublication(params: ScheduleParams): Promise<ScheduleResult[]> {
    console.log('⏰ Programando publicación:', {
      publicacionExternaId: params.publicacionExternaId,
      platforms: params.platforms,
      scheduledFor: params.scheduledFor.toISOString()
    });

    const results: ScheduleResult[] = [];

    try {
      // Obtener datos de la publicación externa
      const publicacionExterna = await this.getPublicacionExterna(params.publicacionExternaId);
      if (!publicacionExterna) {
        throw new Error('Publicación externa no encontrada');
      }

      // Obtener cuentas de redes sociales
      const accounts = await this.getSocialMediaAccounts(params.accountIds);
      
      // Generar captions para cada plataforma
      const captions = await this.generateCaptions(publicacionExterna, params.platforms, params.customCaption);

      // Programar en cada cuenta
      for (const account of accounts) {
        if (!params.platforms.includes(account.platform as Platform)) {
          continue;
        }

        try {
          const result = await this.scheduleForAccount(
            publicacionExterna,
            account,
            captions[account.platform as Platform],
            params.scheduledFor
          );
          results.push(result);
        } catch (error) {
          console.error(`❌ Error programando en ${account.platform}:`, error);
          results.push({
            platform: account.platform as Platform,
            accountId: account.id,
            success: false,
            scheduledFor: params.scheduledFor,
            error: error instanceof Error ? error.message : 'Error desconocido'
          });
        }
      }

      console.log(`✅ Programación completada: ${results.filter(r => r.success).length}/${results.length} exitosas`);
      return results;
    } catch (error) {
      console.error('❌ Error en programación:', error);
      throw error;
    }
  }

  /**
   * Cancelar publicación programada
   */
  async cancelScheduledPublication(publicationId: number): Promise<void> {
    console.log('🚫 Cancelando publicación programada:', publicationId);

    try {
      const publication = await prisma.socialMediaPublication.findUnique({
        where: { id: publicationId }
      });

      if (!publication) {
        throw new Error('Publicación no encontrada');
      }

      if (publication.status !== 'SCHEDULED') {
        throw new Error('Solo se pueden cancelar publicaciones programadas');
      }

      // Cancelar en upload-post si tiene ID externo
      if (publication.externalPostId) {
        try {
          await this.uploadPostClient.cancelScheduledPost(publication.externalPostId);
        } catch (error) {
          console.warn('⚠️ Error cancelando en upload-post:', error);
        }
      }

      // Actualizar estado en base de datos
      await prisma.socialMediaPublication.update({
        where: { id: publicationId },
        data: {
          status: 'CANCELLED',
          errorMessage: 'Cancelada por usuario'
        }
      });

      console.log('✅ Publicación cancelada exitosamente');
    } catch (error) {
      console.error('❌ Error cancelando publicación:', error);
      throw error;
    }
  }

  /**
   * Obtener publicaciones programadas
   */
  async getScheduledPublications(filters?: {
    platform?: Platform;
    accountId?: number;
    dateFrom?: Date;
    dateTo?: Date;
  }): Promise<SocialMediaPublication[]> {
    console.log('📋 Obteniendo publicaciones programadas:', filters);

    const where: any = {
      status: 'SCHEDULED'
    };

    if (filters?.platform) {
      where.platform = filters.platform;
    }

    if (filters?.accountId) {
      where.socialMediaAccountId = filters.accountId;
    }

    if (filters?.dateFrom || filters?.dateTo) {
      where.scheduledFor = {};
      if (filters.dateFrom) {
        where.scheduledFor.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        where.scheduledFor.lte = filters.dateTo;
      }
    }

    const publications = await prisma.socialMediaPublication.findMany({
      where,
      include: {
        publicacionExterna: {
          include: {
            noticia: {
              select: {
                titulo: true,
                contenido: true,
                imagenUrl: true
              }
            }
          }
        },
        socialMediaAccount: true
      },
      orderBy: {
        scheduledFor: 'asc'
      }
    });

    return publications as any[];
  }

  /**
   * Procesar publicaciones programadas (para cron)
   */
  async processScheduledPublications(): Promise<void> {
    console.log('⚡ Procesando publicaciones programadas...');

    const now = new Date();
    const publications = await prisma.socialMediaPublication.findMany({
      where: {
        status: 'SCHEDULED',
        scheduledFor: {
          lte: now
        }
      },
      include: {
        publicacionExterna: {
          include: {
            noticia: true
          }
        },
        socialMediaAccount: true
      }
    });

    console.log(`📊 ${publications.length} publicaciones listas para procesar`);

    for (const publication of publications) {
      try {
        await this.executeScheduledPublication(publication as any);
      } catch (error) {
        console.error(`❌ Error ejecutando publicación ${publication.id}:`, error);
        await this.handlePublicationError(publication.id, error as Error);
      }
    }

    console.log('✅ Procesamiento de publicaciones programadas completado');
  }

  /**
   * Obtener historial de publicaciones
   */
  async getPublishHistory(publicacionExternaId: number): Promise<SocialMediaPublication[]> {
    const publications = await prisma.socialMediaPublication.findMany({
      where: {
        publicacionExternaId
      },
      include: {
        socialMediaAccount: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return publications as any[];
  }

  /**
   * Reintentar publicación fallida
   */
  async retryFailedPublication(publicationId: number): Promise<void> {
    console.log('🔄 Reintentando publicación fallida:', publicationId);

    const publication = await prisma.socialMediaPublication.findUnique({
      where: { id: publicationId },
      include: {
        publicacionExterna: {
          include: {
            noticia: true
          }
        },
        socialMediaAccount: true
      }
    });

    if (!publication) {
      throw new Error('Publicación no encontrada');
    }

    if (publication.status !== 'ERROR') {
      throw new Error('Solo se pueden reintentar publicaciones con error');
    }

    if (publication.retryCount >= 3) {
      throw new Error('Máximo número de reintentos alcanzado');
    }

    try {
      await this.executeScheduledPublication(publication as any);
    } catch (error) {
      await this.handlePublicationError(publicationId, error as Error);
      throw error;
    }
  }

  // Métodos privados auxiliares...
  private async getPublicacionExterna(id: number) {
    return await prisma.publicacionExterna.findUnique({
      where: { id },
      include: {
        noticia: {
          select: {
            titulo: true,
            contenido: true,
            imagenUrl: true
          }
        }
      }
    });
  }

  private async getSocialMediaAccounts(accountIds: number[]) {
    return await prisma.socialMediaAccount.findMany({
      where: {
        id: { in: accountIds },
        isActive: true
      }
    });
  }

  private async generateCaptions(
    publicacionExterna: any,
    platforms: Platform[],
    customCaption?: string
  ) {
    if (customCaption) {
      // Si hay caption personalizado, usarlo para todas las plataformas
      const result: Record<string, string> = {};
      platforms.forEach(platform => {
        result[platform] = customCaption;
      });
      return result;
    }

    // Generar captions con IA
    const captions = await this.captionGenerator.generateMultipleCaptions(
      {
        title: publicacionExterna.noticia.titulo,
        content: publicacionExterna.noticia.contenido,
        url: publicacionExterna.urlPublicacion || '',
        tone: 'professional'
      },
      platforms
    );

    const result: Record<string, string> = {};
    platforms.forEach(platform => {
      result[platform] = captions[platform]?.caption || '';
    });

    return result;
  }

  private async publishToAccount(
    publicacionExterna: any,
    account: any,
    caption: string,
    type: 'immediate' | 'scheduled'
  ): Promise<PublishResult> {
    // Crear registro en base de datos
    const publication = await prisma.socialMediaPublication.create({
      data: {
        publicacionExternaId: publicacionExterna.id,
        socialMediaAccountId: account.id,
        platform: account.platform,
        caption,
        status: 'PENDING',
        createdBy: 'system' // TODO: obtener usuario actual
      }
    });

    try {
      // Publicar en upload-post
      const response = await this.uploadPostClient.publishPost({
        text: caption,
        url: publicacionExterna.urlPublicacion,
        platforms: [account.platform],
        profiles: [account.profileId]
      });

      // Actualizar registro con resultado
      await prisma.socialMediaPublication.update({
        where: { id: publication.id },
        data: {
          status: 'SUCCESS',
          publishedAt: new Date(),
          externalPostId: response.data?.id
        }
      });

      return {
        platform: account.platform as Platform,
        accountId: account.id,
        success: true,
        publicationId: publication.id,
        externalPostId: response.data?.id
      };
    } catch (error) {
      // Actualizar registro con error
      await prisma.socialMediaPublication.update({
        where: { id: publication.id },
        data: {
          status: 'ERROR',
          errorMessage: error instanceof Error ? error.message : 'Error desconocido'
        }
      });

      throw error;
    }
  }

  private async scheduleForAccount(
    publicacionExterna: any,
    account: any,
    caption: string,
    scheduledFor: Date
  ): Promise<ScheduleResult> {
    // Crear registro en base de datos
    const publication = await prisma.socialMediaPublication.create({
      data: {
        publicacionExternaId: publicacionExterna.id,
        socialMediaAccountId: account.id,
        platform: account.platform,
        caption,
        status: 'SCHEDULED',
        scheduledFor,
        createdBy: 'system' // TODO: obtener usuario actual
      }
    });

    try {
      // Programar en upload-post
      const response = await this.uploadPostClient.schedulePost({
        text: caption,
        url: publicacionExterna.urlPublicacion,
        platforms: [account.platform],
        profiles: [account.profileId],
        scheduledTime: scheduledFor
      });

      // Actualizar registro con ID de programación
      await prisma.socialMediaPublication.update({
        where: { id: publication.id },
        data: {
          externalPostId: response.data?.schedule_id
        }
      });

      return {
        platform: account.platform as Platform,
        accountId: account.id,
        success: true,
        scheduledPublicationId: publication.id,
        scheduledFor
      };
    } catch (error) {
      // Actualizar registro con error
      await prisma.socialMediaPublication.update({
        where: { id: publication.id },
        data: {
          status: 'ERROR',
          errorMessage: error instanceof Error ? error.message : 'Error desconocido'
        }
      });

      throw error;
    }
  }

  private async executeScheduledPublication(publication: any): Promise<void> {
    console.log(`⚡ Ejecutando publicación programada ${publication.id}`);

    // Actualizar estado a PENDING
    await prisma.socialMediaPublication.update({
      where: { id: publication.id },
      data: { status: 'PENDING' }
    });

    try {
      // Publicar inmediatamente
      const response = await this.uploadPostClient.publishPost({
        text: publication.caption,
        url: publication.publicacionExterna.urlPublicacion,
        platforms: [publication.platform],
        profiles: [publication.socialMediaAccount.profileId]
      });

      // Actualizar con éxito
      await prisma.socialMediaPublication.update({
        where: { id: publication.id },
        data: {
          status: 'SUCCESS',
          publishedAt: new Date(),
          externalPostId: response.data?.id
        }
      });

      console.log(`✅ Publicación ${publication.id} ejecutada exitosamente`);
    } catch (error) {
      throw error;
    }
  }

  private async handlePublicationError(publicationId: number, error: Error): Promise<void> {
    const publication = await prisma.socialMediaPublication.findUnique({
      where: { id: publicationId }
    });

    if (!publication) return;

    const newRetryCount = publication.retryCount + 1;
    const maxRetries = 3;

    if (newRetryCount < maxRetries) {
      // Programar reintento
      const retryDelay = Math.pow(2, newRetryCount) * 5; // Backoff exponencial
      const retryTime = new Date(Date.now() + retryDelay * 60 * 1000);

      await prisma.socialMediaPublication.update({
        where: { id: publicationId },
        data: {
          status: 'SCHEDULED',
          scheduledFor: retryTime,
          retryCount: newRetryCount,
          errorMessage: `Reintento ${newRetryCount}/${maxRetries}: ${error.message}`
        }
      });

      console.log(`🔄 Programando reintento ${newRetryCount} para publicación ${publicationId} en ${retryDelay} minutos`);
    } else {
      // Marcar como error final
      await prisma.socialMediaPublication.update({
        where: { id: publicationId },
        data: {
          status: 'ERROR',
          retryCount: newRetryCount,
          errorMessage: `Error final después de ${maxRetries} intentos: ${error.message}`
        }
      });

      console.error(`❌ Publicación ${publicationId} falló definitivamente después de ${maxRetries} intentos`);
    }
  }
}
