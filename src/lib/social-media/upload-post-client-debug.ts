// Cliente simplificado para debugging de upload-post
export class UploadPostClientDebug {
  private apiKey: string;
  private baseUrl = 'https://api.upload-post.com';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.UPLOAD_POST_API_KEY || '';
    console.log('🔧 Inicializando cliente debug...');
    console.log(`   API Key presente: ${!!this.apiKey}`);
    console.log(`   API Key longitud: ${this.apiKey.length}`);
    console.log(`   API Key primeros 20 chars: ${this.apiKey.substring(0, 20)}...`);
    console.log(`   Base URL: ${this.baseUrl}`);
  }

  async testMultipleEndpoints(): Promise<any> {
    const endpoints = [
      '/api/uploadposts/users',
      '/api/uploadposts',
      '/profiles',
      '/profile',
      '/user/profiles',
      '/v1/profiles',
      '/api/profiles'
    ];

    const results = [];

    for (const endpoint of endpoints) {
      console.log(`🧪 [DEBUG] Probando endpoint: ${endpoint}`);

      try {
        const url = `${this.baseUrl}${endpoint}`;
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        });

        const contentType = response.headers.get('content-type');
        const responseText = await response.text();

        results.push({
          endpoint,
          status: response.status,
          statusText: response.statusText,
          contentType,
          isJson: contentType?.includes('application/json'),
          isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html'),
          responsePreview: responseText.substring(0, 100)
        });

      } catch (error) {
        results.push({
          endpoint,
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    return results;
  }

  async testConnection(): Promise<boolean> {
    console.log('🔍 [DEBUG] Probando conexión...');
    
    if (!this.apiKey) {
      console.log('❌ [DEBUG] No hay API key');
      return false;
    }

    try {
      const url = `${this.baseUrl}/api/uploadposts/users`;
      console.log(`📡 [DEBUG] Haciendo request a: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`📊 [DEBUG] Response status: ${response.status}`);
      console.log(`📊 [DEBUG] Response statusText: ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ [DEBUG] Error response: ${errorText}`);
        return false;
      }

      // Verificar content-type antes de parsear JSON
      const contentType = response.headers.get('content-type');
      console.log(`📋 [DEBUG] Content-Type: ${contentType}`);

      const responseText = await response.text();
      console.log(`📄 [DEBUG] Response text (primeros 200 chars): ${responseText.substring(0, 200)}`);

      // Verificar si es HTML
      if (responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')) {
        console.log(`❌ [DEBUG] La respuesta es HTML, no JSON. Posible problema de autenticación.`);
        return false;
      }

      // Intentar parsear JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log(`✅ [DEBUG] Response data:`, JSON.stringify(data, null, 2));
      } catch (parseError) {
        console.log(`❌ [DEBUG] Error parseando JSON:`, parseError);
        console.log(`📄 [DEBUG] Response completo: ${responseText}`);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('❌ [DEBUG] Error en testConnection:', error);
      return false;
    }
  }

  async getProfiles(): Promise<any[]> {
    console.log('👥 [DEBUG] Obteniendo perfiles...');
    
    if (!this.apiKey) {
      console.log('❌ [DEBUG] No hay API key para getProfiles');
      throw new Error('API key requerida');
    }

    try {
      const url = `${this.baseUrl}/api/uploadposts/users`;
      console.log(`📡 [DEBUG] Request URL: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`📊 [DEBUG] Status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.log(`❌ [DEBUG] Error: ${errorText}`);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      // Verificar content-type
      const contentType = response.headers.get('content-type');
      console.log(`📋 [DEBUG] Content-Type: ${contentType}`);

      const responseText = await response.text();
      console.log(`📄 [DEBUG] Response text (primeros 200 chars): ${responseText.substring(0, 200)}`);

      // Verificar si es HTML
      if (responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')) {
        console.log(`❌ [DEBUG] La respuesta es HTML, no JSON. Problema de autenticación.`);
        throw new Error('La API devolvió HTML en lugar de JSON. Verifica la API key.');
      }

      // Intentar parsear JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log(`✅ [DEBUG] Profiles data:`, JSON.stringify(data, null, 2));
      } catch (parseError) {
        console.log(`❌ [DEBUG] Error parseando JSON:`, parseError);
        console.log(`📄 [DEBUG] Response completo: ${responseText}`);
        throw new Error('Respuesta no es JSON válido');
      }

      // Verificar estructura de respuesta de upload-post
      if (data.success && data.profiles && Array.isArray(data.profiles)) {
        console.log(`📱 [DEBUG] ${data.profiles.length} usuarios encontrados`);

        // Contar cuentas de redes sociales
        let totalAccounts = 0;
        for (const user of data.profiles) {
          const socialAccounts = user.social_accounts || {};
          if (socialAccounts.facebook && !socialAccounts.facebook.reauth_required) totalAccounts++;
          if (socialAccounts.instagram && !socialAccounts.instagram.reauth_required) totalAccounts++;
          if (socialAccounts.x && !socialAccounts.x.reauth_required) totalAccounts++;
          if (socialAccounts.tiktok && socialAccounts.tiktok !== "" && !socialAccounts.tiktok.reauth_required) totalAccounts++;
        }

        console.log(`📱 [DEBUG] ${totalAccounts} cuentas de redes sociales activas`);
        return data.profiles;
      } else {
        console.log('⚠️ [DEBUG] Estructura de respuesta inesperada');
        console.log('📄 [DEBUG] Estructura recibida:', Object.keys(data));
        return [];
      }
    } catch (error) {
      console.error('❌ [DEBUG] Error en getProfiles:', error);
      throw error;
    }
  }
}
