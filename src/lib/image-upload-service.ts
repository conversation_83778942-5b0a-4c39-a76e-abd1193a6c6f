import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { ImageOptimizationService } from './image-optimization-service';

export interface UploadResult {
  success: boolean;
  imagenUrl?: string;
  error?: string;
  fileName?: string;
  fileSize?: number;
  message?: string;
  optimization?: {
    wasOptimized: boolean;
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    newDimensions: string;
  };
  optimizationInfo?: {
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    newDimensions: string;
  };
}

export class ImageUploadService {
  private static readonly UPLOAD_DIR = path.join(process.cwd(), 'public', 'uploads', 'images');
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  /**
   * Inicializar directorio de uploads
   */
  static async initializeUploadDir(): Promise<void> {
    try {
      if (!existsSync(this.UPLOAD_DIR)) {
        await mkdir(this.UPLOAD_DIR, { recursive: true });
        console.log('📁 Directorio de uploads creado:', this.UPLOAD_DIR);
      }
    } catch (error) {
      console.error('❌ Error creando directorio de uploads:', error);
      throw new Error('Error al inicializar directorio de uploads');
    }
  }

  /**
   * Validar archivo de imagen
   */
  static validateImageFile(file: File): { isValid: boolean; error?: string } {
    // Validar tipo de archivo
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        isValid: false,
        error: `Tipo de archivo no permitido. Solo se permiten: ${this.ALLOWED_TYPES.join(', ')}`
      };
    }

    // Validar tamaño
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `Archivo demasiado grande. Máximo permitido: ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    // Validar que el archivo no esté vacío
    if (file.size === 0) {
      return {
        isValid: false,
        error: 'El archivo está vacío'
      };
    }

    // Validar nombre de archivo
    if (!file.name || file.name.trim() === '') {
      return {
        isValid: false,
        error: 'Nombre de archivo inválido'
      };
    }

    return { isValid: true };
  }

  /**
   * Generar nombre único para el archivo
   */
  static generateFileName(originalName: string): string {
    const extension = path.extname(originalName).toLowerCase();
    const uuid = uuidv4();
    const timestamp = Date.now();
    return `${timestamp}-${uuid}${extension}`;
  }

  /**
   * Subir archivo de imagen
   */
  static async uploadImage(file: File): Promise<UploadResult> {
    try {
      console.log('📤 Iniciando upload de imagen:', {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Validar archivo
      const validation = this.validateImageFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Inicializar directorio si no existe
      await this.initializeUploadDir();

      // Generar nombre único
      const fileName = this.generateFileName(file.name);
      const filePath = path.join(this.UPLOAD_DIR, fileName);

      // Convertir File a Buffer con validación de memoria
      let bytes: ArrayBuffer;
      let originalBuffer: Buffer;

      try {
        bytes = await file.arrayBuffer();
        originalBuffer = Buffer.from(bytes);

        // Verificar que el buffer se creó correctamente
        if (!originalBuffer || originalBuffer.length === 0) {
          throw new Error('Error al convertir archivo a buffer');
        }

        // Verificar que el tamaño coincide
        if (originalBuffer.length !== file.size) {
          console.warn(`⚠️ Tamaño de buffer (${originalBuffer.length}) no coincide con archivo (${file.size})`);
        }
      } catch (error) {
        console.error('❌ Error convirtiendo archivo a buffer:', error);
        return {
          success: false,
          error: 'Error al procesar el archivo. El archivo puede estar corrupto.'
        };
      }

      console.log(`🔍 Verificando si la imagen necesita optimización...`);

      let finalBuffer = originalBuffer;
      let optimizationInfo = null;

      try {
        // Verificar si necesita optimización
        const optimizationCheck = await ImageOptimizationService.needsOptimization(originalBuffer);

        if (optimizationCheck.needsOptimization) {
          console.log(`🗜️ Optimizando imagen: ${optimizationCheck.reasons.join(', ')}`);

          try {
            // Optimizar imagen
            const optimized = await ImageOptimizationService.optimizeAIGeneratedImage(originalBuffer);
            finalBuffer = Buffer.from(optimized.buffer);
            optimizationInfo = {
              originalSize: optimized.originalSize,
              optimizedSize: optimized.optimizedSize,
              compressionRatio: optimized.compressionRatio,
              newDimensions: `${optimized.width}x${optimized.height}`
            };

            console.log(`✅ Imagen optimizada: ${optimized.compressionRatio.toFixed(1)}% de compresión`);
          } catch (optimizationError) {
            console.warn('⚠️ Error optimizando imagen, usando original:', optimizationError instanceof Error ? optimizationError.message : 'Error desconocido');
            finalBuffer = originalBuffer;
          }
        } else {
          console.log(`✅ Imagen ya está optimizada`);
        }
      } catch (checkError) {
        console.warn('⚠️ Error verificando optimización, usando imagen original:', checkError instanceof Error ? checkError.message : 'Error desconocido');
        finalBuffer = originalBuffer;
      }

      // Guardar archivo (optimizado o original)
      await writeFile(filePath, finalBuffer);

      // Generar URL pública
      const imagenUrl = `/uploads/images/${fileName}`;

      console.log('✅ Imagen subida exitosamente:', {
        fileName,
        imagenUrl,
        originalSize: file.size,
        finalSize: finalBuffer.length,
        optimized: !!optimizationInfo
      });

      return {
        success: true,
        imagenUrl,
        fileName,
        fileSize: finalBuffer.length,
        optimization: optimizationInfo ? {
          wasOptimized: true,
          originalSize: optimizationInfo.originalSize,
          optimizedSize: optimizationInfo.optimizedSize,
          compressionRatio: optimizationInfo.compressionRatio,
          newDimensions: optimizationInfo.newDimensions
        } : {
          wasOptimized: false,
          originalSize: file.size,
          optimizedSize: finalBuffer.length,
          compressionRatio: 0,
          newDimensions: 'Sin cambios'
        }
      };

    } catch (error) {
      console.error('❌ Error subiendo imagen:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido al subir imagen'
      };
    }
  }

  /**
   * Procesar imagen desde FormData
   */
  static async processImageFromFormData(formData: FormData, fieldName: string = 'imagen'): Promise<UploadResult> {
    try {
      const file = formData.get(fieldName) as File;
      
      if (!file || file.size === 0) {
        return {
          success: false,
          error: 'No se encontró archivo de imagen'
        };
      }

      return await this.uploadImage(file);
    } catch (error) {
      console.error('❌ Error procesando imagen desde FormData:', error);
      return {
        success: false,
        error: 'Error al procesar archivo de imagen'
      };
    }
  }

  /**
   * Obtener información de archivo subido
   */
  static getFileInfo(fileName: string): { exists: boolean; path?: string; url?: string } {
    const filePath = path.join(this.UPLOAD_DIR, fileName);
    const exists = existsSync(filePath);
    
    return {
      exists,
      path: exists ? filePath : undefined,
      url: exists ? `/uploads/images/${fileName}` : undefined
    };
  }

  /**
   * Limpiar archivos temporales (para uso futuro)
   */
  static async cleanupTempFiles(olderThanHours: number = 24): Promise<void> {
    // Implementación futura para limpiar archivos temporales
    console.log(`🧹 Limpieza de archivos temporales (más de ${olderThanHours} horas)`);
  }

  /**
   * Procesa imagen base64 con optimización automática inteligente
   */
  static async processBase64Image(
    base64String: string,
    titulo?: string,
    options?: {
      forceOptimization?: boolean;
      targetFormat?: 'jpeg' | 'png' | 'webp';
      quality?: number;
    }
  ): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      console.log('🔄 Iniciando procesamiento de imagen base64...');

      // Validar entrada
      if (!base64String || !base64String.includes(',')) {
        throw new Error('Formato de base64 inválido');
      }

      // Analizar imagen antes de procesar
      const analysis = ImageOptimizationService.analyzeBase64Image(base64String);

      console.log('📊 Análisis inicial de imagen:', {
        tamaño: `${(analysis.sizeBytes / 1024).toFixed(2)}KB`,
        formato: analysis.format,
        necesitaOptimización: analysis.needsOptimization,
        razones: analysis.reasons
      });

      let finalBase64 = base64String;
      let optimizationResult = null;

      // Decidir si optimizar automáticamente
      const shouldOptimize = options?.forceOptimization || analysis.needsOptimization;

      if (shouldOptimize) {
        console.log('�️ Aplicando optimización automática...');

        // Configurar optimización
        const optimizationConfig = {
          targetFormat: options?.targetFormat || 'jpeg',
          quality: options?.quality || 85,
          maxWidth: 1920,
          maxHeight: 1080,
          enableResize: true
        };

        // Optimizar imagen
        optimizationResult = await ImageOptimizationService.optimizeBase64Image(
          base64String,
          optimizationConfig
        );

        if (optimizationResult.success && optimizationResult.optimizedBase64) {
          finalBase64 = optimizationResult.optimizedBase64;
          console.log('✅ Optimización exitosa:', {
            compresión: `${optimizationResult.compressionRatio.toFixed(1)}%`,
            tamañoFinal: `${(optimizationResult.optimizedSize / 1024).toFixed(2)}KB`,
            formato: optimizationResult.format,
            dimensiones: `${optimizationResult.dimensions.width}x${optimizationResult.dimensions.height}`
          });
        } else {
          console.warn('⚠️ Optimización falló, usando imagen original:', optimizationResult.error);
        }
      } else {
        console.log('✅ Imagen ya está optimizada, no requiere procesamiento adicional');
      }

      // Convertir base64 final a buffer
      const [header, data] = finalBase64.split(',');
      const finalBuffer = Buffer.from(data, 'base64');

      // Generar nombre de archivo inteligente
      const mimeType = header.match(/data:([^;]+)/)?.[1] || 'image/jpeg';
      const extension = mimeType.split('/')[1];

      // Usar título si está disponible, sino usar timestamp
      let fileName: string;
      if (titulo) {
        // Limpiar título para nombre de archivo
        const cleanTitle = titulo
          .toLowerCase()
          .replace(/[^a-z0-9\s]/g, '') // Remover caracteres especiales
          .replace(/\s+/g, '-') // Reemplazar espacios con guiones
          .substring(0, 50); // Limitar longitud
        fileName = `${cleanTitle}-${Date.now()}.${extension}`;
      } else {
        fileName = `ai-generated-${Date.now()}.${extension}`;
      }

      // Guardar archivo en disco
      await this.initializeUploadDir();
      const filePath = path.join(this.UPLOAD_DIR, fileName);
      await writeFile(filePath, finalBuffer);

      const imagenUrl = `/uploads/images/${fileName}`;
      const processingTime = Date.now() - startTime;

      console.log('✅ Imagen base64 procesada exitosamente:', {
        archivo: fileName,
        tamañoFinal: `${(finalBuffer.length / 1024).toFixed(2)}KB`,
        url: imagenUrl,
        tiempoProcesamiento: `${processingTime}ms`,
        optimizada: !!optimizationResult?.success
      });

      // Preparar resultado
      const result: UploadResult = {
        success: true,
        imagenUrl,
        fileName,
        fileSize: finalBuffer.length,
        message: 'Imagen base64 procesada exitosamente'
      };

      // Agregar información de optimización si aplica
      if (optimizationResult?.success) {
        result.optimization = {
          wasOptimized: true,
          originalSize: optimizationResult.originalSize,
          optimizedSize: optimizationResult.optimizedSize,
          compressionRatio: optimizationResult.compressionRatio,
          newDimensions: `${optimizationResult.dimensions.width}x${optimizationResult.dimensions.height}`
        };
      } else {
        result.optimization = {
          wasOptimized: false,
          originalSize: analysis.sizeBytes,
          optimizedSize: finalBuffer.length,
          compressionRatio: 0,
          newDimensions: 'Sin cambios'
        };
      }

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('❌ Error procesando imagen base64:', {
        error: error instanceof Error ? error.message : 'Error desconocido',
        tiempoProcesamiento: `${processingTime}ms`
      });

      return {
        success: false,
        error: `Error procesando imagen base64: ${error instanceof Error ? error.message : 'Error desconocido'}`
      };
    }
  }
}

export default ImageUploadService;

