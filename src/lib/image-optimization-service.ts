// Importación condicional de Sharp para manejar errores en producción
let sharp: any = null;
try {
  sharp = require('sharp');
  console.log('✅ Sharp cargado exitosamente');
} catch (error) {
  console.error('❌ Error cargando Sharp:', error instanceof Error ? error.message : 'Error desconocido');
  console.warn('⚠️ Sharp no disponible - optimización de imágenes deshabilitada');
}

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'png' | 'jpeg' | 'webp';
  compressionLevel?: number;
  forceFormat?: boolean; // Forzar conversión de formato
  autoOptimize?: boolean; // Optimización automática basada en tamaño
}

export interface Base64OptimizationConfig {
  maxSizeBytes: number; // Tamaño máximo antes de optimizar (default: 1MB)
  targetFormat: 'jpeg' | 'png' | 'webp'; // Formato objetivo
  quality: number; // Calidad de compresión (80-90)
  maxWidth: number; // Ancho máximo
  maxHeight: number; // Alto máximo
  enableResize: boolean; // Habilitar redimensionamiento
}

export interface OptimizedImageResult {
  buffer: Buffer;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  format: string;
  width: number;
  height: number;
}

export class ImageOptimizationService {
  /**
   * Optimiza una imagen para web
   */
  static async optimizeImage(
    inputBuffer: Buffer,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImageResult> {
    const startTime = Date.now();

    // Verificar si Sharp está disponible
    if (!sharp) {
      console.warn('⚠️ Sharp no disponible, devolviendo imagen original');
      return {
        buffer: inputBuffer,
        originalSize: inputBuffer.length,
        optimizedSize: inputBuffer.length,
        compressionRatio: 0,
        format: 'original',
        width: 0,
        height: 0
      };
    }

    try {
      console.log('🖼️ Iniciando optimización de imagen...');
      
      // Configuración por defecto
      const {
        maxWidth = 1200,
        maxHeight = 800,
        quality = 85,
        format = 'png',
        compressionLevel = 6
      } = options;

      const originalSize = inputBuffer.length;
      console.log(`📊 Tamaño original: ${(originalSize / 1024).toFixed(2)} KB`);

      // Obtener metadatos de la imagen original
      const metadata = await sharp(inputBuffer).metadata();
      console.log(`📐 Dimensiones originales: ${metadata.width}x${metadata.height}`);
      console.log(`🎨 Formato original: ${metadata.format}`);

      // Crear pipeline de optimización
      let pipeline = sharp(inputBuffer);

      // 1. Redimensionar si es necesario
      const needsResize = (metadata.width && metadata.width > maxWidth) || 
                         (metadata.height && metadata.height > maxHeight);
      
      if (needsResize) {
        console.log(`🔄 Redimensionando a máximo ${maxWidth}x${maxHeight}`);
        pipeline = pipeline.resize(maxWidth, maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // 2. Aplicar optimizaciones según el formato
      let optimizedBuffer: Buffer;
      
      if (format === 'png') {
        console.log('🎯 Optimizando como PNG...');
        optimizedBuffer = await pipeline
          .png({
            compressionLevel,
            palette: true, // Usar paleta para reducir tamaño
            quality,
            effort: 8 // Máximo esfuerzo de compresión
          })
          .toBuffer();
      } else if (format === 'jpeg') {
        console.log('🎯 Optimizando como JPEG...');
        optimizedBuffer = await pipeline
          .jpeg({
            quality,
            mozjpeg: true, // Usar mozjpeg para mejor compresión
            chromaSubsampling: '4:2:0'
          })
          .toBuffer();
      } else if (format === 'webp') {
        console.log('🎯 Optimizando como WebP...');
        optimizedBuffer = await pipeline
          .webp({
            quality,
            effort: 6 // Buen balance entre compresión y velocidad
          })
          .toBuffer();
      } else {
        throw new Error(`Formato no soportado: ${format}`);
      }

      // Obtener metadatos de la imagen optimizada
      const optimizedMetadata = await sharp(optimizedBuffer).metadata();
      const optimizedSize = optimizedBuffer.length;
      const compressionRatio = ((originalSize - optimizedSize) / originalSize) * 100;

      const processingTime = Date.now() - startTime;
      
      console.log(`✅ Optimización completada en ${processingTime}ms:`);
      console.log(`📊 Tamaño optimizado: ${(optimizedSize / 1024).toFixed(2)} KB`);
      console.log(`📐 Dimensiones finales: ${optimizedMetadata.width}x${optimizedMetadata.height}`);
      console.log(`🗜️ Compresión: ${compressionRatio.toFixed(1)}%`);

      return {
        buffer: optimizedBuffer,
        originalSize,
        optimizedSize,
        compressionRatio,
        format,
        width: optimizedMetadata.width || 0,
        height: optimizedMetadata.height || 0
      };

    } catch (error) {
      console.error('❌ Error en optimización de imagen:', error);
      throw new Error(`Error optimizando imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  /**
   * Optimiza específicamente imágenes generadas por IA
   */
  static async optimizeAIGeneratedImage(inputBuffer: Buffer): Promise<OptimizedImageResult> {
    console.log('🤖 Optimizando imagen generada por IA...');
    
    return this.optimizeImage(inputBuffer, {
      maxWidth: 1024,  // Tamaño óptimo para artículos
      maxHeight: 768,  // Mantener aspecto 4:3
      quality: 90,     // Alta calidad para imágenes IA
      format: 'png',   // PNG para preservar detalles
      compressionLevel: 9 // Máxima compresión
    });
  }

  /**
   * Crea múltiples versiones optimizadas de una imagen
   */
  static async createMultipleVersions(inputBuffer: Buffer): Promise<{
    thumbnail: OptimizedImageResult;
    medium: OptimizedImageResult;
    large: OptimizedImageResult;
  }> {
    console.log('📸 Creando múltiples versiones de imagen...');

    const [thumbnail, medium, large] = await Promise.all([
      // Thumbnail: 300x200
      this.optimizeImage(inputBuffer, {
        maxWidth: 300,
        maxHeight: 200,
        quality: 80,
        format: 'jpeg'
      }),
      // Medium: 600x400
      this.optimizeImage(inputBuffer, {
        maxWidth: 600,
        maxHeight: 400,
        quality: 85,
        format: 'jpeg'
      }),
      // Large: 1200x800
      this.optimizeImage(inputBuffer, {
        maxWidth: 1200,
        maxHeight: 800,
        quality: 90,
        format: 'png'
      })
    ]);

    return { thumbnail, medium, large };
  }

  /**
   * Convierte cualquier imagen a PNG optimizado
   */
  static async convertToPNG(inputBuffer: Buffer): Promise<OptimizedImageResult> {
    console.log('🔄 Convirtiendo imagen a PNG optimizado...');
    
    return this.optimizeImage(inputBuffer, {
      maxWidth: 1200,
      maxHeight: 800,
      quality: 90,
      format: 'png',
      compressionLevel: 9
    });
  }

  /**
   * Verifica si una imagen necesita optimización
   */
  static async needsOptimization(inputBuffer: Buffer): Promise<{
    needsOptimization: boolean;
    reasons: string[];
    currentSize: number;
    estimatedSavings: number;
  }> {
    // Verificar si Sharp está disponible
    if (!sharp) {
      console.warn('⚠️ Sharp no disponible para verificar optimización');
      return {
        needsOptimization: false,
        reasons: ['Sharp no disponible'],
        currentSize: inputBuffer.length,
        estimatedSavings: 0
      };
    }

    try {
      const metadata = await sharp(inputBuffer).metadata();
      const currentSize = inputBuffer.length;
      const reasons: string[] = [];
    
    // Verificar tamaño del archivo
    if (currentSize > 2 * 1024 * 1024) { // > 2MB
      reasons.push('Archivo muy grande (>2MB)');
    }
    
    // Verificar dimensiones
    if (metadata.width && metadata.width > 1200) {
      reasons.push('Ancho muy grande (>1200px)');
    }
    
    if (metadata.height && metadata.height > 800) {
      reasons.push('Alto muy grande (>800px)');
    }
    
    // Verificar formato
    if (metadata.format && !['png', 'jpeg', 'webp'].includes(metadata.format)) {
      reasons.push('Formato no optimizado');
    }
    
      const needsOptimization = reasons.length > 0;
      const estimatedSavings = needsOptimization ? currentSize * 0.3 : 0; // Estimación 30% de ahorro

      return {
        needsOptimization,
        reasons,
        currentSize,
        estimatedSavings
      };
    } catch (error) {
      console.error('❌ Error verificando necesidad de optimización:', error instanceof Error ? error.message : 'Error desconocido');
      return {
        needsOptimization: false,
        reasons: ['Error verificando imagen'],
        currentSize: inputBuffer.length,
        estimatedSavings: 0
      };
    }
  }

  /**
   * Configuración por defecto para optimización de base64
   */
  static getDefaultBase64Config(): Base64OptimizationConfig {
    return {
      maxSizeBytes: 1024 * 1024, // 1MB
      targetFormat: 'jpeg',
      quality: 85,
      maxWidth: 1920,
      maxHeight: 1080,
      enableResize: true
    };
  }

  /**
   * Detecta si una imagen base64 necesita optimización
   */
  static analyzeBase64Image(base64String: string): {
    needsOptimization: boolean;
    sizeBytes: number;
    estimatedSizeAfterOptimization: number;
    format: string;
    reasons: string[];
  } {
    try {
      // Extraer información del header
      const [header, data] = base64String.split(',');
      const mimeType = header.match(/data:([^;]+)/)?.[1] || 'unknown';
      const format = mimeType.split('/')[1] || 'unknown';

      // Calcular tamaño real del archivo
      const sizeBytes = Math.round((data.length * 3) / 4);

      const config = this.getDefaultBase64Config();
      const reasons: string[] = [];

      // Verificar si excede el tamaño máximo
      if (sizeBytes > config.maxSizeBytes) {
        reasons.push(`Tamaño excesivo: ${(sizeBytes / 1024 / 1024).toFixed(2)}MB > ${(config.maxSizeBytes / 1024 / 1024).toFixed(2)}MB`);
      }

      // Verificar si es PNG (candidato para conversión a JPEG)
      if (format === 'png' && sizeBytes > 500 * 1024) { // PNG > 500KB
        reasons.push('PNG grande candidato para conversión a JPEG');
      }

      // Estimar tamaño después de optimización
      let estimatedSizeAfterOptimization = sizeBytes;
      if (format === 'png' && config.targetFormat === 'jpeg') {
        estimatedSizeAfterOptimization = Math.round(sizeBytes * 0.3); // JPEG típicamente 70% más pequeño
      } else if (reasons.length > 0) {
        estimatedSizeAfterOptimization = Math.round(sizeBytes * 0.6); // Optimización general
      }

      return {
        needsOptimization: reasons.length > 0,
        sizeBytes,
        estimatedSizeAfterOptimization,
        format,
        reasons
      };
    } catch (error) {
      console.error('❌ Error analizando imagen base64:', error);
      return {
        needsOptimization: false,
        sizeBytes: 0,
        estimatedSizeAfterOptimization: 0,
        format: 'unknown',
        reasons: ['Error analizando imagen']
      };
    }
  }

  /**
   * Optimiza una imagen base64 automáticamente
   */
  static async optimizeBase64Image(
    base64String: string,
    customConfig?: Partial<Base64OptimizationConfig>
  ): Promise<{
    success: boolean;
    optimizedBase64?: string;
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    format: string;
    dimensions: { width: number; height: number };
    processingTime: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      console.log('🔄 Iniciando optimización de imagen base64...');

      // Verificar si Sharp está disponible
      if (!sharp) {
        throw new Error('Sharp no está disponible para optimización');
      }

      // Analizar imagen
      const analysis = this.analyzeBase64Image(base64String);
      console.log('📊 Análisis de imagen:', {
        tamaño: `${(analysis.sizeBytes / 1024).toFixed(2)}KB`,
        formato: analysis.format,
        necesitaOptimización: analysis.needsOptimization,
        razones: analysis.reasons
      });

      // Configuración
      const config = { ...this.getDefaultBase64Config(), ...customConfig };

      // Convertir base64 a buffer
      const [header, data] = base64String.split(',');
      const inputBuffer = Buffer.from(data, 'base64');

      // Crear pipeline de Sharp
      let pipeline = sharp(inputBuffer);

      // Obtener metadatos
      const metadata = await pipeline.metadata();
      const originalWidth = metadata.width || 0;
      const originalHeight = metadata.height || 0;

      console.log('📐 Dimensiones originales:', `${originalWidth}x${originalHeight}`);

      // Redimensionar si es necesario
      if (config.enableResize &&
          (originalWidth > config.maxWidth || originalHeight > config.maxHeight)) {
        pipeline = pipeline.resize(config.maxWidth, config.maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
        console.log('📏 Redimensionando a máximo:', `${config.maxWidth}x${config.maxHeight}`);
      }

      // Aplicar formato y compresión
      let outputBuffer: Buffer;
      let outputFormat = config.targetFormat;

      if (config.targetFormat === 'jpeg') {
        outputBuffer = await pipeline
          .jpeg({
            quality: config.quality,
            progressive: true,
            mozjpeg: true // Mejor compresión
          })
          .toBuffer();
      } else if (config.targetFormat === 'webp') {
        outputBuffer = await pipeline
          .webp({
            quality: config.quality,
            effort: 6 // Máximo esfuerzo de compresión
          })
          .toBuffer();
      } else {
        // PNG optimizado
        outputBuffer = await pipeline
          .png({
            compressionLevel: 9,
            progressive: true
          })
          .toBuffer();
      }

      // Obtener dimensiones finales
      const finalMetadata = await sharp(outputBuffer).metadata();
      const finalWidth = finalMetadata.width || 0;
      const finalHeight = finalMetadata.height || 0;

      // Calcular estadísticas
      const originalSize = inputBuffer.length;
      const optimizedSize = outputBuffer.length;
      const compressionRatio = ((originalSize - optimizedSize) / originalSize) * 100;
      const processingTime = Date.now() - startTime;

      // Convertir de vuelta a base64
      const optimizedBase64 = `data:image/${outputFormat};base64,${outputBuffer.toString('base64')}`;

      console.log('✅ Optimización completada:', {
        tiempoMs: processingTime,
        tamañoOriginal: `${(originalSize / 1024).toFixed(2)}KB`,
        tamañoOptimizado: `${(optimizedSize / 1024).toFixed(2)}KB`,
        compresión: `${compressionRatio.toFixed(1)}%`,
        dimensionesFinales: `${finalWidth}x${finalHeight}`,
        formato: outputFormat
      });

      return {
        success: true,
        optimizedBase64,
        originalSize,
        optimizedSize,
        compressionRatio,
        format: outputFormat,
        dimensions: { width: finalWidth, height: finalHeight },
        processingTime
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('❌ Error optimizando imagen base64:', error);

      return {
        success: false,
        originalSize: 0,
        optimizedSize: 0,
        compressionRatio: 0,
        format: 'error',
        dimensions: { width: 0, height: 0 },
        processingTime,
        error: error instanceof Error ? error.message : 'Error desconocido'
      };
    }
  }
}
