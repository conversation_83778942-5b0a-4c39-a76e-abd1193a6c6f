/**
 * Credenciales de desarrollo - SOLO para entorno de desarrollo
 * Este archivo NO debe incluir credenciales reales de producción
 */

export interface DevCredential {
  email: string;
  password: string;
  role: string;
  description: string;
}

/**
 * Credenciales de prueba para desarrollo
 * Estas credenciales solo están disponibles cuando NODE_ENV === 'development'
 */
export const DEV_CREDENTIALS: DevCredential[] = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    description: 'Administrador del sistema'
  },
  {
    email: '<EMAIL>', 
    password: 'user123',
    role: 'user',
    description: 'Usuario estándar'
  }
];

/**
 * Obtiene las credenciales de desarrollo si está en modo desarrollo
 * @returns Array de credenciales o array vacío si no está en desarrollo
 */
export function getDevCredentials(): DevCredential[] {
  if (process.env.NODE_ENV !== 'development') {
    return [];
  }
  
  return DEV_CREDENTIALS;
}

/**
 * Verifica si las credenciales de desarrollo están habilitadas
 */
export function areDevCredentialsEnabled(): boolean {
  return process.env.NODE_ENV === 'development' && 
         process.env.SHOW_DEV_CREDENTIALS !== 'false';
}

/**
 * Obtiene las credenciales formateadas para mostrar en UI
 */
export function getFormattedCredentials(): string[] {
  if (!areDevCredentialsEnabled()) {
    return [];
  }

  const credentials = getDevCredentials();
  return credentials.map(cred => `${cred.description}: ${cred.email} / ${cred.password}`);
}

/**
 * Obtiene información para mostrar credenciales de desarrollo
 */
export function getDevCredentialsInfo() {
  return {
    enabled: areDevCredentialsEnabled(),
    credentials: getDevCredentials()
  };
}
