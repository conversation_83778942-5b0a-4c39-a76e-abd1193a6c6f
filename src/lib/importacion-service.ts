/**
 * Servicio para manejar la importación de noticias nacionales
 */

import { PrismaClient } from '@prisma/client';
import { NoticiaExternaMapeada } from './external-db';

const prisma = new PrismaClient();

export interface ResultadoImportacion {
  exito: boolean;
  noticiaId?: number;
  mensaje: string;
  error?: string;
}

/**
 * Procesa el contenido de una noticia externa para adaptarlo al sistema
 */
export function procesarContenidoNoticia(noticiaExterna: NoticiaExternaMapeada): {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
} {
  // Limpiar y procesar el título
  let titulo = noticiaExterna.titulo.trim();
  
  // Limitar el título a 200 caracteres
  if (titulo.length > 200) {
    titulo = titulo.substring(0, 197) + '...';
  }

  // Usar el resumen como subtítulo si está disponible
  let subtitulo = noticiaExterna.resumen?.trim();
  if (subtitulo && subtitulo.length > 300) {
    subtitulo = subtitulo.substring(0, 297) + '...';
  }

  // Procesar el contenido
  let contenido = noticiaExterna.contenido.trim();
  
  // Limpiar HTML básico si es necesario
  contenido = contenido
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remover scripts
    .replace(/<style[^>]*>.*?<\/style>/gi, '') // Remover estilos
    .replace(/<!--.*?-->/g, '') // Remover comentarios HTML
    .trim();

  // Generar resumen si no existe
  let resumen = noticiaExterna.resumen;
  if (!resumen && contenido) {
    // Extraer las primeras 2-3 oraciones como resumen
    const oraciones = contenido.split(/[.!?]+/).filter(o => o.trim().length > 10);
    if (oraciones.length > 0) {
      resumen = oraciones.slice(0, 2).join('. ').trim();
      if (resumen.length > 500) {
        resumen = resumen.substring(0, 497) + '...';
      } else {
        resumen += '.';
      }
    }
  }

  return {
    titulo,
    subtitulo,
    volanta: undefined, // No extraemos volanta por ahora
    contenido,
    resumen
  };
}

/**
 * Valida si una noticia puede ser importada
 */
export async function validarImportacion(noticiaExterna: NoticiaExternaMapeada): Promise<{
  valida: boolean;
  errores: string[];
}> {
  const errores: string[] = [];

  // Validar campos requeridos
  if (!noticiaExterna.titulo || noticiaExterna.titulo.trim().length === 0) {
    errores.push('El título es requerido');
  }

  if (!noticiaExterna.contenido || noticiaExterna.contenido.trim().length === 0) {
    errores.push('El contenido es requerido');
  }

  // Validar longitud mínima del contenido
  if (noticiaExterna.contenido && noticiaExterna.contenido.trim().length < 100) {
    errores.push('El contenido debe tener al menos 100 caracteres');
  }

  // Verificar si ya fue importada por ID externo (más confiable)
  console.log(`🔍 Validando importación para noticia externa ID: ${noticiaExterna.id}`);
  const estadoImportacion = await verificarNoticiaYaImportada(noticiaExterna.id);
  console.log(`📊 Estado de importación:`, estadoImportacion);

  if (estadoImportacion.yaImportada) {
    console.log(`⚠️ Noticia ya importada, agregando error de validación`);
    errores.push(`Esta noticia ya fue importada anteriormente (ID: ${estadoImportacion.noticiaId})`);
  }

  // Verificar si ya existe una noticia con el mismo título (verificación adicional)
  if (noticiaExterna.titulo && !estadoImportacion.yaImportada) {
    const noticiaExistente = await prisma.noticia.findFirst({
      where: {
        titulo: {
          equals: noticiaExterna.titulo.trim(),
          mode: 'insensitive'
        }
      }
    });

    if (noticiaExistente) {
      errores.push(`Ya existe una noticia con el título "${noticiaExterna.titulo}"`);
    }
  }

  return {
    valida: errores.length === 0,
    errores
  };
}

/**
 * Obtiene o crea la categoría para noticias nacionales
 */
export async function obtenerCategoriaImportacion(): Promise<{ id: number; nombre: string }> {
  let categoria = await prisma.categoria.findFirst({
    where: { 
      nombre: {
        equals: 'Nacionales',
        mode: 'insensitive'
      }
    }
  });

  if (!categoria) {
    categoria = await prisma.categoria.create({
      data: {
        nombre: 'Nacionales',
        descripcion: 'Noticias nacionales importadas desde fuentes externas',
        color: '#3B82F6' // Azul
      }
    });
  }

  return categoria;
}

/**
 * Verifica si una noticia externa ya fue importada
 */
export async function verificarNoticiaYaImportada(idExterno: number): Promise<{
  yaImportada: boolean;
  noticiaId?: number;
  fechaImportacion?: Date;
  importadaPor?: string;
}> {
  try {
    console.log(`🔍 Verificando si la noticia con ID externo ${idExterno} ya fue importada...`);

    // Buscar tanto como número como string para mayor compatibilidad
    const noticiaImportada = await prisma.noticia.findFirst({
      where: {
        OR: [
          {
            webhookData: {
              path: ['idOriginal'],
              equals: idExterno
            } as any
          },
          {
            webhookData: {
              path: ['idOriginal'],
              equals: idExterno.toString()
            }
          }
        ]
      },
      select: {
        id: true,
        createdAt: true,
        webhookData: true,
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log(`📊 Resultado de búsqueda para ID ${idExterno}:`, noticiaImportada ? 'ENCONTRADA' : 'NO ENCONTRADA');

    if (noticiaImportada) {
      const metadatos = noticiaImportada.webhookData as any;
      console.log(`📋 Metadatos de la noticia encontrada:`, metadatos);

      return {
        yaImportada: true,
        noticiaId: noticiaImportada.id,
        fechaImportacion: noticiaImportada.createdAt,
        importadaPor: noticiaImportada.user?.name || 'Usuario desconocido'
      };
    }

    return { yaImportada: false };

  } catch (error) {
    console.error('❌ Error al verificar noticia importada:', error);
    return { yaImportada: false };
  }
}

/**
 * Obtiene el estado de importación para múltiples noticias externas
 */
export async function obtenerEstadosImportacion(idsExternos: number[]): Promise<Map<number, {
  yaImportada: boolean;
  noticiaId?: number;
  fechaImportacion?: Date;
  importadaPor?: string;
}>> {
  try {
    const estadosMap = new Map();

    // Inicializar todos como no importados
    idsExternos.forEach(id => {
      estadosMap.set(id, { yaImportada: false });
    });

    // Buscar noticias importadas - crear condiciones OR para cada ID
    const condicionesOR: any[] = [];

    // Agregar condiciones para cada ID como número y como string
    idsExternos.forEach(id => {
      condicionesOR.push({
        webhookData: {
          path: ['idOriginal'],
          equals: id
        }
      });
      condicionesOR.push({
        webhookData: {
          path: ['idOriginal'],
          equals: id.toString()
        }
      });
    });

    const noticiasImportadas = await prisma.noticia.findMany({
      where: {
        OR: condicionesOR
      },
      select: {
        id: true,
        createdAt: true,
        webhookData: true,
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    // Actualizar el mapa con las noticias encontradas
    noticiasImportadas.forEach(noticia => {
      const metadatos = noticia.webhookData as any;
      const idOriginal = metadatos?.idOriginal;

      // Convertir idOriginal a número para comparación consistente
      const idOriginalNumero = typeof idOriginal === 'string' ? parseInt(idOriginal, 10) : idOriginal;

      if (idOriginalNumero && idsExternos.includes(idOriginalNumero)) {
        estadosMap.set(idOriginalNumero, {
          yaImportada: true,
          noticiaId: noticia.id,
          fechaImportacion: noticia.createdAt,
          importadaPor: noticia.user?.name || 'Usuario desconocido'
        });
      }
    });

    return estadosMap;

  } catch (error) {
    console.error('Error al obtener estados de importación:', error);
    // En caso de error, devolver todos como no importados
    const estadosMap = new Map();
    idsExternos.forEach(id => {
      estadosMap.set(id, { yaImportada: false });
    });
    return estadosMap;
  }
}

/**
 * Crea los metadatos de importación
 */
export function crearMetadatosImportacion(
  noticiaExterna: NoticiaExternaMapeada,
  usuarioId: string
): object {
  return {
    importada: true,
    fuenteExterna: true,
    idOriginal: noticiaExterna.id,
    autorOriginal: noticiaExterna.autor,
    categoriaOriginal: noticiaExterna.categoria,
    urlOriginal: noticiaExterna.url_original,
    fechaPublicacionOriginal: noticiaExterna.fecha_publicacion,
    fechaImportacion: new Date().toISOString(),
    importadaPor: usuarioId,
    version: '1.0'
  };
}

/**
 * Importa una noticia externa al sistema principal
 */
export async function importarNoticia(
  noticiaExterna: NoticiaExternaMapeada,
  usuarioId: string
): Promise<ResultadoImportacion> {
  try {
    // Validar la noticia antes de importar
    const validacion = await validarImportacion(noticiaExterna);
    if (!validacion.valida) {
      return {
        exito: false,
        mensaje: 'La noticia no puede ser importada',
        error: validacion.errores.join(', ')
      };
    }

    // Procesar el contenido
    const contenidoProcesado = procesarContenidoNoticia(noticiaExterna);

    // Obtener la categoría
    const categoria = await obtenerCategoriaImportacion();

    // Crear los metadatos
    const metadatos = crearMetadatosImportacion(noticiaExterna, usuarioId);
    console.log(`📋 Metadatos creados para importación:`, metadatos);

    // Crear la noticia en el sistema principal
    console.log(`💾 Creando noticia en el sistema principal...`);
    const nuevaNoticia = await prisma.noticia.create({
      data: {
        titulo: contenidoProcesado.titulo,
        subtitulo: contenidoProcesado.subtitulo,
        volanta: contenidoProcesado.volanta,
        contenido: contenidoProcesado.contenido,
        resumen: contenidoProcesado.resumen,
        imagenUrl: noticiaExterna.imagen_url,
        categoriaId: categoria.id,
        userId: parseInt(usuarioId),
        estado: 'BORRADOR', // Importar como borrador para permitir edición
        fechaPublicacion: noticiaExterna.fecha_publicacion,
        webhookData: JSON.stringify(metadatos)
      }
    });

    // Crear notificación
    await prisma.notificacion.create({
      data: {
        tipo: 'SISTEMA',
        titulo: 'Noticia nacional importada',
        mensaje: `Se importó exitosamente la noticia "${contenidoProcesado.titulo}"`,
        userId: parseInt(usuarioId),
        noticiaId: nuevaNoticia.id
      }
    });

    return {
      exito: true,
      noticiaId: nuevaNoticia.id,
      mensaje: 'Noticia importada exitosamente'
    };

  } catch (error) {
    console.error('Error al importar noticia:', error);
    
    return {
      exito: false,
      mensaje: 'Error interno al importar la noticia',
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

/**
 * Obtiene estadísticas de importación
 */
export async function obtenerEstadisticasImportacion(): Promise<{
  totalImportadas: number;
  importadasHoy: number;
  importadasEsteMes: number;
}> {
  try {
    const hoy = new Date();
    hoy.setHours(0, 0, 0, 0);

    const inicioMes = new Date(hoy.getFullYear(), hoy.getMonth(), 1);

    const [totalImportadas, importadasHoy, importadasEsteMes] = await Promise.all([
      prisma.noticia.count({
        where: {
          webhookData: {
            path: ['importada'],
            equals: true
          } as any
        }
      }),
      prisma.noticia.count({
        where: {
          webhookData: {
            path: ['importada'],
            equals: true
          } as any,
          createdAt: {
            gte: hoy
          }
        }
      }),
      prisma.noticia.count({
        where: {
          webhookData: {
            path: ['importada'],
            equals: true
          } as any,
          createdAt: {
            gte: inicioMes
          }
        }
      })
    ]);

    return {
      totalImportadas,
      importadasHoy,
      importadasEsteMes
    };

  } catch (error) {
    console.error('Error al obtener estadísticas de importación:', error);
    return {
      totalImportadas: 0,
      importadasHoy: 0,
      importadasEsteMes: 0
    };
  }
}
