import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from './prisma';



export interface RewriteRequest {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  prompt: string;
  diarioNombre: string;
}

export interface RewriteResponse {
  titulo: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  metadatos: {
    modelo: string;
    tokens_usados: number;
    tiempo_generacion: number;
    diario: string;
    proveedor: string;
  };
}

export interface AIConfig {
  // OpenAI
  openaiApiKey?: string;
  openaiModel: string;
  openaiMaxTokens: number;
  openaiTemperature: number;
  
  // Gemini
  geminiApiKey?: string;
  geminiModel: string;
  geminiMaxTokens: number;
  geminiTemperature: number;
  
  // Global
  defaultProvider: 'OPENAI' | 'GEMINI';
}

// Obtener configuración de IA
export async function getAIConfig(): Promise<AIConfig> {
  let config = await prisma.aIConfig.findFirst({
    where: { isActive: true },
    orderBy: { createdAt: 'desc' }
  });

  // Migrar modelos obsoletos
  if (config && (config.geminiModel === 'gemini-pro' || config.geminiModel === 'gemini-1.5-flash')) {
    const newModel = 'gemini-2.5-flash';
    console.log(`🔄 Migrando modelo Gemini obsoleto de ${config.geminiModel} a ${newModel}`);
    config = await prisma.aIConfig.update({
      where: { id: config.id },
      data: { geminiModel: newModel }
    });
  }

  if (!config) {
    // Configuración por defecto
    return {
      openaiApiKey: process.env.OPENAI_API_KEY,
      openaiModel: 'gpt-3.5-turbo',
      openaiMaxTokens: 2000,
      openaiTemperature: 0.7,
      geminiApiKey: process.env.GEMINI_API_KEY,
      geminiModel: 'gemini-2.5-flash',
      geminiMaxTokens: 2000,
      geminiTemperature: 0.7,
      defaultProvider: 'OPENAI'
    };
  }

  // SIEMPRE usar variables de entorno para API keys (nunca de la base de datos)
  return {
    openaiApiKey: process.env.OPENAI_API_KEY,
    openaiModel: config.openaiModel,
    openaiMaxTokens: config.openaiMaxTokens,
    openaiTemperature: config.openaiTemperature,
    geminiApiKey: process.env.GEMINI_API_KEY,
    geminiModel: config.geminiModel,
    geminiMaxTokens: config.geminiMaxTokens,
    geminiTemperature: config.geminiTemperature,
    defaultProvider: config.defaultProvider as 'OPENAI' | 'GEMINI'
  };
}

// Reescribir noticia usando OpenAI
async function rewriteWithOpenAI(request: RewriteRequest, config: AIConfig): Promise<RewriteResponse> {
  const startTime = Date.now();

  // Usar SIEMPRE la variable de entorno para la API key
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('OpenAI API key no configurada en variables de entorno');
  }

  const openai = new OpenAI({
    apiKey: apiKey,
  });

  const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES CRÍTICAS:
- Sigue EXACTAMENTE las instrucciones del prompt personalizado del diario
- Si el prompt pide HTML, entrega HTML puro dentro del JSON
- Si el prompt pide formato específico, respétalo completamente
- NO generes subtítulo, solo volanta, título, resumen y contenido
- El contenido debe seguir el formato solicitado en el prompt del diario
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "contenido en el formato solicitado por el prompt del diario"
}

No incluyas explicaciones adicionales, solo el JSON.`;

  // Prepare the request parameters based on the model
  const requestParams: any = {
    model: config.openaiModel,
    messages: [
      {
        role: "system",
        content: "Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional."
      },
      {
        role: "user",
        content: fullPrompt
      }
    ],
  };

  // Use the correct temperature based on the model
  if (!requiresDefaultTemperature(config.openaiModel)) {
    requestParams.temperature = config.openaiTemperature;
  }

  // Use the correct token parameter based on the model
  if (usesMaxCompletionTokens(config.openaiModel)) {
    requestParams.max_completion_tokens = config.openaiMaxTokens;
  } else {
    requestParams.max_tokens = config.openaiMaxTokens;
  }

  const completion = await openai.chat.completions.create(requestParams);

  const responseText = completion.choices[0]?.message?.content;
  if (!responseText || responseText.trim() === '') {
    console.log('⚠️ Respuesta vacía de OpenAI. Modelo:', config.openaiModel);
    console.log('📊 Detalles de la respuesta:', {
      choices: completion.choices?.length || 0,
      finishReason: completion.choices[0]?.finish_reason,
      usage: completion.usage
    });
    throw new Error(`El modelo ${config.openaiModel} no generó contenido. Esto puede ocurrir con modelos experimentales. Intenta con un modelo diferente como gpt-4o-mini.`);
  }

  // Intentar parsear la respuesta JSON
  let parsedResponse;
  try {
    parsedResponse = JSON.parse(responseText);
  } catch (parseError) {
    // Si falla el parsing, intentar extraer JSON del texto
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      parsedResponse = JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('La respuesta de OpenAI no está en formato JSON válido');
    }
  }

  const endTime = Date.now();
  const generationTime = endTime - startTime;

  return {
    titulo: parsedResponse.titulo || request.titulo,
    volanta: parsedResponse.volanta,
    contenido: parsedResponse.contenido || request.contenido, // HTML directo del prompt
    resumen: parsedResponse.resumen || undefined, // HTML directo del prompt
    metadatos: {
      modelo: completion.model,
      tokens_usados: completion.usage?.total_tokens || 0,
      tiempo_generacion: generationTime,
      diario: request.diarioNombre,
      proveedor: 'OpenAI'
    }
  };
}

// Reescribir noticia usando Google Gemini
async function rewriteWithGemini(request: RewriteRequest, config: AIConfig): Promise<RewriteResponse> {
  const startTime = Date.now();

  // Usar SIEMPRE la variable de entorno para la API key
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('Google Gemini API key no configurada en variables de entorno');
  }

  const genAI = new GoogleGenerativeAI(apiKey);

  // Configuración inicial con valores por defecto
  let maxOutputTokens = 8192;

  // Usar el prompt personalizado del diario, igual que OpenAI
  const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES CRÍTICAS:
- Sigue EXACTAMENTE las instrucciones del prompt personalizado del diario
- Si el prompt pide HTML, entrega HTML puro dentro del JSON
- Si el prompt pide formato específico, respétalo completamente
- NO generes subtítulo, solo volanta, título, resumen y contenido
- El contenido debe seguir el formato solicitado en el prompt del diario
- Mantén TODOS los hechos y datos importantes del contenido original
- El contenido reescrito debe ser TAN LARGO como el original o más extenso
- NO trunces ni acortes el contenido - debe estar COMPLETO
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "contenido en el formato solicitado por el prompt del diario"
}

No incluyas explicaciones adicionales, solo el JSON.`;

  // Verificar longitud del prompt y ajustar maxOutputTokens
  const promptLength = fullPrompt.length;
  const estimatedTokens = Math.ceil(promptLength / 4); // Aproximación: 4 chars = 1 token

  console.log(`📏 Prompt length: ${promptLength} chars, estimated tokens: ${estimatedTokens}`);

  // Ajustar maxOutputTokens según el tamaño del prompt para evitar límites
  if (estimatedTokens > 6000) {
    maxOutputTokens = 4096;
    console.log(`⚠️ Prompt muy largo, reduciendo maxOutputTokens a ${maxOutputTokens}`);
  } else if (estimatedTokens > 4000) {
    maxOutputTokens = 6144;
    console.log(`⚠️ Prompt mediano, ajustando maxOutputTokens a ${maxOutputTokens}`);
  }

  // Actualizar la configuración del modelo con el maxOutputTokens ajustado
  const model = genAI.getGenerativeModel({
    model: config.geminiModel,
    generationConfig: {
      temperature: config.geminiTemperature,
      maxOutputTokens: maxOutputTokens,
      topP: 0.8,
      topK: 40,
    }
  });

  console.log('📤 Enviando prompt a Gemini (longitud:', fullPrompt.length, 'caracteres)');

  const result = await model.generateContent(fullPrompt);
  const response = await result.response;

  console.log('📊 Response object keys:', Object.keys(response));
  console.log('📊 Response candidates:', response.candidates?.length || 0);

  // Verificar si la respuesta fue truncada
  const finishReason = response.candidates?.[0]?.finishReason;
  console.log('📊 Finish reason:', finishReason);

  if (finishReason === 'MAX_TOKENS') {
    console.log('⚠️ Respuesta truncada por límite de tokens');
    console.log(`📊 Tokens estimados del prompt: ${estimatedTokens}, maxOutputTokens usado: ${maxOutputTokens}`);
    throw new Error(`Contenido truncado por límite de tokens. Prompt: ${estimatedTokens} tokens, Output: ${maxOutputTokens} tokens`);
  }

  const responseText = response.text();

  console.log('📥 Response text length:', responseText?.length || 0);
  console.log('📥 Respuesta cruda de Gemini (completa):', responseText);

  if (!responseText || responseText.trim() === '') {
    console.error('❌ Respuesta vacía de Gemini');
    console.error('📊 Response completo:', JSON.stringify(response, null, 2));

    if (finishReason === 'MAX_TOKENS' as any) {
      throw new Error('Prompt demasiado largo - Gemini alcanzó límite de tokens');
    } else {
      throw new Error('Gemini devolvió respuesta vacía - posible filtro de contenido');
    }
  }

  // Limpiar y extraer JSON de la respuesta
  let parsedResponse;
  try {
    // Intentar parsing directo primero
    parsedResponse = JSON.parse(responseText);
    console.log('✅ JSON parseado directamente');
  } catch (parseError) {
    console.log('⚠️ Parsing directo falló, intentando extraer JSON...');

    // Limpiar la respuesta de markdown y texto extra
    let cleanedText = responseText
      .replace(/```json/gi, '') // Remover ```json
      .replace(/```/g, '') // Remover ```
      .trim();

    // Buscar el JSON usando regex más robusto
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleanedText = jsonMatch[0];
      console.log('✅ JSON extraído con regex:', cleanedText.substring(0, 100) + '...');
    } else {
      console.error('❌ No se encontró JSON en texto limpiado:', cleanedText);
      console.error('❌ Respuesta completa de Gemini:', responseText);

      // Si la respuesta está truncada, intentar completar el JSON
      if (finishReason === 'MAX_TOKENS' as any && cleanedText.includes('"volanta"')) {
        console.log('⚠️ Intentando reparar JSON truncado...');

        // Intentar completar el JSON básico
        let repairedJson = cleanedText;
        if (!repairedJson.endsWith('}')) {
          // Cerrar campos incompletos
          if (repairedJson.includes('"titulo":') && !repairedJson.includes('"resumen"')) {
            repairedJson += '", "resumen": "Resumen no disponible", "contenido": "Contenido no disponible"}';
          } else if (repairedJson.includes('"resumen":') && !repairedJson.includes('"contenido"')) {
            repairedJson += '", "contenido": "Contenido no disponible"}';
          } else {
            repairedJson += '"}';
          }
        }

        cleanedText = repairedJson;
        console.log('🔧 JSON reparado:', cleanedText);
      } else {
        throw new Error(`La respuesta de Google Gemini no contiene JSON válido. Respuesta: "${responseText.substring(0, 500)}..."`);
      }
    }

    try {
      parsedResponse = JSON.parse(cleanedText);
      console.log('✅ JSON extraído y parseado exitosamente');
    } catch (secondParseError) {
      console.error('❌ Error en segundo intento de parsing:', secondParseError);
      console.error('❌ Texto limpiado:', cleanedText);
      console.error('❌ Respuesta original:', responseText);
      throw new Error(`No se pudo parsear JSON de Gemini. Texto limpiado: "${cleanedText.substring(0, 200)}..."`);
    }
  }

  // Validar que el JSON tenga la estructura esperada
  if (!parsedResponse || typeof parsedResponse !== 'object') {
    throw new Error('La respuesta parseada no es un objeto válido');
  }

  // Validar campos requeridos
  const requiredFields = ['titulo', 'volanta', 'resumen', 'contenido'];
  const missingFields = requiredFields.filter(field => !parsedResponse[field]);

  if (missingFields.length > 0) {
    console.log('⚠️ Campos faltantes:', missingFields);
    console.log('📋 Campos disponibles:', Object.keys(parsedResponse));

    // Usar valores por defecto para campos faltantes
    if (!parsedResponse.titulo) parsedResponse.titulo = request.titulo;
    if (!parsedResponse.volanta) parsedResponse.volanta = request.volanta || 'Sin volanta';
    if (!parsedResponse.resumen) parsedResponse.resumen = request.resumen || 'Sin resumen';
    if (!parsedResponse.contenido) parsedResponse.contenido = request.contenido;
  }

  // Limpiar contenido truncado y puntos suspensivos
  if (parsedResponse.contenido) {
    // Remover puntos suspensivos al final
    parsedResponse.contenido = parsedResponse.contenido.replace(/\.{3,}$/, '');

    // Verificar si el contenido parece truncado
    const originalLength = request.contenido.length;
    const rewrittenLength = parsedResponse.contenido.length;

    console.log(`📏 Longitud original: ${originalLength}, reescrito: ${rewrittenLength}`);

    // Si el contenido reescrito es significativamente más corto, puede estar truncado
    if (rewrittenLength < originalLength * 0.5) {
      console.log('⚠️ Contenido posiblemente truncado - muy corto comparado con el original');
    }

    // Verificar si termina abruptamente (sin punto final)
    const lastChar = parsedResponse.contenido.trim().slice(-1);
    if (lastChar && !'.!?'.includes(lastChar)) {
      console.log('⚠️ Contenido posiblemente truncado - no termina con puntuación');
    }
  }

  const endTime = Date.now();
  const generationTime = endTime - startTime;

  console.log('✅ Reescritura de Gemini completada exitosamente');
  console.log(`⏱️ Tiempo de generación: ${generationTime}ms`);

  return {
    titulo: parsedResponse.titulo,
    volanta: parsedResponse.volanta,
    contenido: parsedResponse.contenido, // HTML directo del prompt
    resumen: parsedResponse.resumen || undefined, // HTML directo del prompt
    metadatos: {
      modelo: config.geminiModel,
      tokens_usados: 0, // Gemini no proporciona conteo de tokens en la respuesta
      tiempo_generacion: generationTime,
      diario: request.diarioNombre,
      proveedor: 'Google Gemini'
    }
  };
}

// Función principal para reescribir noticias
export async function rewriteNoticia(request: RewriteRequest, diarioId?: number): Promise<RewriteResponse> {
  try {
    console.log(`🤖 Iniciando reescritura para diario: ${request.diarioNombre} (ID: ${diarioId})`);

    const config = await getAIConfig();
    let provider = config.defaultProvider;

    console.log(`⚙️ Configuración inicial - Proveedor por defecto: ${provider}`);

    // Si se especifica un diario, verificar su configuración específica
    if (diarioId) {
      const diario = await prisma.diario.findUnique({
        where: { id: diarioId }
      });

      console.log(`📰 Configuración del diario ${request.diarioNombre}:`, {
        useGlobalConfig: diario?.useGlobalConfig,
        aiProvider: diario?.aiProvider,
        aiModel: diario?.aiModel
      });

      if (diario && !diario.useGlobalConfig && diario.aiProvider) {
        provider = diario.aiProvider as 'OPENAI' | 'GEMINI';
        console.log(`🔄 Cambiando proveedor a configuración específica del diario: ${provider}`);

        // Si el diario tiene un modelo específico, usarlo
        if (diario.aiModel) {
          if (provider === 'OPENAI') {
            config.openaiModel = diario.aiModel;
            console.log(`📝 Usando modelo OpenAI específico: ${diario.aiModel}`);
          } else {
            config.geminiModel = diario.aiModel;
            console.log(`📝 Usando modelo Gemini específico: ${diario.aiModel}`);
          }
        }
      }
    }

    console.log(`🎯 Proveedor final seleccionado: ${provider}`);
    console.log(`📋 Prompt del diario (primeros 100 chars): ${request.prompt.substring(0, 100)}...`);

    // Ejecutar reescritura según el proveedor
    if (provider === 'GEMINI') {
      return await rewriteWithGemini(request, config);
    } else {
      return await rewriteWithOpenAI(request, config);
    }

  } catch (error) {
    console.error('Error en rewriteNoticia:', error);
    throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

/**
 * Determines if a model uses the new max_completion_tokens parameter
 */
function usesMaxCompletionTokens(model: string): boolean {
  // Models that require max_completion_tokens instead of max_tokens
  const newModels = [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-4o-2024',
    'gpt-5',        // GPT-5 series
    'o1-preview',
    'o1-mini',
    'chatgpt-4o-latest'
  ];

  // Also check for any model with "2024" or "2025" in the name (likely newer models)
  const hasNewYear = model.includes('2024') || model.includes('2025');

  return newModels.some(newModel => model.includes(newModel)) || hasNewYear;
}

/**
 * Determines if a model requires default temperature (1) and cannot use custom temperature
 */
function requiresDefaultTemperature(model: string): boolean {
  // Models that only support temperature: 1 (default)
  const restrictedModels = [
    'gpt-5',        // GPT-5 series
    'o1-preview',   // o1 series
    'o1-mini'
  ];

  // Also check for any model with "2025" in the name (likely newer restricted models)
  const hasNewYear = model.includes('2025');

  return restrictedModels.some(restrictedModel => model.includes(restrictedModel)) || hasNewYear;
}

// Función para probar conexión con OpenAI
export async function testOpenAIConnection(): Promise<boolean> {
  try {
    console.log('🔍 Probando conexión con OpenAI...');

    const config = await getAIConfig();
    // Usar SIEMPRE la variable de entorno para la API key
    const keyToUse = process.env.OPENAI_API_KEY;

    console.log('🔑 API Key disponible:', !!keyToUse);
    console.log('📝 Modelo a usar:', config.openaiModel);

    if (!keyToUse) {
      console.error('❌ No hay API key disponible en variables de entorno');
      return false;
    }

    const openai = new OpenAI({
      apiKey: keyToUse,
    });

    console.log('📡 Enviando petición de prueba...');

    // Prepare the request parameters based on the model
    const requestParams: any = {
      model: config.openaiModel,
      messages: [{ role: "user", content: "Responde solo con 'OK'" }],
    };

    // Use the correct temperature based on the model
    if (requiresDefaultTemperature(config.openaiModel)) {
      // Don't set temperature for models that require default (1)
      console.log('📝 Usando temperatura por defecto (1) para modelo:', config.openaiModel);
    } else {
      requestParams.temperature = 0;
      console.log('📝 Usando temperatura 0 para modelo:', config.openaiModel);
    }

    // Use the correct token parameter based on the model
    if (usesMaxCompletionTokens(config.openaiModel)) {
      requestParams.max_completion_tokens = 10;
      console.log('📝 Usando max_completion_tokens para modelo:', config.openaiModel);
    } else {
      requestParams.max_tokens = 10;
      console.log('📝 Usando max_tokens para modelo:', config.openaiModel);
    }

    const completion = await openai.chat.completions.create(requestParams);

    const responseContent = completion.choices[0]?.message?.content;
    console.log('📥 Respuesta recibida:', responseContent || '[RESPUESTA VACÍA]');

    // For newer models, just having a successful response is enough
    // Some models might not respond exactly as expected but still work
    const hasResponse = completion.choices && completion.choices.length > 0;
    const hasContent = responseContent !== null && responseContent !== undefined;
    const isOk = hasResponse && (hasContent || !!completion.choices[0]?.message);

    console.log('✅ Conexión exitosa:', isOk);
    console.log('📊 Detalles de respuesta:', {
      hasResponse,
      hasContent,
      responseLength: responseContent?.length || 0,
      finishReason: completion.choices[0]?.finish_reason
    });

    return isOk;
  } catch (error) {
    console.error('❌ Error testing OpenAI connection:', error);
    if (error instanceof Error) {
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);
    }
    return false;
  }
}

// Función para probar conexión con Google Gemini (SOLO GENERACIÓN DE TEXTO)
export async function testGeminiConnection(): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔍 Probando conexión con Google Gemini para generación de texto...');

    const config = await getAIConfig();
    // Usar SIEMPRE la variable de entorno para la API key
    const keyToUse = process.env.GEMINI_API_KEY;

    console.log('🔑 API Key disponible:', !!keyToUse);

    if (!keyToUse) {
      const errorMsg = 'No hay API key disponible para Gemini en variables de entorno';
      console.error('❌', errorMsg);
      return { success: false, error: errorMsg };
    }

    // Usar modelo específico para generación de texto (NO imagen)
    const textModel = 'gemini-2.5-flash'; // Modelo específico para texto
    console.log('📝 Modelo de texto a usar:', textModel);

    const genAI = new GoogleGenerativeAI(keyToUse);

    // Configuración específica para generación de texto
    const model = genAI.getGenerativeModel({
      model: textModel,
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 50,
        topP: 0.8,
        topK: 10,
      }
    });

    console.log('📡 Enviando petición de prueba a Gemini (texto)...');

    // Crear una promesa con timeout de 5 segundos
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout: La conexión tardó más de 5 segundos')), 5000);
    });

    const testPromise = model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: 'Responde exactamente con la palabra "CONEXION_OK" para confirmar que la API funciona.' }]
      }]
    });

    // Ejecutar con timeout
    const result = await Promise.race([testPromise, timeoutPromise]);
    const response = await result.response;
    const text = response.text().trim();

    console.log('📥 Respuesta recibida de Gemini:', text);

    // Verificar respuesta válida
    const isValid = text.includes('CONEXION_OK') || text.includes('OK');

    if (isValid) {
      console.log('✅ Conexión Gemini exitosa para generación de texto');
      return { success: true };
    } else {
      const errorMsg = `Respuesta inesperada de Gemini: "${text}"`;
      console.log('⚠️', errorMsg);
      return { success: false, error: errorMsg };
    }

  } catch (error) {
    console.error('❌ Error testing Gemini connection:', error);

    let errorMessage = 'Error desconocido';

    if (error instanceof Error) {
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);

      // Identificar tipos específicos de error
      if (error.message.includes('API_KEY_INVALID')) {
        errorMessage = 'API Key de Gemini inválida o sin permisos para generación de texto';
      } else if (error.message.includes('PERMISSION_DENIED')) {
        errorMessage = 'API Key sin permisos para usar Gemini AI para generación de texto';
      } else if (error.message.includes('QUOTA_EXCEEDED')) {
        errorMessage = 'Cuota de API de Gemini excedida';
      } else if (error.message.includes('Timeout')) {
        errorMessage = 'Timeout: Gemini no respondió en 5 segundos';
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Error de red al conectar con Gemini API';
      } else if (error.message.includes('model')) {
        errorMessage = 'Modelo de Gemini no disponible o incorrecto para generación de texto';
      } else {
        errorMessage = error.message;
      }
    }

    return { success: false, error: errorMessage };
  }
}

// Función legacy para compatibilidad (devuelve solo boolean)
export async function testGeminiConnectionLegacy(): Promise<boolean> {
  const result = await testGeminiConnection();
  return result.success;
}

// Función para validar API Key de Gemini directamente
export async function validateGeminiApiKey(apiKey: string): Promise<{ valid: boolean; error?: string }> {
  try {
    console.log('🔍 Validando API Key de Gemini directamente...');

    if (!apiKey || !apiKey.startsWith('AIza')) {
      return {
        valid: false,
        error: 'Formato de API Key inválido. Debe comenzar con "AIza"'
      };
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({
      model: 'gemini-2.5-flash', // Modelo específico para texto
      generationConfig: {
        temperature: 0,
        maxOutputTokens: 10,
      }
    });

    // Timeout de 3 segundos para validación rápida
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout de validación')), 3000);
    });

    const testPromise = model.generateContent('Test');

    await Promise.race([testPromise, timeoutPromise]);

    console.log('✅ API Key de Gemini válida');
    return { valid: true };

  } catch (error) {
    console.error('❌ Error validando API Key de Gemini:', error);

    let errorMessage = 'API Key inválida';

    if (error instanceof Error) {
      if (error.message.includes('API_KEY_INVALID')) {
        errorMessage = 'API Key inválida o revocada';
      } else if (error.message.includes('PERMISSION_DENIED')) {
        errorMessage = 'API Key sin permisos para Gemini AI';
      } else if (error.message.includes('Timeout')) {
        errorMessage = 'Timeout validando API Key';
      }
    }

    return { valid: false, error: errorMessage };
  }
}

// Función con fallback automático para manejar errores de tokens
export async function rewriteNoticiaWithFallback(request: RewriteRequest, diarioId: number): Promise<RewriteResponse> {
  const config = await getAIConfig();
  let provider = config.defaultProvider;

  // Determinar proveedor según configuración del diario
  if (diarioId) {
    const diario = await prisma.diario.findUnique({
      where: { id: diarioId }
    });

    if (diario && !diario.useGlobalConfig && diario.aiProvider) {
      provider = diario.aiProvider as 'OPENAI' | 'GEMINI';
      console.log(`🔄 Usando proveedor específico del diario: ${provider}`);

      // Si el diario tiene un modelo específico, usarlo
      if (diario.aiModel) {
        if (provider === 'OPENAI') {
          config.openaiModel = diario.aiModel;
        } else {
          config.geminiModel = diario.aiModel;
        }
      }
    }
  }

  console.log(`🎯 Intentando con proveedor principal: ${provider}`);

  try {
    // Intentar con el proveedor configurado
    if (provider === 'GEMINI') {
      return await rewriteWithGemini(request, config);
    } else {
      return await rewriteWithOpenAI(request, config);
    }
  } catch (primaryError) {
    console.log(`❌ Error con proveedor principal (${provider}):`, primaryError);

    // Si es un error de tokens con Gemini, intentar fallback a OpenAI
    if (provider === 'GEMINI' && primaryError instanceof Error &&
        (primaryError.message.includes('tokens') || primaryError.message.includes('MAX_TOKENS'))) {

      console.log(`🔄 Error de tokens detectado, intentando fallback a OpenAI...`);

      try {
        return await rewriteWithOpenAI(request, config);
      } catch (fallbackError) {
        console.log(`❌ Fallback a OpenAI también falló:`, fallbackError);
        throw new Error(`Gemini falló por tokens, OpenAI también falló: ${fallbackError instanceof Error ? fallbackError.message : 'Error desconocido'}`);
      }
    }

    // Para otros errores, relanzar el error original
    throw primaryError;
  }
}
