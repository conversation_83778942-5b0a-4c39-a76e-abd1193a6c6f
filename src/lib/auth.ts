import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcrypt';
import { prisma } from './prisma';

// Extender tipos de NextAuth para incluir role
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
    }
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: string;
    email: string;
    name: string;
    iat?: number;
  }
}

// Validar que el secreto de NextAuth esté configurado
if (!process.env.NEXTAUTH_SECRET) {
  throw new Error(
    'NEXTAUTH_SECRET environment variable is required. ' +
    'Generate one with: openssl rand -base64 32'
  );
}

// Rate limiting simple en memoria (en producción usar Redis)
const loginAttempts = new Map<string, { count: number; lastAttempt: number }>();
const MAX_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minutos

function checkRateLimit(email: string): boolean {
  const now = Date.now();
  const attempts = loginAttempts.get(email);

  if (!attempts) {
    loginAttempts.set(email, { count: 1, lastAttempt: now });
    return true;
  }

  // Reset si ha pasado el tiempo de bloqueo
  if (now - attempts.lastAttempt > LOCKOUT_TIME) {
    loginAttempts.set(email, { count: 1, lastAttempt: now });
    return true;
  }

  // Verificar si está bloqueado
  if (attempts.count >= MAX_ATTEMPTS) {
    return false;
  }

  // Incrementar intentos
  attempts.count++;
  attempts.lastAttempt = now;
  return true;
}

function resetRateLimit(email: string): void {
  loginAttempts.delete(email);
}

// Función para sanitizar inputs
function sanitizeInput(input: string): string {
  return input.trim().toLowerCase();
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials, req) {
        // Validación básica de entrada
        if (!credentials?.email || !credentials?.password) {
          if (process.env.NODE_ENV === 'development') {
            console.log('❌ Credenciales faltantes');
          }
          return null;
        }

        // Sanitizar email
        const email = sanitizeInput(credentials.email);

        // Validar formato de email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          if (process.env.NODE_ENV === 'development') {
            console.log('❌ Formato de email inválido');
          }
          return null;
        }

        // Verificar rate limiting
        if (!checkRateLimit(email)) {
          if (process.env.NODE_ENV === 'development') {
            console.log('❌ Demasiados intentos de login para:', email);
          }
          return null;
        }

        try {
          // Buscar usuario
          const user = await prisma.user.findUnique({
            where: { email },
            select: {
              id: true,
              email: true,
              name: true,
              password: true,
              role: true,
              isActive: true,
              createdAt: true,
              updatedAt: true
            }
          });

          if (!user) {
            if (process.env.NODE_ENV === 'development') {
              console.log('❌ Usuario no encontrado:', email);
            }
            return null;
          }

          // Verificar si el usuario está activo
          if (!user.isActive) {
            if (process.env.NODE_ENV === 'development') {
              console.log('❌ Usuario inactivo:', email);
            }
            return null;
          }

          // Verificar contraseña
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            if (process.env.NODE_ENV === 'development') {
              console.log('❌ Contraseña inválida para:', email);
            }
            return null;
          }

          // Reset rate limit en login exitoso
          resetRateLimit(email);

          if (process.env.NODE_ENV === 'development') {
            console.log('✅ Autenticación exitosa para:', email);
          }

          // Retornar usuario sin información sensible
          return {
            id: user.id.toString(),
            email: user.email,
            name: user.name,
            role: user.role,
          };
        } catch (error) {
          // Log error sin exponer detalles sensibles
          console.error('❌ Error de autenticación:', error instanceof Error ? error.message : 'Error desconocido');
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 horas
    updateAge: 60 * 60, // 1 hora
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 horas
  },
  callbacks: {
    async jwt({ token, user, account, trigger }): Promise<any> {
      // En el primer login, agregar información del usuario
      if (user) {
        token.role = user.role;
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.iat = Math.floor(Date.now() / 1000);
      }

      // Verificar si el token ha expirado
      if (token.iat && Date.now() - token.iat * 1000 > 24 * 60 * 60 * 1000) {
        return null; // Forzar re-autenticación
      }

      // En actualizaciones de sesión, verificar que el usuario sigue activo
      if (trigger === 'update' && token.id) {
        try {
          const user = await prisma.user.findUnique({
            where: { id: parseInt(token.id as string) },
            select: { isActive: true, role: true }
          });

          if (!user || !user.isActive) {
            return null; // Invalidar token si usuario inactivo
          }

          // Actualizar rol si cambió
          token.role = user.role;
        } catch (error) {
          console.error('Error verificando usuario en JWT callback:', error);
          return null;
        }
      }

      return token;
    },
    async session({ session, token }): Promise<any> {
      // Verificar que el token es válido
      if (!token || !token.id) {
        return null;
      }

      // Agregar información del usuario a la sesión
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
      }

      return session;
    },
    async signIn({ user, account, profile, email, credentials }) {
      // Verificaciones adicionales de seguridad en el sign-in
      if (!user || !user.email) {
        return false;
      }

      // Para providers de credentials, ya se validó en authorize()
      if (account?.provider === 'credentials') {
        return true;
      }

      // Para otros providers, agregar validaciones adicionales aquí
      return true;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log de eventos de seguridad (solo en desarrollo)
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 Sign-in event:', {
          userId: user.id,
          email: user.email,
          provider: account?.provider,
          isNewUser
        });
      }
    },
    async signOut({ token, session }) {
      // Log de eventos de logout
      if (process.env.NODE_ENV === 'development') {
        console.log('🚪 Sign-out event:', {
          userId: token?.id || session?.user?.id
        });
      }
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};