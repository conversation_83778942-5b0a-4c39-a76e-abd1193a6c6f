import { PrismaClient } from '@prisma/client';
import { getDatabaseConfig, validateDatabaseConfig } from './database-security';

// Validar configuración de base de datos al inicializar
validateDatabaseConfig();

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Crear cliente Prisma con configuración segura
export const prisma = globalForPrisma.prisma ?? new PrismaClient(getDatabaseConfig());

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;