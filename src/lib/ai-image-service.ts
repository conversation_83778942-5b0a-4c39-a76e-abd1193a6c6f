import { prisma } from './prisma';
import type { Prisma } from '@prisma/client';
import { ImageUploadService } from './image-upload-service';

export interface GeneracionImagenParams {
  titulo: string;
  resumen?: string;
  contenido?: string;
  estilo?: string;
  configuracionId?: number;
}

export interface GeneracionImagenResult {
  id: number;
  imagenUrl?: string;
  estado: string;
  error?: string;
  metadatos?: Prisma.JsonValue;
  tiempoGeneracion?: number;
  tokensUsados?: number;
  costo?: number;
}

export class AIImageService {
  
  /**
   * Genera una imagen usando IA basada en el contenido de la noticia
   */
  static async generarImagen(
    params: GeneracionImagenParams,
    usuarioId: number,
    noticiaId?: number
  ): Promise<GeneracionImagenResult> {
    const startTime = Date.now();
    
    try {
      // Obtener configuración de IA
      const configuracion = await this.obtenerConfiguracion(params.configuracionId);
      
      // Verificar límites de uso
      await this.verificarLimites(usuarioId, configuracion.id);
      
      // Generar prompt optimizado
      const prompt = this.generarPrompt(params, configuracion.promptPorDefecto || undefined);
      
      // Crear registro de generación
      const generacion = await prisma.generacionImagen.create({
        data: {
          noticiaId,
          usuarioId,
          configuracionIAId: configuracion.id,
          prompt,
          promptOriginal: this.crearPromptOriginal(params),
          estado: 'PROCESANDO',
        },
      });

      try {
        // Generar imagen con el proveedor correspondiente
        const resultado = await this.generarConProveedor(
          configuracion,
          prompt,
          params
        );

        const tiempoGeneracion = Date.now() - startTime;

        // Procesar imagen base64 si es necesario
        let imagenUrlFinal = resultado.imagenUrl;
        let metadatosFinales = resultado.metadatos;

        if (resultado.imagenUrl && resultado.imagenUrl.startsWith('data:image/')) {
          console.log('🔄 Procesando imagen base64 generada automáticamente...');

          try {
            const uploadResult = await ImageUploadService.processBase64Image(
              resultado.imagenUrl,
              params.titulo,
              {
                forceOptimization: false, // Solo optimizar si es necesario
                targetFormat: 'jpeg',
                quality: 85
              }
            );

            if (uploadResult.success && uploadResult.imagenUrl) {
              imagenUrlFinal = uploadResult.imagenUrl;

              // Agregar información de optimización a metadatos
              metadatosFinales = {
                ...resultado.metadatos,
                optimizacion: uploadResult.optimization as any,
                archivoGuardado: uploadResult.fileName,
                tamañoFinal: uploadResult.fileSize,
                procesadoAutomaticamente: true
              } as any;

              console.log('✅ Imagen base64 procesada y guardada:', {
                archivoOriginal: uploadResult.fileName,
                tamañoFinal: `${((uploadResult.fileSize || 0) / 1024).toFixed(2)}KB`,
                optimizada: uploadResult.optimization?.wasOptimized || false
              });
            } else {
              console.warn('⚠️ Error procesando imagen base64, usando original:', uploadResult.error);
            }
          } catch (error) {
            console.warn('⚠️ Error en procesamiento automático de imagen:', error);
            // Continuar con imagen original si falla el procesamiento
          }
        }

        // Actualizar registro con resultado exitoso
        const generacionActualizada = await prisma.generacionImagen.update({
          where: { id: generacion.id },
          data: {
            imagenUrl: imagenUrlFinal,
            estado: 'COMPLETADA',
            metadatos: JSON.stringify(metadatosFinales),
            tiempoGeneracion,
            tokensUsados: resultado.tokensUsados,
            costo: resultado.costo,
          },
        });

        // Actualizar límites de uso
        await this.actualizarLimites(usuarioId, configuracion.id);

        return {
          id: generacionActualizada.id,
          imagenUrl: generacionActualizada.imagenUrl || undefined,
          estado: generacionActualizada.estado,
          metadatos: generacionActualizada.metadatos,
          tiempoGeneracion: generacionActualizada.tiempoGeneracion || undefined,
          tokensUsados: generacionActualizada.tokensUsados || undefined,
          costo: generacionActualizada.costo ? Number(generacionActualizada.costo) : undefined,
        };

      } catch (error) {
        // Actualizar registro con error
        await prisma.generacionImagen.update({
          where: { id: generacion.id },
          data: {
            estado: 'ERROR',
            error: error instanceof Error ? error.message : 'Error desconocido',
          },
        });

        throw error;
      }

    } catch (error) {
      console.error('Error en generación de imagen:', error);
      throw new Error(`Error al generar imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  /**
   * Obtiene la configuración de IA activa
   */
  private static async obtenerConfiguracion(configuracionId?: number) {
    if (configuracionId) {
      const config = await prisma.configuracionIA.findUnique({
        where: { id: configuracionId, activo: true },
      });

      if (!config) {
        throw new Error('Configuración de IA no encontrada o inactiva');
      }

      return config;
    }

    // Obtener configuración por defecto
    const config = await prisma.configuracionIA.findFirst({
      where: { activo: true },
      orderBy: { createdAt: 'asc' },
    });

    if (!config) {
      throw new Error('No hay configuraciones de IA disponibles');
    }

    return config;
  }

  /**
   * Verifica los límites de uso del usuario
   */
  private static async verificarLimites(usuarioId: number, configuracionId: number) {
    const ahora = new Date();
    const finMes = new Date(ahora.getFullYear(), ahora.getMonth() + 1, 0);

    // Buscar o crear límite mensual
    let limite = await prisma.limiteUsoIA.findFirst({
      where: {
        usuarioId,
        configuracionIAId: configuracionId,
        periodo: 'MENSUAL',
      },
    });

    if (!limite) {
      // Crear límite por defecto
      const configuracion = await prisma.configuracionIA.findUnique({
        where: { id: configuracionId },
      });

      const limitesDefecto = configuracion?.limitesUso as Record<string, unknown> | null;
      const limiteMensual = (limitesDefecto?.mensual as number) || 10;

      limite = await prisma.limiteUsoIA.create({
        data: {
          usuarioId,
          configuracionIAId: configuracionId,
          periodo: 'MENSUAL',
          limite: limiteMensual,
          usado: 0,
          fechaReset: finMes,
        },
      });
    }

    // Verificar si necesita reset
    if (limite.fechaReset < ahora) {
      limite = await prisma.limiteUsoIA.update({
        where: { id: limite.id },
        data: {
          usado: 0,
          fechaReset: finMes,
        },
      });
    }

    // Verificar límite
    if (limite.usado >= limite.limite) {
      throw new Error(`Límite mensual de ${limite.limite} generaciones alcanzado`);
    }
  }

  /**
   * Actualiza los límites de uso después de una generación exitosa
   */
  private static async actualizarLimites(usuarioId: number, configuracionId: number) {
    await prisma.limiteUsoIA.updateMany({
      where: {
        usuarioId,
        configuracionIAId: configuracionId,
        periodo: 'MENSUAL',
      },
      data: {
        usado: {
          increment: 1,
        },
      },
    });
  }

  /**
   * Genera un prompt optimizado para la generación de imágenes
   */
  private static generarPrompt(params: GeneracionImagenParams, promptTemplate?: string): string {
    const template = promptTemplate || 
      'Create a professional, high-quality image for a news article about: {titulo}. The image should be journalistic, clear, and appropriate for news media. Style: photorealistic, well-lit, professional.';

    let prompt = template.replace('{titulo}', params.titulo);
    
    if (params.resumen) {
      prompt = prompt.replace('{resumen}', params.resumen);
    }
    
    if (params.contenido) {
      const contenidoCorto = params.contenido.substring(0, 200) + '...';
      prompt = prompt.replace('{contenido}', contenidoCorto);
    }

    if (params.estilo) {
      prompt += ` Style: ${params.estilo}`;
    }

    return prompt;
  }

  /**
   * Crea el prompt original para referencia
   */
  private static crearPromptOriginal(params: GeneracionImagenParams): string {
    return `Título: ${params.titulo}\nResumen: ${params.resumen || 'N/A'}\nEstilo: ${params.estilo || 'Por defecto'}`;
  }

  /**
   * Genera imagen usando el proveedor específico
   */
  private static async generarConProveedor(
    configuracion: Record<string, unknown>,
    prompt: string,
    params: GeneracionImagenParams
  ) {
    switch (configuracion.proveedor) {
      case 'GOOGLE_GEMINI':
        return this.generarConGeminiVeo3(configuracion, prompt, params);
      case 'GOOGLE_IMAGEN':
        return this.generarConGeminiVeo3(configuracion, prompt, params);
      case 'OPENAI':
        return this.generarConOpenAI(configuracion, prompt, params);
      default:
        throw new Error(`Proveedor ${configuracion.proveedor} no soportado`);
    }
  }

  /**
   * Genera imagen usando Google Gemini (Imagen o Gemini 2.0 Flash)
   */
  private static async generarConGeminiVeo3(
    configuracion: Record<string, unknown>,
    prompt: string,
    _params: GeneracionImagenParams
  ) {
    // Usar directamente la API key desde variables de entorno
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('API Key de Google Gemini no configurada. Configure GEMINI_API_KEY en las variables de entorno.');
    }

    const parametros = (configuracion.parametros as Record<string, unknown>) || {};
    const modelo = (configuracion.modelo as string) || 'imagen-3.0-generate-001';

    console.log('🎨 Generando imagen con Google Gemini:', {
      modelo,
      prompt: prompt.substring(0, 100) + '...',
      configuracion: configuracion.nombre
    });

    try {
      // USAR SOLO IMAGEN 4.0 - Todos los modelos se redirigen a este
      return await this.generarConImagen(apiKey, modelo, prompt, parametros);
    } catch (error) {
      console.error('Error en generación con Google Gemini:', error);
      throw new Error(`Generación de imagen falló: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  /**
   * Crea un prompt seguro para evitar filtros de contenido
   */
  private static createSafePrompt(originalPrompt: string): string {
    // Palabras que pueden activar filtros de seguridad
    const problematicWords = [
      'policía', 'police', 'bebé', 'baby', 'niño', 'child', 'rescue', 'emergency',
      'hospital', 'médico', 'doctor', 'ambulancia', 'ambulance', 'accidente', 'accident'
    ];

    // Reemplazos seguros
    const safeReplacements: { [key: string]: string } = {
      'policía': 'professional person',
      'police': 'professional person',
      'bebé': 'small person',
      'baby': 'small person',
      'niño': 'young person',
      'child': 'young person',
      'rescue': 'help',
      'emergency': 'urgent situation',
      'hospital': 'medical building',
      'médico': 'healthcare professional',
      'doctor': 'healthcare professional',
      'ambulancia': 'medical vehicle',
      'ambulance': 'medical vehicle',
      'accidente': 'incident',
      'accident': 'incident'
    };

    let safePrompt = originalPrompt.toLowerCase();

    // Reemplazar palabras problemáticas
    for (const [problematic, safe] of Object.entries(safeReplacements)) {
      safePrompt = safePrompt.replace(new RegExp(problematic, 'gi'), safe);
    }

    // Si el prompt sigue siendo problemático, usar uno genérico
    if (problematicWords.some(word => safePrompt.includes(word.toLowerCase()))) {
      return 'Professional news photograph, high quality, well-lit, journalistic style';
    }

    return `${safePrompt}. Professional photograph, high quality, well-lit`;
  }

  /**
   * Genera imagen usando SOLO el modelo Imagen 4.0 (ÚNICO MODELO SOPORTADO)
   */
  private static async generarConImagen(
    apiKey: string,
    modelo: string,
    prompt: string,
    parametros: Record<string, unknown>
  ) {
    // FORZAR el uso del único modelo que funciona
    const modeloCorregido = 'imagen-4.0-generate-preview-06-06';

    console.log(`🔧 Corrigiendo modelo de ${modelo} a ${modeloCorregido}`);

    // Usar la API correcta para Imagen 4.0 con predict (la que funciona)
    const url = `https://generativelanguage.googleapis.com/v1beta/models/${modeloCorregido}:predict?key=${apiKey}`;

    // Crear un prompt más genérico y seguro para evitar filtros
    const safePrompt = this.createSafePrompt(prompt);

    const requestBody = {
      instances: [{
        prompt: safePrompt
      }],
      parameters: {
        sampleCount: 1,
        aspectRatio: (parametros.aspectRatio as string) || "1:1"
      }
    };

    console.log('📤 Enviando request a Imagen 4.0:', {
      url,
      modelo: modeloCorregido,
      prompt: prompt.substring(0, 100) + '...'
    });

    const startTime = Date.now();

    // Timeout de 30 segundos
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout: Imagen 4.0 no respondió en 30 segundos')), 30000);
    });

    const fetchPromise = fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    try {
      const response = await Promise.race([fetchPromise, timeoutPromise]);
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`⏱️ Tiempo de respuesta Imagen 4.0: ${duration}ms`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error response:', errorText);

        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: { message: errorText } };
        }

        throw new Error(`API Imagen 4.0 Error ${response.status}: ${errorData.error?.message || 'Error desconocido'}`);
      }

      const data = await response.json();
      console.log('📥 Response structure:', Object.keys(data));

      // Verificar si hay predicciones generadas (API predict)
      if (!data.predictions || data.predictions.length === 0) {
        console.error('❌ Respuesta completa:', JSON.stringify(data, null, 2));
        throw new Error('Imagen 4.0 no generó predicciones - array vacío');
      }

      const prediction = data.predictions[0];
      console.log('📄 Prediction structure:', Object.keys(prediction));

      // Buscar la imagen en la predicción
      let imagenUrl = null;

      if (prediction.bytesBase64Encoded) {
        // Imagen en base64 (formato estándar)
        imagenUrl = `data:image/png;base64,${prediction.bytesBase64Encoded}`;
        console.log('✅ Imagen encontrada en bytesBase64Encoded');
      } else if (prediction.imageBytes) {
        // Formato alternativo
        imagenUrl = `data:image/png;base64,${prediction.imageBytes}`;
        console.log('✅ Imagen encontrada en imageBytes');
      } else if (prediction.image && prediction.image.data) {
        // Otro formato posible
        imagenUrl = `data:image/png;base64,${prediction.image.data}`;
        console.log('✅ Imagen encontrada en image.data');
      }

      if (!imagenUrl) {
        console.error('❌ No se encontró imagen en la predicción:', JSON.stringify(prediction, null, 2));
        throw new Error('Imagen 4.0 no devolvió imagen válida - verificar formato de respuesta');
      }

      console.log('✅ Imagen generada exitosamente con Imagen 4.0');

      return {
        imagenUrl,
        metadatos: {
          modelo: modeloCorregido,
          proveedor: 'google_gemini_imagen',
          prompt,
          parametros,
          timestamp: new Date().toISOString(),
          aspectRatio: (parametros.aspectRatio as string) || "16:9",
          duration
        },
        tokensUsados: Math.ceil(prompt.length / 4),
        costo: 0.04,
      };

    } catch (error) {
      console.error('❌ Error en generarConImagen:', error);

      if (error instanceof Error) {
        if (error.message.includes('Timeout')) {
          throw new Error('Timeout: La generación de imagen tardó más de 30 segundos');
        } else if (error.message.includes('404')) {
          throw new Error('Modelo Imagen 4.0 no disponible - verificar configuración de API');
        } else if (error.message.includes('403')) {
          throw new Error('Sin permisos para usar Imagen 4.0 - verificar API Key');
        } else {
          throw error;
        }
      }

      throw new Error('Error desconocido en generación de imagen');
    }
  }



  /**
   * Genera imagen usando OpenAI DALL-E
   */
  private static async generarConOpenAI(
    configuracion: Record<string, unknown>,
    prompt: string,
    _params: GeneracionImagenParams
  ) {
    try {
      // Usar directamente la API key desde variables de entorno
      const apiKey = process.env.OPENAI_API_KEY;

      if (!apiKey) {
        throw new Error('API Key de OpenAI no configurada. Configure OPENAI_API_KEY en las variables de entorno.');
      }

      const parametros = (configuracion.parametros as Record<string, unknown>) || {};
      const modelo = (configuracion.modelo as string) || 'dall-e-3';

      console.log('🎨 Generando imagen con OpenAI DALL-E:', {
        modelo,
        prompt: prompt.substring(0, 100) + '...',
        configuracion: configuracion.nombre
      });

      const url = 'https://api.openai.com/v1/images/generations';

      const requestBody = {
        model: modelo,
        prompt: prompt,
        n: 1,
        size: (parametros.size as string) || "1024x1024",
        quality: (parametros.quality as string) || "standard",
        style: (parametros.style as string) || "vivid"
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Error de API OpenAI: ${response.status} - ${errorData.error?.message || 'Error desconocido'}`);
      }

      const data = await response.json();

      if (!data.data || data.data.length === 0) {
        throw new Error('No se generaron imágenes');
      }

      const imagenUrl = data.data[0].url;

      return {
        imagenUrl,
        metadatos: {
          modelo,
          proveedor: 'openai_dalle',
          prompt,
          parametros,
          timestamp: new Date().toISOString(),
          revisedPrompt: data.data[0].revised_prompt || prompt,
        },
        tokensUsados: Math.ceil(prompt.length / 4), // Estimación
        costo: modelo === 'dall-e-3' ? 0.04 : 0.02, // Costo estimado por imagen
      };

    } catch (error) {
      console.error('Error en generación con OpenAI:', error);
      throw new Error(`Error al generar imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  /**
   * Obtiene el historial de generaciones de un usuario
   */
  static async obtenerHistorial(usuarioId: number, limite = 20) {
    return prisma.generacionImagen.findMany({
      where: { usuarioId },
      include: {
        configuracion: {
          select: {
            nombre: true,
            modelo: true,
            proveedor: true,
          },
        },
        noticia: {
          select: {
            id: true,
            titulo: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limite,
    });
  }

  /**
   * Obtiene estadísticas de uso de un usuario
   */
  static async obtenerEstadisticas(usuarioId: number) {
    const ahora = new Date();
    const inicioMes = new Date(ahora.getFullYear(), ahora.getMonth(), 1);

    const [totalGeneraciones, generacionesMes, limitesActuales] = await Promise.all([
      prisma.generacionImagen.count({
        where: { usuarioId },
      }),
      prisma.generacionImagen.count({
        where: {
          usuarioId,
          createdAt: { gte: inicioMes },
        },
      }),
      prisma.limiteUsoIA.findMany({
        where: { usuarioId },
        include: {
          configuracion: {
            select: {
              nombre: true,
              modelo: true,
            },
          },
        },
      }),
    ]);

    return {
      totalGeneraciones,
      generacionesMes,
      limitesActuales,
    };
  }

  /**
   * Configuración por defecto para procesamiento automático de imágenes
   */
  static getAutoProcessingConfig() {
    return {
      enableAutoProcessing: true,
      maxSizeForAutoProcessing: 1024 * 1024, // 1MB
      defaultTargetFormat: 'jpeg' as const,
      defaultQuality: 85,
      enableResize: true,
      maxWidth: 1920,
      maxHeight: 1080
    };
  }

  /**
   * Verifica si una imagen base64 necesita procesamiento automático
   */
  static shouldAutoProcess(base64String: string): boolean {
    try {
      const config = this.getAutoProcessingConfig();

      if (!config.enableAutoProcessing) {
        return false;
      }

      // Calcular tamaño aproximado
      const [, data] = base64String.split(',');
      const sizeBytes = Math.round((data.length * 3) / 4);

      return sizeBytes > config.maxSizeForAutoProcessing;
    } catch (error) {
      console.warn('⚠️ Error verificando si necesita auto-procesamiento:', error);
      return false;
    }
  }

  /**
   * Procesa una imagen base64 con configuración optimizada para IA
   */
  static async processAIGeneratedImage(
    base64String: string,
    titulo: string,
    options?: {
      forceOptimization?: boolean;
      targetFormat?: 'jpeg' | 'png' | 'webp';
      quality?: number;
    }
  ) {
    const config = this.getAutoProcessingConfig();

    return await ImageUploadService.processBase64Image(
      base64String,
      titulo,
      {
        forceOptimization: options?.forceOptimization || this.shouldAutoProcess(base64String),
        targetFormat: options?.targetFormat || config.defaultTargetFormat,
        quality: options?.quality || config.defaultQuality
      }
    );
  }
}
