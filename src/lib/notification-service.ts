import { prisma } from '@/lib/prisma';

export interface CreateNotificationData {
  tipo: 'WEBHOOK_NOTICIA' | 'SISTEMA';
  titulo: string;
  mensaje?: string;
  noticiaId?: number;
  userId?: number;
  metadata?: any;
}

export class NotificationService {
  
  // Crear notificación para nueva noticia de WhatsApp
  static async createWebhookNotification(noticiaId: number, periodista: string, titulo: string) {
    try {
      // Obtener todos los usuarios con rol ADMIN o EDITOR
      const usuarios = await prisma.user.findMany({
        where: {
          role: {
            in: ['ADMIN', 'EDITOR']
          },
          isActive: true
        },
        select: { id: true }
      });

      // Crear notificación para cada usuario elegible
      const notificaciones = await Promise.all(
        usuarios.map(usuario => 
          prisma.notificacion.create({
            data: {
              tipo: 'WEBHOOK_NOTICIA',
              titulo: `Nueva noticia desde WhatsApp: ${titulo}`,
              mensaje: `Noticia recibida de ${periodista} vía WhatsApp`,
              noticiaId: noticiaId,
              userId: usuario.id,
              metadata: JSON.stringify({
                source: 'WhatsApp',
                periodista: periodista,
                timestamp: new Date().toISOString()
              })
            }
          })
        )
      );

      console.log(`✅ Creadas ${notificaciones.length} notificaciones para la noticia ID ${noticiaId}`);
      return notificaciones;

    } catch (error) {
      console.error('❌ Error al crear notificaciones de WhatsApp:', error);
      return [];
    }
  }

  // Crear notificación personalizada
  static async createNotification(data: CreateNotificationData) {
    try {
      const notificacion = await prisma.notificacion.create({
        data: {
          tipo: data.tipo,
          titulo: data.titulo,
          mensaje: data.mensaje,
          noticiaId: data.noticiaId,
          userId: data.userId!,
          metadata: data.metadata
        },
        include: {
          noticia: {
            select: {
              id: true,
              titulo: true,
              periodista: true,
              estado: true
            }
          }
        }
      });

      return notificacion;

    } catch (error) {
      console.error('❌ Error al crear notificación:', error);
      return null;
    }
  }

  // Obtener notificaciones de un usuario
  static async getUserNotifications(userId: number, limit: number = 10, onlyUnread: boolean = false) {
    try {
      const where: any = { userId };
      if (onlyUnread) {
        where.leida = false;
      }

      const notificaciones = await prisma.notificacion.findMany({
        where,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          noticia: {
            select: {
              id: true,
              titulo: true,
              periodista: true,
              estado: true,
              categoria: {
                select: {
                  nombre: true,
                  color: true
                }
              }
            }
          }
        }
      });

      return notificaciones;

    } catch (error) {
      console.error('❌ Error al obtener notificaciones:', error);
      return [];
    }
  }

  // Contar notificaciones no leídas
  static async getUnreadCount(userId: number) {
    try {
      const count = await prisma.notificacion.count({
        where: {
          userId,
          leida: false
        }
      });

      return count;

    } catch (error) {
      console.error('❌ Error al contar notificaciones no leídas:', error);
      return 0;
    }
  }

  // Marcar notificación como leída
  static async markAsRead(notificationId: number, userId: number) {
    try {
      const notificacion = await prisma.notificacion.updateMany({
        where: {
          id: notificationId,
          userId: userId
        },
        data: {
          leida: true,
          updatedAt: new Date()
        }
      });

      return notificacion.count > 0;

    } catch (error) {
      console.error('❌ Error al marcar notificación como leída:', error);
      return false;
    }
  }

  // Marcar todas las notificaciones como leídas
  static async markAllAsRead(userId: number) {
    try {
      const result = await prisma.notificacion.updateMany({
        where: {
          userId,
          leida: false
        },
        data: {
          leida: true,
          updatedAt: new Date()
        }
      });

      return result.count;

    } catch (error) {
      console.error('❌ Error al marcar todas las notificaciones como leídas:', error);
      return 0;
    }
  }

  // Limpiar notificaciones antiguas (mantener solo las últimas 50 por usuario)
  static async cleanupOldNotifications() {
    try {
      // Obtener todos los usuarios
      const usuarios = await prisma.user.findMany({
        select: { id: true }
      });

      let totalDeleted = 0;

      for (const usuario of usuarios) {
        // Obtener IDs de las notificaciones más recientes (últimas 50)
        const recentNotifications = await prisma.notificacion.findMany({
          where: { userId: usuario.id },
          orderBy: { createdAt: 'desc' },
          take: 50,
          select: { id: true }
        });

        const recentIds = recentNotifications.map(n => n.id);

        // Eliminar notificaciones antiguas
        if (recentIds.length === 50) {
          const deleted = await prisma.notificacion.deleteMany({
            where: {
              userId: usuario.id,
              id: {
                notIn: recentIds
              }
            }
          });

          totalDeleted += deleted.count;
        }
      }

      if (totalDeleted > 0) {
        console.log(`🧹 Limpieza completada: ${totalDeleted} notificaciones antiguas eliminadas`);
      }

      return totalDeleted;

    } catch (error) {
      console.error('❌ Error al limpiar notificaciones antiguas:', error);
      return 0;
    }
  }
}
