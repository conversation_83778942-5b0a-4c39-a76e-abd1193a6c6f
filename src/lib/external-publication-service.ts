// ========================================
// SERVICIO DE PUBLICACIÓN EXTERNA
// ========================================

import {
  DiarioExterno,
  CategoriaExterna,
  ImagenExternaResponse,
  ArticuloExternoResponse,
  ImagenExternaRequest,
  ArticuloExternoRequest,
  ConexionTestResult,
  ApiResponse
} from '@/types/external-publication';

export class ExternalPublicationService {
  private diario: DiarioExterno;

  constructor(diario: DiarioExterno) {
    this.diario = diario;
  }

  /**
   * Obtiene las categorías disponibles del diario externo
   */
  async fetchCategories(parentId?: number): Promise<ApiResponse<CategoriaExterna[]>> {
    try {
      const url = new URL(`${this.diario.urlBase}/api/v1/categories/fetch`);
      if (parentId) {
        url.searchParams.append('parent_id', parentId.toString());
      }

      console.log(`🔗 Conectando a: ${url.toString()}`);
      console.log(`🔑 Token: ${this.diario.bearerToken.substring(0, 20)}...`);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.diario.bearerToken}`,
        },
      });

      console.log(`📡 Respuesta del servidor: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Error del servidor externo: ${errorText}`);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📦 Datos recibidos:`, data);

      // Manejar diferentes formatos de respuesta
      let categoriasRaw = [];
      if (Array.isArray(data)) {
        categoriasRaw = data;
      } else if (data.payload && Array.isArray(data.payload)) {
        categoriasRaw = data.payload;
      } else if (data.categories && Array.isArray(data.categories)) {
        categoriasRaw = data.categories;
      } else if (data.data && Array.isArray(data.data)) {
        categoriasRaw = data.data;
      }

      console.log(`📋 Categorías raw encontradas: ${categoriasRaw.length}`);

      // Procesar categorías y sus hijos recursivamente
      const procesarCategorias = (cats: any[], parentId: number | null = null): any[] => {
        const resultado: any[] = [];

        for (const cat of cats) {
          // Mapear la categoría principal
          const categoria = {
            id: cat.category_id || cat.id,
            name: cat.category_name || cat.name || cat.nombre || cat.title,
            description: cat.category_description || cat.description || cat.descripcion || '',
            parent_id: parentId
          };

          if (categoria.id && categoria.name) {
            resultado.push(categoria);

            // Procesar categorías hijas si existen
            if (cat.children && Array.isArray(cat.children) && cat.children.length > 0) {
              const hijos = procesarCategorias(cat.children, categoria.id);
              resultado.push(...hijos);
            }
          }
        }

        return resultado;
      };

      const categorias = procesarCategorias(categoriasRaw);
      console.log(`✅ Categorías procesadas: ${categorias.length} categorías`);

      return {
        success: true,
        data: categorias,
        message: 'Categorías obtenidas exitosamente'
      };
    } catch (error) {
      console.error('❌ Error fetching categories:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
        message: 'Error al obtener categorías'
      };
    }
  }

  /**
   * Sube una imagen al diario externo
   */
  async uploadImage(imageRequest: ImagenExternaRequest): Promise<ApiResponse<ImagenExternaResponse>> {
    try {
      console.log(`🖼️ Iniciando subida de imagen: ${imageRequest.title}`);

      // Validar tipo de archivo
      const file = imageRequest.file;
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ];

      console.log(`📋 Tipo de archivo: ${file.type}`);

      if (!allowedTypes.includes(file.type.toLowerCase())) {
        console.error(`❌ Tipo de archivo no soportado: ${file.type}`);
        return {
          success: false,
          error: `Tipo de archivo no soportado: ${file.type}. Tipos permitidos: ${allowedTypes.join(', ')}`,
          message: 'Formato de imagen no válido'
        };
      }

      const formData = new FormData();
      formData.append('title', imageRequest.title);
      formData.append('summary', imageRequest.summary);
      formData.append('date', imageRequest.date);
      formData.append('category_id', imageRequest.category_id.toString());
      formData.append('file', imageRequest.file);

      console.log(`🚀 Enviando imagen a: ${this.diario.urlBase}/api/v1/image/insert`);
      console.log(`🔑 Token: ${this.diario.bearerToken.substring(0, 20)}...`);

      const response = await fetch(`${this.diario.urlBase}/api/v1/image/insert`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.diario.bearerToken}`,
        },
        body: formData,
      });

      console.log(`📡 Respuesta del servidor: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Error del servidor: ${errorText}`);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log(`✅ Imagen subida exitosamente:`, data);
      
      return {
        success: true,
        data: data,
        message: 'Imagen subida exitosamente'
      };
    } catch (error) {
      console.error('Error uploading image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
        message: 'Error al subir imagen'
      };
    }
  }

  /**
   * Publica un artículo en el diario externo
   */
  async publishArticle(articleRequest: ArticuloExternoRequest): Promise<ApiResponse<ArticuloExternoResponse>> {
    try {
      console.log(`📰 Iniciando publicación de artículo:`);
      console.log(`🚀 Enviando a: ${this.diario.urlBase}/api/v1/article/insert`);
      console.log(`📋 Payload:`, JSON.stringify(articleRequest, null, 2));

      const response = await fetch(`${this.diario.urlBase}/api/v1/article/insert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.diario.bearerToken}`,
        },
        body: JSON.stringify(articleRequest),
      });

      console.log(`📡 Respuesta del servidor: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Error del servidor: ${errorText}`);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log(`✅ Artículo publicado exitosamente:`, data);
      
      return {
        success: true,
        data: data,
        message: 'Artículo publicado exitosamente'
      };
    } catch (error) {
      console.error('Error publishing article:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
        message: 'Error al publicar artículo'
      };
    }
  }

  /**
   * Descarga una imagen desde una URL
   */
  async downloadImage(imageUrl: string): Promise<ApiResponse<Blob>> {
    try {
      console.log(`📥 Descargando imagen: ${imageUrl}`);
      console.log(`🌍 Variables de entorno disponibles:`, {
        NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
        NEXTAUTH_URL: process.env.NEXTAUTH_URL,
        NODE_ENV: process.env.NODE_ENV
      });

      // Convertir URL relativa a absoluta si es necesario
      let fullImageUrl = imageUrl;
      if (imageUrl.startsWith('/')) {
        // URL relativa - necesitamos construir la URL completa
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL ||
                       process.env.NEXTAUTH_URL ||
                       'https://panelunificadov2.sv1.diarios.live';
        fullImageUrl = `${baseUrl}${imageUrl}`;
        console.log(`🔗 URL convertida a absoluta: ${fullImageUrl}`);
      }

      const response = await fetch(fullImageUrl);
      console.log(`📡 Respuesta de descarga: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.error(`❌ Error descargando imagen: ${response.status} ${response.statusText}`);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type') || '';
      console.log(`📋 Tipo de contenido: ${contentType}`);

      // Validar tipo de archivo antes de descargar
      const allowedTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ];

      // Si es SVG, intentar convertir a PNG
      if (contentType.toLowerCase().includes('image/svg+xml')) {
        console.log(`🔄 Detectado SVG, intentando conversión a PNG...`);
        return await this.convertSvgToPng(imageUrl);
      }

      if (!allowedTypes.some(type => contentType.toLowerCase().includes(type))) {
        console.error(`❌ Tipo de archivo no soportado: ${contentType}`);
        return {
          success: false,
          error: `Tipo de archivo no soportado: ${contentType}. Tipos permitidos: ${allowedTypes.join(', ')}. Nota: SVG se convierte automáticamente a PNG.`,
          message: 'Formato de imagen no válido'
        };
      }

      const blob = await response.blob();
      console.log(`✅ Imagen descargada exitosamente. Tamaño: ${blob.size} bytes`);

      return {
        success: true,
        data: blob,
        message: 'Imagen descargada exitosamente'
      };
    } catch (error) {
      console.error('❌ Error downloading image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
        message: 'Error al descargar imagen'
      };
    }
  }

  /**
   * Prueba la conexión con el diario externo
   */
  async testConnection(): Promise<ConexionTestResult> {
    const result: ConexionTestResult = {
      success: false,
      message: '',
      details: {
        conectividad: false,
        autenticacion: false,
        categorias: false,
      }
    };

    try {
      // Test 1: Conectividad básica
      const connectivityTest = await fetch(`${this.diario.urlBase}/api/v1/categories/fetch`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.diario.bearerToken}`,
        },
      });

      if (connectivityTest.ok) {
        result.details!.conectividad = true;
        result.details!.autenticacion = true;

        // Test 2: Obtener categorías
        const categoriesResult = await this.fetchCategories();
        if (categoriesResult.success) {
          result.details!.categorias = true;
          result.success = true;
          result.message = 'Conexión exitosa. Todos los servicios funcionan correctamente.';
        } else {
          result.message = 'Conexión establecida pero error al obtener categorías.';
        }
      } else if (connectivityTest.status === 401 || connectivityTest.status === 403) {
        result.details!.conectividad = true;
        result.message = 'Error de autenticación. Verifica el Bearer Token.';
      } else {
        result.message = `Error de conectividad: HTTP ${connectivityTest.status}`;
      }
    } catch (error) {
      result.message = `Error de conexión: ${error instanceof Error ? error.message : 'Error desconocido'}`;
    }

    return result;
  }

  /**
   * Formatea una fecha para el API externa en zona horaria de Argentina (GMT-3)
   */
  static formatDateForAPI(date: Date): string {
    // Crear fecha en zona horaria de Argentina (GMT-3)
    const argentinaOffset = -3 * 60; // -3 horas en minutos
    const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
    const argentinaTime = new Date(utc + (argentinaOffset * 60000));

    // Formatear como YYYY-MM-DD HH:mm:ss
    const year = argentinaTime.getFullYear();
    const month = String(argentinaTime.getMonth() + 1).padStart(2, '0');
    const day = String(argentinaTime.getDate()).padStart(2, '0');
    const hours = String(argentinaTime.getHours()).padStart(2, '0');
    const minutes = String(argentinaTime.getMinutes()).padStart(2, '0');
    const seconds = String(argentinaTime.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * Valida que una URL sea válida
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Obtiene el nombre del archivo desde una URL
   */
  static getFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      return pathname.split('/').pop() || 'image.jpg';
    } catch {
      return 'image.jpg';
    }
  }

  /**
   * Valida el formato del Bearer Token
   */
  static isValidBearerToken(token: string): boolean {
    // Validación básica: debe ser un JWT o tener formato válido
    return token.length > 10 && (token.includes('.') || token.length > 32);
  }

  /**
   * Convierte una imagen SVG a PNG
   */
  private async convertSvgToPng(svgUrl: string): Promise<ApiResponse<Blob>> {
    try {
      console.log(`🔄 Convirtiendo SVG a PNG: ${svgUrl}`);

      // En un entorno de servidor, necesitaríamos usar una librería como puppeteer o sharp
      // Por ahora, vamos a rechazar SVG con un mensaje más claro
      return {
        success: false,
        error: 'Las imágenes SVG no son soportadas por el diario externo. Por favor, use imágenes en formato JPG, PNG, GIF o WebP.',
        message: 'Formato SVG no soportado'
      };

      // TODO: Implementar conversión real usando sharp o similar
      // const sharp = require('sharp');
      // const svgBuffer = await fetch(svgUrl).then(r => r.arrayBuffer());
      // const pngBuffer = await sharp(Buffer.from(svgBuffer)).png().toBuffer();
      // return { success: true, data: new Blob([pngBuffer], { type: 'image/png' }) };

    } catch (error) {
      console.error('❌ Error converting SVG:', error);
      return {
        success: false,
        error: 'Error al procesar imagen SVG',
        message: 'Error en conversión de imagen'
      };
    }
  }
}

/**
 * Factory para crear instancias del servicio
 */
export function createExternalPublicationService(diario: DiarioExterno): ExternalPublicationService {
  return new ExternalPublicationService(diario);
}

/**
 * Utilidades para manejo de errores
 */
export class PublicationError extends Error {
  constructor(
    message: string,
    public step: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'PublicationError';
  }
}

/**
 * Timeout wrapper para requests
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number = 30000
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
  });

  return Promise.race([promise, timeoutPromise]);
}
