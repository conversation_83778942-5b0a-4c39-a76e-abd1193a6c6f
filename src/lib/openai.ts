import OpenAI from 'openai';

// Configuración de OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key-here',
});

export interface RewriteRequest {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  prompt: string;
  diarioNombre: string;
}

export interface RewriteResponse {
  titulo: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  metadatos: {
    modelo: string;
    tokens_usados: number;
    tiempo_generacion: number;
    diario: string;
  };
}

export async function rewriteNoticia(request: RewriteRequest): Promise<RewriteResponse> {
  const startTime = Date.now();

  try {
    // Construir el prompt completo
    const fullPrompt = `${request.prompt}

NOTICIA ORIGINAL:
Volanta: ${request.volanta || 'Sin volanta'}
Título: ${request.titulo}
Subtítulo: ${request.subtitulo || 'Sin subtítulo'}
Resumen: ${request.resumen || 'Sin resumen'}
Contenido: ${request.contenido}

INSTRUCCIONES CRÍTICAS:
- Sigue EXACTAMENTE las instrucciones del prompt personalizado del diario
- Si el prompt pide HTML, entrega HTML puro dentro del JSON
- Si el prompt pide formato específico, respétalo completamente
- NO generes subtítulo, solo volanta, título, resumen y contenido
- El contenido debe seguir el formato solicitado en el prompt del diario
- Responde ÚNICAMENTE en formato JSON con esta estructura exacta:
{
  "volanta": "nueva volanta",
  "titulo": "nuevo título",
  "resumen": "nuevo resumen",
  "contenido": "contenido en el formato solicitado por el prompt del diario"
}

No incluyas explicaciones adicionales, solo el JSON.`;

    const model = "gpt-3.5-turbo";

    // Prepare the request parameters based on the model
    const requestParams: any = {
      model,
      messages: [
        {
          role: "system",
          content: "Eres un periodista experto en reescritura de noticias. Siempre respondes en formato JSON válido sin texto adicional."
        },
        {
          role: "user",
          content: fullPrompt
        }
      ],
    };

    // Use the correct temperature based on the model
    if (!requiresDefaultTemperature(model)) {
      requestParams.temperature = 0.7;
    }

    // Use the correct token parameter based on the model
    if (usesMaxCompletionTokens(model)) {
      requestParams.max_completion_tokens = 2000;
    } else {
      requestParams.max_tokens = 2000;
    }

    const completion = await openai.chat.completions.create(requestParams);

    const responseText = completion.choices[0]?.message?.content;
    if (!responseText) {
      throw new Error('No se recibió respuesta de OpenAI');
    }

    // Intentar parsear la respuesta JSON
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
    } catch (parseError) {
      // Si falla el parsing, intentar extraer JSON del texto
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedResponse = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('La respuesta de OpenAI no está en formato JSON válido');
      }
    }

    const endTime = Date.now();
    const generationTime = endTime - startTime;

    return {
      titulo: parsedResponse.titulo || request.titulo,
      volanta: parsedResponse.volanta,
      contenido: parsedResponse.contenido || request.contenido,
      resumen: parsedResponse.resumen,
      metadatos: {
        modelo: completion.model,
        tokens_usados: completion.usage?.total_tokens || 0,
        tiempo_generacion: generationTime,
        diario: request.diarioNombre,
      }
    };

  } catch (error) {
    console.error('Error en rewriteNoticia:', error);
    throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }
}

/**
 * Determines if a model uses the new max_completion_tokens parameter
 */
function usesMaxCompletionTokens(model: string): boolean {
  // Models that require max_completion_tokens instead of max_tokens
  const newModels = [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-4o-2024',
    'gpt-5',        // GPT-5 series
    'o1-preview',
    'o1-mini',
    'chatgpt-4o-latest'
  ];

  // Also check for any model with "2024" or "2025" in the name (likely newer models)
  const hasNewYear = model.includes('2024') || model.includes('2025');

  return newModels.some(newModel => model.includes(newModel)) || hasNewYear;
}

/**
 * Determines if a model requires default temperature (1) and cannot use custom temperature
 */
function requiresDefaultTemperature(model: string): boolean {
  // Models that only support temperature: 1 (default)
  const restrictedModels = [
    'gpt-5',        // GPT-5 series
    'o1-preview',   // o1 series
    'o1-mini'
  ];

  // Also check for any model with "2025" in the name (likely newer restricted models)
  const hasNewYear = model.includes('2025');

  return restrictedModels.some(restrictedModel => model.includes(restrictedModel)) || hasNewYear;
}

export async function testOpenAIConnection(): Promise<boolean> {
  try {
    const model = "gpt-3.5-turbo"; // Safe fallback model

    // Prepare the request parameters based on the model
    const requestParams: any = {
      model,
      messages: [{ role: "user", content: "Responde solo con 'OK'" }],
    };

    // Use the correct temperature based on the model
    if (!requiresDefaultTemperature(model)) {
      requestParams.temperature = 0;
    }

    // Use the correct token parameter based on the model
    if (usesMaxCompletionTokens(model)) {
      requestParams.max_completion_tokens = 5;
    } else {
      requestParams.max_tokens = 5;
    }

    const completion = await openai.chat.completions.create(requestParams);

    return completion.choices[0]?.message?.content?.includes('OK') || false;
  } catch (error) {
    console.error('Error testing OpenAI connection:', error);
    return false;
  }
}