export interface Correccion {
  id: string;
  titulo: string;
  contenido: string;
  medio: string;
  fechaPublicacion: Date;
  fechaCorreccion: Date;
  estado: 'PENDIENTE' | 'EN_REVISION' | 'COMPLETADA' | 'RECHAZADA';
  prioridad: 'BAJA' | 'MEDIA' | 'ALTA' | 'URGENTE';
  imagenUrl?: string;
  observaciones?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

export interface CreateCorreccionData {
  titulo: string;
  contenido: string;
  medio: string;
  fechaPublicacion: Date;
  prioridad: 'BAJA' | 'MEDIA' | 'ALTA' | 'URGENTE';
  observaciones?: string;
}

export interface UpdateCorreccionData {
  titulo?: string;
  contenido?: string;
  medio?: string;
  fechaPublicacion?: Date;
  estado?: 'PENDIENTE' | 'EN_REVISION' | 'COMPLETADA' | 'RECHAZADA';
  prioridad?: 'BAJA' | 'MEDIA' | 'ALTA' | 'URGENTE';
  observaciones?: string;
}

export interface CorreccionFilters {
  estado?: string;
  prioridad?: string;
  medio?: string;
  fechaDesde?: string;
  fechaHasta?: string;
  search?: string;
} 