// ========================================
// TIPOS PARA SISTEMA DE PUBLICACIÓN EXTERNA
// ========================================

import { EstadoPublicacion } from '@prisma/client';

// Tipos para Diarios Externos
export interface DiarioExterno {
  id: number;
  nombre: string;
  urlBase: string;
  bearerToken: string;
  categoriaImagenId: number;
  endpointImagen: string;
  endpointCategoria: string;
  endpointArticulo: string;
  configuracion?: string;
  descripcion?: string;
  activo: boolean;
  createdAt: Date;
  updatedAt: Date;
  categoriaMapeos?: CategoriaMapeo[];
  publicacionesExternas?: PublicacionExterna[];
}

export interface DiarioExternoInput {
  nombre: string;
  urlBase: string;
  bearerToken: string;
  categoriaImagenId: number;
  endpointImagen?: string;
  endpointCategoria?: string;
  endpointArticulo?: string;
  configuracion?: DiarioExternoConfig;
  descripcion?: string;
  activo?: boolean;
}

// Configuración específica del diario externo
export interface DiarioExternoConfig {
  // Configuración de imagen
  imagenConfig?: {
    maxSize?: number; // MB
    allowedFormats?: string[];
    compression?: boolean;
  };
  // Configuración de categorías
  categoriaConfig?: {
    autoSync?: boolean;
    parentCategoryId?: number;
  };
  // Configuración de artículos
  articuloConfig?: {
    autoPublish?: boolean;
    requiresApproval?: boolean;
    defaultCategories?: number[];
  };
}

// Tipos para Mapeo de Categorías
export interface CategoriaMapeo {
  id: number;
  diarioExternoId: number;
  categoriaLocalId: number;
  categoriaExternaId: number;
  createdAt: Date;
  diarioExterno?: DiarioExterno;
  categoriaLocal?: {
    id: number;
    nombre: string;
    color: string;
  };
}

export interface CategoriaMapeoInput {
  diarioExternoId: number;
  categoriaLocalId: number;
  categoriaExternaId: number;
}

// Tipos para Publicaciones Externas
export interface PublicacionExterna {
  id: number;
  noticiaId: number;
  diarioExternoId: number;
  imagenExternaId?: number;
  articuloExternoId?: number;
  urlPublicacion?: string;
  estado: EstadoPublicacion;
  errorMensaje?: string;
  metadatos?: string;
  createdAt: Date;
  updatedAt: Date;
  noticia?: {
    id: number;
    titulo: string;
  };
  diarioExterno?: DiarioExterno;
}

// Tipos para APIs Externas
export interface CategoriaExterna {
  id: number;
  name: string;
  parent_id?: number;
  description?: string;
}

export interface ImagenExternaResponse {
  id?: number; // Para compatibilidad hacia atrás
  image_id: number; // Campo real de Del Sur
  url?: string;
  message?: string;
  code?: number;
  image_type?: string;
  payload?: any;
}

export interface ArticuloExternoResponse {
  id: number;
  url?: string;
  message?: string;
  slug?: string;
}

export interface ImagenExternaRequest {
  title: string;
  summary: string;
  date: string; // yyyy-mm-dd hh:mm:ss
  category_id: number;
  file: File | Blob;
}

export interface ArticuloExternoRequest {
  header: string;
  created_at: string; // yyyy-mm-dd hh:mm:ss
  deferred_publication: boolean;
  publish: boolean;
  title: string;
  summary: string;
  content: string;
  images: number[];
  categories: number[];
}

// Tipos para el Wizard de Publicación
export interface PublicationWizardStep {
  step: number;
  title: string;
  description: string;
  completed: boolean;
  error?: string;
}

export interface PublicationWizardData {
  diarioExternoId?: number;
  categoriasMapeadas: number[];
  imagenExternaId?: number;
  articuloExternoId?: number;
  urlPublicacion?: string;
}

// Tipos para Validación de Conexión
export interface ConexionTestResult {
  success: boolean;
  message: string;
  details?: {
    categorias?: boolean;
    autenticacion?: boolean;
    conectividad?: boolean;
  };
}

// Tipos para Respuestas de API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Estados del Wizard
export type WizardStep = 'select-diario' | 'confirm-categories' | 'publish' | 'result';

// Tipos para Logs de Publicación
export interface PublicationLog {
  step: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  timestamp: Date;
  details?: any;
}

// Tipos para Estadísticas
export interface PublicationStats {
  total: number;
  exitosas: number;
  fallidas: number;
  pendientes: number;
  porDiario: {
    [diarioId: number]: {
      nombre: string;
      total: number;
      exitosas: number;
      fallidas: number;
    };
  };
}

// Tipos para Configuración de Diario
export interface DiarioConfig {
  id: number;
  nombre: string;
  urlBase: string;
  activo: boolean;
  categoriaImagenId: number;
  totalMapeos: number;
  ultimaPublicacion?: Date;
}

// Tipos para Importación de Categorías
export interface ImportCategoriesResult {
  success: boolean;
  imported: number;
  skipped: number;
  errors: string[];
  categories: CategoriaExterna[];
}
