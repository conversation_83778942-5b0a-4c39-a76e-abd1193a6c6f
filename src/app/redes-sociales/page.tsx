'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ExternalLink, Calendar, User, Tag, Share2, Eye, Copy, CheckCircle, Bug, Loader2 } from 'lucide-react';
import AppHeader from '@/components/AppHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { PublishToSocialButton } from '@/components/social-media/PublishToSocialButton';

interface PublicacionExterna {
  id: number;
  noticiaId: number;
  noticiaTitulo: string;
  categoria: string;
  categoriaColor: string;
  autor: string;
  diario: {
    id: number;
    nombre: string;
    urlBase: string;
    activo: boolean;
  };
  estado: string;
  urlPublicacion: string;
  fechaPublicacion: string;
  fechaCreacion: string;
  tituloPublicado?: string;
  imagenPublicadaUrl?: string;
  // Campos de versión
  esVersion?: boolean;
  versionId?: number;
  versionDiario?: string;
  // Campos para el componente de redes sociales
  noticia?: {
    titulo: string;
    contenido: string;
    imagenUrl?: string;
  };
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function RedesSocialesPage() {
  const router = useRouter();
  const [publicaciones, setPublicaciones] = useState<PublicacionExterna[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [filtros, setFiltros] = useState({
    busqueda: '',
    diario: '',
    categoria: ''
  });
  const [diarios, setDiarios] = useState<Array<{id: number, nombre: string}>>([]);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  // Estados de estado limpiados - solo los esenciales

  // Funciones de test eliminadas - interfaz limpia

  // Cargar publicaciones exitosas
  const cargarPublicaciones = async (page = 1) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        estado: 'EXITOSO' // Solo publicaciones exitosas
      });

      if (filtros.diario) {
        params.append('diarioId', filtros.diario);
      }

      const response = await fetch(`/api/publicaciones-externas?${params}`);
      const data = await response.json();

      if (data.success) {
        let publicacionesFiltradas = data.data;

        // Filtrar por búsqueda local
        if (filtros.busqueda) {
          publicacionesFiltradas = publicacionesFiltradas.filter((pub: PublicacionExterna) =>
            pub.noticiaTitulo.toLowerCase().includes(filtros.busqueda.toLowerCase()) ||
            pub.autor.toLowerCase().includes(filtros.busqueda.toLowerCase())
          );
        }

        // Filtrar por categoría local
        if (filtros.categoria) {
          publicacionesFiltradas = publicacionesFiltradas.filter((pub: PublicacionExterna) =>
            pub.categoria.toLowerCase().includes(filtros.categoria.toLowerCase())
          );
        }

        setPublicaciones(publicacionesFiltradas);
        setPagination(data.pagination);
      } else {
        toast.error('Error al cargar publicaciones');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar publicaciones');
    } finally {
      setLoading(false);
    }
  };

  // Cargar diarios disponibles
  const cargarDiarios = async () => {
    try {
      const response = await fetch('/api/diarios-externos');
      const data = await response.json();
      if (Array.isArray(data)) {
        setDiarios(data.filter((d: any) => d.activo));
      }
    } catch (error) {
      console.error('Error al cargar diarios:', error);
    }
  };

  // Funciones de debug, test y metadatos eliminadas - interfaz limpia

  useEffect(() => {
    cargarPublicaciones();
    cargarDiarios();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      cargarPublicaciones(1);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filtros]);

  // Copiar URL al portapapeles
  const copiarUrl = async (url: string, titulo: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(url);
      toast.success(`Enlace de "${titulo}" copiado al portapapeles`);
      
      setTimeout(() => {
        setCopiedUrl(null);
      }, 2000);
    } catch (error) {
      toast.error('Error al copiar enlace');
    }
  };

  // Abrir enlace en nueva pestaña
  const abrirEnlace = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Formatear fecha
  const formatearFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <AppHeader
        title="Redes Sociales"
        subtitle="Noticias publicadas exitosamente en diarios externos"
        showBackButton={true}
        backUrl="/dashboard"
        backText="Volver al Dashboard"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header con estadísticas */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {pagination && (
                <span>
                  {publicaciones.length} de {pagination.total} publicaciones
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Filtros */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Input
              placeholder="Buscar por título o autor..."
              value={filtros.busqueda}
              onChange={(e) => setFiltros(prev => ({ ...prev, busqueda: e.target.value }))}
              className="w-full"
            />
          </div>
          <div>
            <select
              value={filtros.diario}
              onChange={(e) => setFiltros(prev => ({ ...prev, diario: e.target.value }))}
              className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <option value="">Filtrar por diario</option>
              {diarios.map((diario) => (
                <option key={diario.id} value={diario.id.toString()}>
                  {diario.nombre}
                </option>
              ))}
            </select>
          </div>
          <div>
            <Input
              placeholder="Filtrar por categoría..."
              value={filtros.categoria}
              onChange={(e) => setFiltros(prev => ({ ...prev, categoria: e.target.value }))}
              className="w-full"
            />
          </div>
        </div>

        {/* Loading */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}

        {/* Grid de Publicaciones */}
        {!loading && (
          <>
            {publicaciones.length === 0 ? (
              <div className="text-center py-12">
                <Share2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  No hay publicaciones exitosas
                </h3>
                <p className="text-muted-foreground mb-4">
                  No se encontraron noticias publicadas con los filtros aplicados.
                </p>
                {/* Botones de test eliminados - interfaz limpia */}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {publicaciones.map((publicacion) => (
                  <Card key={publicacion.id} className="hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <span
                          className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold mb-2"
                          style={{ backgroundColor: publicacion.categoriaColor + '20', color: publicacion.categoriaColor }}
                        >
                          <Tag className="h-3 w-3 mr-1" />
                          {publicacion.categoria}
                        </span>
                        <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold text-green-600 border-green-600">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Exitoso
                        </span>
                      </div>
                      <CardTitle className="text-sm font-semibold leading-tight" style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}>
                        {publicacion.tituloPublicado || publicacion.noticiaTitulo}
                      </CardTitle>

                      {/* Indicador de versión IA */}
                      {publicacion.esVersion && (
                        <div className="mt-2">
                          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            🤖 Versión IA
                            {publicacion.versionDiario && (
                              <span className="ml-1">• {publicacion.versionDiario}</span>
                            )}
                          </span>
                        </div>
                      )}
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      {/* Imagen si existe */}
                      {publicacion.imagenPublicadaUrl && (
                        <div className="mb-3">
                          <img
                            src={publicacion.imagenPublicadaUrl}
                            alt={publicacion.noticiaTitulo}
                            className="w-full h-32 object-cover rounded-md"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </div>
                      )}

                      {/* Información del diario */}
                      <div className="flex items-center mb-3">
                        <div className="h-6 w-6 bg-primary/10 rounded mr-2 flex items-center justify-center">
                          <span className="text-xs font-bold text-primary">
                            {publicacion.diario.nombre.charAt(0)}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-foreground">
                          {publicacion.diario.nombre}
                        </span>
                        {!publicacion.diario.activo && (
                          <span className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-600 rounded">
                            Inactivo
                          </span>
                        )}
                      </div>

                      {/* Metadatos */}
                      <div className="space-y-2 text-xs text-muted-foreground mb-4">
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {publicacion.autor}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatearFecha(publicacion.fechaPublicacion)}
                        </div>
                      </div>

                      {/* Botones de acción */}
                      <div className="space-y-2">
                        {/* Primera fila de botones */}
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() => abrirEnlace(publicacion.urlPublicacion)}
                            className="flex-1"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Ver
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copiarUrl(publicacion.urlPublicacion, publicacion.noticiaTitulo)}
                            className="px-3"
                          >
                            {copiedUrl === publicacion.urlPublicacion ? (
                              <CheckCircle className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => abrirEnlace(publicacion.urlPublicacion)}
                            className="px-3"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>

                        {/* Segunda fila: Botones de Redes Sociales */}
                        <div className="space-y-2">
                          <PublishToSocialButton
                            publicacion={{
                              id: publicacion.id,
                              urlPublicacion: publicacion.urlPublicacion,
                              noticia: {
                                titulo: publicacion.tituloPublicado || publicacion.noticiaTitulo,
                                contenido: `Noticia publicada en ${publicacion.diario.nombre}`, // Contenido básico
                                imagenUrl: publicacion.imagenPublicadaUrl
                              }
                            }}
                            onPublishSuccess={() => {
                              toast.success('¡Publicado en redes sociales!');
                              // Opcional: recargar datos
                              cargarPublicaciones(pagination?.page || 1);
                            }}
                            variant="outline"
                            size="sm"
                            className="w-full"
                          />

                          {/* Botones de test eliminados - interfaz limpia */}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Paginación */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2 mt-8">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => cargarPublicaciones(pagination.page - 1)}
                  disabled={!pagination.hasPrev}
                >
                  Anterior
                </Button>
                <span className="text-sm text-muted-foreground">
                  Página {pagination.page} de {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => cargarPublicaciones(pagination.page + 1)}
                  disabled={!pagination.hasNext}
                >
                  Siguiente
                </Button>
              </div>
            )}
          </>
        )}

        {/* Sección de debug eliminada - interfaz limpia */}
      </div>
    </div>
  );
}
