'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Tag,
  Globe,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Clock
} from 'lucide-react';
import AppHeader from '@/components/AppHeader';
import { NoticiaExternaMapeada } from '@/lib/external-db';

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface ApiResponse {
  success: boolean;
  data: {
    noticias: NoticiaExternaMapeada[];
    pagination: PaginationInfo;
  };
  error?: string;
}

interface EstadoImportacion {
  yaImportada: boolean;
  noticiaId?: number;
  fechaImportacion?: Date;
  importadaPor?: string;
}

interface EstadosImportacionResponse {
  success: boolean;
  data: {
    estados: Record<number, EstadoImportacion>;
    totalConsultados: number;
    yaImportados: number;
  };
  error?: string;
}

export default function NoticiasNacionalesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Estados
  const [noticias, setNoticias] = useState<NoticiaExternaMapeada[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [importando, setImportando] = useState<Set<number>>(new Set());
  const [importacionExitosa, setImportacionExitosa] = useState<Set<number>>(new Set());
  const [estadosImportacion, setEstadosImportacion] = useState<Record<number, EstadoImportacion>>({});
  const [verificandoEstados, setVerificandoEstados] = useState(false);
  const [modoDemo, setModoDemo] = useState(false);

  // Generar datos de demostración
  const generarDatosDemo = (): NoticiaExternaMapeada[] => {
    return [
      {
        id: 1,
        titulo: "Los videos de los graves incidentes de los hinchas de la U de Chile que interrumpieron el duelo ante Independiente",
        contenido: "Los hinchas de Universidad de Chile protagonizaron graves incidentes...",
        resumen: "Incidentes graves en el partido Universidad de Chile vs Independiente",
        autor: "Infobae",
        categoria: "Deportes",
        fecha_publicacion: new Date('2025-08-21'),
        url_original: "https://www.infobae.com/deportes/2025/08/21/los-videos-de-los-graves-incidentes-de-los-hinchas-de-la-u-de-chile-que-interrumpieron-el-duelo-ante-independiente/",
        imagen_url: undefined,
        fuente: "Infobae",
        created_at: new Date('2025-08-21')
      },
      {
        id: 1002,
        titulo: "Cambio climático: Nuevas políticas ambientales en América Latina",
        contenido: "Los gobiernos de la región implementan medidas urgentes...",
        resumen: "Políticas ambientales urgentes en Latinoamérica",
        autor: "Carlos Rodríguez",
        categoria: "Medio Ambiente",
        fecha_publicacion: new Date('2024-01-14'),
        url_original: "https://ejemplo.com/noticia-1002",
        imagen_url: undefined,
        fuente: "EcoNews",
        created_at: new Date('2024-01-14')
      },
      {
        id: 1003,
        titulo: "Economía global: Perspectivas para el segundo trimestre",
        contenido: "Análisis económico revela tendencias prometedoras...",
        resumen: "Análisis económico del segundo trimestre",
        autor: "Ana Martínez",
        categoria: "Economía",
        fecha_publicacion: new Date('2024-01-13'),
        url_original: "https://ejemplo.com/noticia-1003",
        imagen_url: undefined,
        fuente: "FinanceDaily",
        created_at: new Date('2024-01-13')
      }
    ];
  };

  // Activar modo demo
  const activarModoDemo = async () => {
    console.log('🎯 BOTÓN "VER DEMO" CLICKEADO - INICIANDO MODO DEMO');
    setModoDemo(true);
    setError(null);
    const datosDemo = generarDatosDemo();
    console.log('📋 Datos demo generados:', datosDemo.length, 'noticias');
    setNoticias(datosDemo);
    setPagination({
      currentPage: 1,
      totalPages: 1,
      totalItems: datosDemo.length,
      itemsPerPage: 12,
      hasNextPage: false,
      hasPrevPage: false
    });

    console.log('🔄 Modo demo activado, verificando estados de importación...');
    // Verificar estados de importación reales para los datos demo
    try {
      await verificarEstadosImportacion(datosDemo);
      console.log('✅ Estados de importación verificados correctamente');
    } catch (error) {
      console.error('❌ Error al verificar estados de importación:', error);
    }
    console.log('✅ Modo demo completamente configurado');
  };

  // Filtros
  const [filtros, setFiltros] = useState({
    busqueda: '',
    fechaDesde: '',
    fechaHasta: '',
    categoria: '',
    autor: '',
    page: 1,
    limit: 12
  });

  // Redireccionar si no está autenticado
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
    }
  }, [session, status, router]);

  // Verificar estados de importación
  const verificarEstadosImportacion = async (noticiasParaVerificar: NoticiaExternaMapeada[]) => {
    if (noticiasParaVerificar.length === 0) {
      console.log('No hay noticias para verificar estados de importación');
      return;
    }

    try {
      console.log('🔍 FUNCIÓN verificarEstadosImportacion INICIADA');
      console.log('📋 Noticias recibidas para verificar:', noticiasParaVerificar.length);
      setVerificandoEstados(true);

      const idsExternos = noticiasParaVerificar.map(noticia => noticia.id).filter(id => id !== null && id !== undefined);
      console.log('📋 IDs externos extraídos:', idsExternos);

      if (idsExternos.length === 0) {
        console.log('No hay IDs válidos para verificar');
        return;
      }

      const response = await fetch('/api/noticias-nacionales/estado-importacion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ idsExternos }),
      });

      const data: EstadosImportacionResponse = await response.json();

      console.log('🔍 Respuesta del endpoint de estados:', data);
      console.log('🔍 IDs consultados:', idsExternos);

      if (response.ok && data.success) {
        console.log('✅ Estados recibidos del servidor:', data.data.estados);
        setEstadosImportacion(data.data.estados);
        console.log(`✅ Estados de importación verificados para ${idsExternos.length} noticias`);

        // Log del estado actual después de actualizar
        console.log('📊 Estado actual de estadosImportacion después de actualizar:', data.data.estados);

        // Forzar re-render para asegurar que los botones se actualicen
        setTimeout(() => {
          console.log('🔄 Forzando re-render de componentes...');
        }, 100);
      } else {
        console.warn('❌ Error al verificar estados de importación:', data.error);
        // No mostrar error al usuario, solo log interno
      }

    } catch (error) {
      console.warn('Error al verificar estados de importación:', error);
      // No mostrar error al usuario, solo log interno
    } finally {
      setVerificandoEstados(false);
    }
  };

  // Cargar noticias
  const cargarNoticias = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(filtros).forEach(([key, value]) => {
        if (value) {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/noticias-nacionales?${params}`);
      const data: ApiResponse = await response.json();

      if (!response.ok) {
        // Manejar errores específicos de conexión externa
        if (response.status === 503 && data.error?.includes('base de datos externa')) {
          setError('No se puede conectar a la base de datos externa. Por favor, verifica la configuración de conexión.');
          setNoticias([]);
          setPagination(null);
          return;
        }
        throw new Error(data.error || 'Error al cargar noticias');
      }

      if (data.success) {
        setNoticias(data.data.noticias || []);
        setPagination(data.data.pagination);

        // Verificar estados de importación después de cargar las noticias REALES
        // Solo si hay noticias cargadas
        if (data.data.noticias && data.data.noticias.length > 0) {
          console.log('🔄 Iniciando verificación de estados de importación para', data.data.noticias.length, 'noticias REALES');
          console.log('📋 Estructura de la primera noticia:', data.data.noticias[0]);

          // Activar indicador de verificación
          setVerificandoEstados(true);

          try {
            await verificarEstadosImportacion(data.data.noticias);
            console.log('✅ Verificación de estados completada exitosamente para noticias REALES');
          } catch (error) {
            console.error('❌ Error en verificación de estados:', error);
          } finally {
            // Desactivar indicador de verificación
            setVerificandoEstados(false);
          }
        } else {
          console.log('⚠️ No hay noticias para verificar estados de importación');
        }
      } else {
        throw new Error(data.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error al cargar noticias:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

      // Proporcionar mensajes de error más específicos
      if (errorMessage.includes('fetch')) {
        setError('Error de conexión. Por favor, verifica tu conexión a internet.');
      } else if (errorMessage.includes('timeout')) {
        setError('Tiempo de espera agotado. La base de datos externa no responde.');
      } else {
        setError(errorMessage);
      }

      setNoticias([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  // Cargar noticias al montar el componente y cuando cambien los filtros (solo si no está en modo demo)
  useEffect(() => {
    if (session && !modoDemo) {
      cargarNoticias();
    }
  }, [session, filtros, modoDemo]);

  // Manejar cambio de página
  const cambiarPagina = (nuevaPagina: number) => {
    setFiltros(prev => ({ ...prev, page: nuevaPagina }));
  };

  // Manejar búsqueda
  const manejarBusqueda = (e: React.FormEvent) => {
    e.preventDefault();
    setFiltros(prev => ({ ...prev, page: 1 }));
  };

  // Importar noticia
  const importarNoticia = async (noticiaId: number) => {
    // Verificar si ya está importada
    const estadoImportacion = estadosImportacion[noticiaId];
    if (estadoImportacion?.yaImportada) {
      alert(`Esta noticia ya fue importada anteriormente por ${estadoImportacion.importadaPor}`);
      return;
    }

    // Manejar modo demo
    if (modoDemo) {
      setImportando(prev => new Set([...prev, noticiaId]));

      // Simular importación en modo demo
      setTimeout(() => {
        setImportacionExitosa(prev => new Set([...prev, noticiaId]));
        setEstadosImportacion(prev => ({
          ...prev,
          [noticiaId]: {
            yaImportada: true,
            noticiaId: Math.floor(Math.random() * 1000) + 300,
            fechaImportacion: new Date(),
            importadaPor: session?.user?.name || 'Usuario Demo'
          }
        }));

        setImportando(prev => {
          const newSet = new Set(prev);
          newSet.delete(noticiaId);
          return newSet;
        });

        alert('¡Importación simulada exitosa! En modo real, serías redirigido a la página de edición.');
      }, 2000);

      return;
    }

    try {
      setImportando(prev => new Set([...prev, noticiaId]));

      const response = await fetch(`/api/noticias-nacionales/${noticiaId}/importar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al importar noticia');
      }

      if (data.success) {
        setImportacionExitosa(prev => new Set([...prev, noticiaId]));

        // Actualizar el estado de importación
        setEstadosImportacion(prev => ({
          ...prev,
          [noticiaId]: {
            yaImportada: true,
            noticiaId: data.data.noticia.id,
            fechaImportacion: new Date(),
            importadaPor: session?.user?.name || 'Usuario actual'
          }
        }));

        // Redirigir a la página de edición después de 2 segundos
        setTimeout(() => {
          router.push(`/noticias/${data.data.noticia.id}/editar`);
        }, 2000);
      }

    } catch (error) {
      console.error('Error al importar noticia:', error);
      alert(error instanceof Error ? error.message : 'Error al importar noticia');
    } finally {
      setImportando(prev => {
        const newSet = new Set(prev);
        newSet.delete(noticiaId);
        return newSet;
      });
    }
  };

  // Obtener estado del botón de importación
  const obtenerEstadoBotonImportacion = (noticiaId: number) => {
    const estadoImportacion = estadosImportacion[noticiaId];
    const estaImportando = importando.has(noticiaId);
    const importacionExitosaLocal = importacionExitosa.has(noticiaId);

    // Log para diagnosticar
    console.log(`🔍 Verificando estado del botón para noticia ID ${noticiaId}:`);
    console.log(`   - estadoImportacion:`, estadoImportacion);
    console.log(`   - yaImportada:`, estadoImportacion?.yaImportada);
    console.log(`   - estaImportando:`, estaImportando);
    console.log(`   - importacionExitosaLocal:`, importacionExitosaLocal);
    console.log(`   - estadosImportacion completo:`, Object.keys(estadosImportacion).length, 'estados');
    console.log(`   - claves disponibles:`, Object.keys(estadosImportacion));

    if (estaImportando) {
      return {
        disabled: true,
        texto: 'Importando...',
        icono: 'loading',
        className: 'bg-blue-100 text-blue-800 border border-blue-200 cursor-not-allowed'
      };
    }

    if (importacionExitosaLocal) {
      return {
        disabled: true,
        texto: 'Importada',
        icono: 'success',
        className: 'bg-green-100 text-green-800 border border-green-200 cursor-not-allowed'
      };
    }

    if (estadoImportacion?.yaImportada) {
      return {
        disabled: true,
        texto: 'Ya Importada',
        icono: 'imported',
        className: 'bg-orange-100 text-orange-800 border border-orange-200 cursor-not-allowed',
        tooltip: `Importada por ${estadoImportacion.importadaPor} el ${formatearFecha(new Date(estadoImportacion.fechaImportacion!))}`
      };
    }

    return {
      disabled: false,
      texto: 'Importar',
      icono: 'download',
      className: 'bg-primary text-primary-foreground hover:bg-primary/90'
    };
  };

  // Formatear fecha
  const formatearFecha = (fecha: Date) => {
    return new Date(fecha).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <AppHeader 
        title="Noticias Nacionales"
        subtitle="Importar noticias desde fuentes externas"
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header con filtros */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Noticias Nacionales</h1>
              <p className="text-muted-foreground">
                Explora e importa noticias desde fuentes externas
              </p>
            </div>
            
            <button
              onClick={cargarNoticias}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Actualizar</span>
            </button>
          </div>

          {/* Filtros */}
          <div className="bg-card rounded-lg border p-6">
            <form onSubmit={manejarBusqueda} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Búsqueda */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Buscar
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <input
                      type="text"
                      value={filtros.busqueda}
                      onChange={(e) => setFiltros(prev => ({ ...prev, busqueda: e.target.value }))}
                      placeholder="Título o contenido..."
                      className="w-full pl-10 pr-4 py-2 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                    />
                  </div>
                </div>

                {/* Fecha desde */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Desde
                  </label>
                  <input
                    type="date"
                    value={filtros.fechaDesde}
                    onChange={(e) => setFiltros(prev => ({ ...prev, fechaDesde: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>

                {/* Fecha hasta */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Hasta
                  </label>
                  <input
                    type="date"
                    value={filtros.fechaHasta}
                    onChange={(e) => setFiltros(prev => ({ ...prev, fechaHasta: e.target.value }))}
                    className="w-full px-3 py-2 border border-input rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>

                {/* Botón de búsqueda */}
                <div className="flex items-end">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 flex items-center justify-center space-x-2"
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filtrar</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Indicador de verificación de estados */}
        {verificandoEstados && (
          <div className="mb-6 flex items-center space-x-2 text-blue-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm font-medium">Verificando estados de importación...</span>
          </div>
        )}

        {/* Estado de error */}
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <span className="text-destructive font-medium">Error de Conexión</span>
            </div>
            <p className="text-destructive text-sm mb-3">{error}</p>
            {error.includes('base de datos externa') && (
              <div className="text-sm text-muted-foreground">
                <p className="mb-2"><strong>Posibles soluciones:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Verifica que la base de datos externa esté en línea</li>
                  <li>Revisa la configuración de conexión en las variables de entorno</li>
                  <li>Confirma que las credenciales de acceso sean correctas</li>
                  <li>Verifica que no haya restricciones de firewall</li>
                </ul>
              </div>
            )}
            <div className="flex space-x-3">
              <button
                onClick={cargarNoticias}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Reintentar</span>
              </button>
            </div>
          </div>
        )}

        {/* Indicador de modo demo */}
        {modoDemo && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-blue-600" />
              <span className="text-blue-800 font-medium">Modo Demostración</span>
              <span className="text-blue-600 text-sm">- Datos de ejemplo para probar la funcionalidad</span>
            </div>
            <button
              onClick={() => {
                setModoDemo(false);
                cargarNoticias();
              }}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
            >
              Salir del Demo
            </button>
          </div>
        )}

        {/* Estado de verificación de importaciones */}
        {verificandoEstados && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
            <span className="text-blue-800">Verificando estado de importación...</span>
          </div>
        )}

        {/* Estado de carga */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        )}

        {/* Estadísticas de importación */}
        {!loading && noticias.length > 0 && Object.keys(estadosImportacion).length > 0 && (
          <div className="mb-6 p-4 bg-card rounded-lg border">
            <h3 className="text-sm font-medium text-foreground mb-2">Estado de Importación</h3>
            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-muted-foreground">
                  Ya importadas: {Object.values(estadosImportacion).filter(e => e.yaImportada).length}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-muted-foreground">
                  Disponibles: {Object.values(estadosImportacion).filter(e => !e.yaImportada).length}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span className="text-muted-foreground">
                  Total: {noticias.length}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Grid de noticias */}
        {!loading && noticias.length > 0 && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {noticias.map((noticia) => (
                <div key={noticia.id} className="bg-card rounded-lg border shadow-sm hover:shadow-md transition-shadow">
                  {/* Imagen */}
                  {noticia.imagen_url && (
                    <div className="aspect-video w-full overflow-hidden rounded-t-lg">
                      <img
                        src={noticia.imagen_url}
                        alt={noticia.titulo}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div className="p-6">
                    {/* Título */}
                    <h3 className="text-lg font-semibold text-foreground mb-2 line-clamp-2">
                      {noticia.titulo}
                    </h3>

                    {/* Resumen */}
                    {noticia.resumen && (
                      <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                        {noticia.resumen}
                      </p>
                    )}

                    {/* Metadatos */}
                    <div className="flex flex-wrap gap-2 mb-4 text-xs text-muted-foreground">
                      {noticia.fecha_publicacion && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatearFecha(noticia.fecha_publicacion)}</span>
                        </div>
                      )}
                      {noticia.autor && (
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{noticia.autor}</span>
                        </div>
                      )}
                      {noticia.categoria && (
                        <div className="flex items-center space-x-1">
                          <Tag className="h-3 w-3" />
                          <span>{noticia.categoria}</span>
                        </div>
                      )}
                    </div>

                    {/* Botón de importar */}
                    {(() => {
                      const estadoBoton = obtenerEstadoBotonImportacion(Number(noticia.id));
                      return (
                        <div className="relative">
                          <button
                            onClick={() => importarNoticia(Number(noticia.id))}
                            disabled={estadoBoton.disabled}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${estadoBoton.className}`}
                            title={estadoBoton.tooltip}
                          >
                            {estadoBoton.icono === 'loading' && (
                              <>
                                <RefreshCw className="h-4 w-4 animate-spin" />
                                <span>{estadoBoton.texto}</span>
                              </>
                            )}
                            {estadoBoton.icono === 'success' && (
                              <>
                                <CheckCircle className="h-4 w-4" />
                                <span>{estadoBoton.texto}</span>
                              </>
                            )}
                            {estadoBoton.icono === 'imported' && (
                              <>
                                <ExternalLink className="h-4 w-4" />
                                <span>{estadoBoton.texto}</span>
                              </>
                            )}
                            {estadoBoton.icono === 'download' && (
                              <>
                                <Download className="h-4 w-4" />
                                <span>{estadoBoton.texto}</span>
                              </>
                            )}
                          </button>

                          {/* Información adicional para noticias ya importadas */}
                          {estadosImportacion[Number(noticia.id)]?.yaImportada && (
                            <div className="mt-2 text-xs text-muted-foreground flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>
                                Importada por {estadosImportacion[Number(noticia.id)].importadaPor}
                              </span>
                            </div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              ))}
            </div>

            {/* Paginación */}
            {pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Mostrando {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} a{' '}
                  {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} de{' '}
                  {pagination.totalItems} noticias
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => cambiarPagina(pagination.currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className="p-2 border border-input rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>

                  <span className="px-4 py-2 text-sm font-medium">
                    Página {pagination.currentPage} de {pagination.totalPages}
                  </span>

                  <button
                    onClick={() => cambiarPagina(pagination.currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className="p-2 border border-input rounded-lg hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {/* Estado vacío */}
        {!loading && noticias.length === 0 && !error && (
          <div className="text-center py-12">
            <Globe className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              No se encontraron noticias
            </h3>
            <p className="text-muted-foreground">
              Intenta ajustar los filtros de búsqueda o verifica la conexión con la fuente externa.
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
