'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface PageProps {
  params: Promise<{ id: string }>;
}

export default function NoticiaDetailsPage({ params }: PageProps) {
  const router = useRouter();

  useEffect(() => {
    // Redirigir inmediatamente a la página de revisión
    const redirectToRevision = async () => {
      const resolvedParams = await params;
      router.replace(`/noticias/${resolvedParams.id}/revision`);
    };

    redirectToRevision();
  }, [params, router]);

  // Mostrar un loading mientras se redirige
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirigiendo...</p>
      </div>
    </div>
  );
}
