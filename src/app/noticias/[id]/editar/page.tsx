"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { ArrowLeft, Save, Upload, Link } from "lucide-react";

interface FormData {
  volanta: string;
  titulo: string;
  resumen: string;
  contenido: string;
  imagenUrl: string;
  imagenArchivo: File | null;
  categoriaId: string;
}

export default function EditarNoticiaPage() {
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [categorias, setCategorias] = useState<Array<{ id: string; nombre: string }>>([]);
  const [formData, setFormData] = useState<FormData>({
    volanta: "",
    titulo: "",
    resumen: "",
    contenido: "",
    imagenUrl: "",
    imagenArchivo: null,
    categoriaId: "",
  });

  // Cargar categorías y noticia al montar el componente
  useEffect(() => {
    const loadData = async () => {
      try {
        // Cargar categorías
        const categoriasResponse = await fetch("/api/categorias");
        if (categoriasResponse.ok) {
          const categoriasData = await categoriasResponse.json();
          setCategorias(categoriasData);
        }

        // Cargar noticia
        const noticiaResponse = await fetch(`/api/noticias/${id}`);
        if (noticiaResponse.ok) {
          const noticiaData = await noticiaResponse.json();
          setFormData({
            volanta: noticiaData.volanta || "",
            titulo: noticiaData.titulo || "",
            resumen: noticiaData.resumen || "",
            contenido: noticiaData.contenido || "",
            imagenUrl: noticiaData.imagenUrl || "",
            imagenArchivo: null,
            categoriaId: noticiaData.categoriaId?.toString() || "",
          });
        } else {
          alert("No se pudo cargar la noticia");
          router.push("/noticias");
        }
      } catch (error) {
        console.error("Error cargando datos:", error);
        alert("Error al cargar los datos");
        router.push("/noticias");
      }
    };

    if (session && id) {
      loadData();
    }
  }, [session, id, router]);

  const handleInputChange = (field: keyof FormData, value: string | File | null) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData((prev) => ({ ...prev, imagenArchivo: file }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("volanta", formData.volanta);
      formDataToSend.append("titulo", formData.titulo);
      formDataToSend.append("resumen", formData.resumen);
      formDataToSend.append("contenido", formData.contenido);
      formDataToSend.append("categoriaId", formData.categoriaId);
      
      if (formData.imagenArchivo) {
        formDataToSend.append("imagen", formData.imagenArchivo);
      } else if (formData.imagenUrl) {
        formDataToSend.append("imagenUrl", formData.imagenUrl);
      }

      const response = await fetch(`/api/noticias/${id}`, {
        method: "PUT",
        body: formDataToSend,
      });

      if (response.ok) {
        alert("Noticia actualizada exitosamente");
        router.push(`/noticias/${id}/revision`);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message}`);
      }
    } catch (error) {
      console.error("Error actualizando noticia:", error);
      alert("Error al actualizar la noticia");
    } finally {
      setIsSaving(false);
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Acceso Denegado</h2>
          <p className="text-gray-600">Debes iniciar sesión para editar noticias</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/noticias')}
            className="flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 transition-colors mb-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Volver
          </button>
          <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-100 mb-2">Editar Noticia</h1>
          <p className="text-gray-600 dark:text-gray-400">Modifica los campos para actualizar la noticia</p>
        </div>

        {/* Formulario */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-6xl mx-auto">
          <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Columna izquierda - Campos de texto */}
            <div className="lg:col-span-2 space-y-6">
              {/* Volanta */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Volanta <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.volanta}
                  onChange={(e) => handleInputChange("volanta", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 bg-white dark:bg-gray-700"
                  placeholder="Escribe la volanta de la noticia..."
                  required
                />
              </div>

              {/* Título */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Título <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.titulo}
                  onChange={(e) => handleInputChange("titulo", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-bold text-lg bg-white dark:bg-gray-700"
                  placeholder="Escribe el título de la noticia..."
                  required
                  style={{ fontWeight: 'bold' }}
                />
              </div>

              {/* Resumen */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Resumen
                </label>
                <textarea
                  value={formData.resumen}
                  onChange={(e) => handleInputChange("resumen", e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 resize-none bg-white dark:bg-gray-700"
                  placeholder="Escribe un resumen de la noticia..."
                />
              </div>

              {/* Contenido */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Contenido <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <textarea
                  value={formData.contenido}
                  onChange={(e) => handleInputChange("contenido", e.target.value)}
                  rows={12}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 resize-none bg-white dark:bg-gray-700"
                  placeholder="Escribe el contenido completo de la noticia..."
                  required
                />
              </div>
            </div>

            {/* Columna derecha - Configuración */}
            <div className="space-y-6">
              {/* Categoría */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Categoría <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <select
                  value={formData.categoriaId}
                  onChange={(e) => handleInputChange("categoriaId", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700"
                  required
                >
                  <option value="">Selecciona una categoría</option>
                  {categorias.map((categoria) => (
                    <option key={categoria.id} value={categoria.id}>
                      {categoria.nombre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Subir archivo */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Subir Nueva Imagen
                </label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-blue-400 dark:hover:border-blue-500 transition-colors bg-gray-50 dark:bg-gray-700">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <Upload className="w-8 h-8 text-gray-400 dark:text-gray-500 mx-auto mb-2" />
                    <p className="text-sm text-gray-600 dark:text-gray-300">Haz clic para subir una nueva imagen</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">PNG, JPG, WEBP hasta 5MB</p>
                  </label>
                </div>
                {formData.imagenArchivo && (
                  <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                    ✓ {formData.imagenArchivo.name}
                  </p>
                )}
              </div>

              {/* URL de imagen */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  URL de Imagen
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.imagenUrl}
                    onChange={(e) => handleInputChange("imagenUrl", e.target.value)}
                    className="w-full px-4 py-3 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 bg-white dark:bg-gray-700"
                    placeholder="https://ejemplo.com/imagen.jpg"
                  />
                  <Link className="w-5 h-5 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                </div>
                {formData.imagenUrl && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 break-all">
                    Imagen actual: {formData.imagenUrl}
                  </p>
                )}
              </div>

              {/* Botón de guardar */}
              <div className="pt-6">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="w-full px-8 py-4 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-semibold text-lg"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Guardando...
                    </>
                  ) : (
                    <>
                      <Save className="w-5 h-5 mr-2" />
                      Guardar Cambios
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={() => router.push('/noticias')}
                  className="w-full px-6 py-3 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors mt-3"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 