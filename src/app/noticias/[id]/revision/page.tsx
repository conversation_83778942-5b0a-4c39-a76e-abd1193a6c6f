'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Bot, CheckCircle, XCircle, Clock, ChevronDown, ChevronUp, FileText, User, Calendar, Zap, ExternalLink, Edit3, RotateCcw, Save, X, PlayCircle, Loader2, AlertCircle, CheckCheck, Sparkles, Wand2, Globe, Image, Bold, Italic, Underline, Quote, AlignLeft, AlignCenter, AlignRight, AlignJustify, Indent, Link, Type, List, ListOrdered } from 'lucide-react';
import ImageUploader from '@/components/ui/ImageUploader';
import ExternalPublicationWizard from '@/components/external-publication/ExternalPublicationWizard';
import PublicacionesExternas from '@/components/external-publication/PublicacionesExternas';
import PublishVersionButton from '@/components/PublishVersionButton';
import { ProgramacionSimple } from '@/components/noticias/ProgramacionSimple';

import { filterExternalDiariosForVersion } from '@/utils/diario-mapping';

interface Noticia {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  estado: string;
  destacada: boolean;
  publicada: boolean;
  fechaPublicacion?: string;
  createdAt: string;
  updatedAt: string;
  categoriaId?: number | null;
  categoria?: {
    id: number;
    nombre: string;
    color: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface Version {
  id: number;
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  imagenUrl?: string;
  estado: string;
  estadoPublicacion?: 'PENDIENTE' | 'PUBLICANDO' | 'PUBLICADA' | 'ERROR';
  urlPublicacion?: string;
  promptUsado?: string;
  metadatos?: any;
  createdAt: string;
  updatedAt: string;
  diario: {
    id: number;
    nombre: string;
    descripcion?: string;
  };
  usuario: {
    id: number;
    name: string;
    email: string;
  };
}

interface Diario {
  id: number;
  nombre: string;
  descripcion?: string;
}

export default function RevisionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const id = Array.isArray(params.id) ? params.id[0] : params.id;

  const [noticia, setNoticia] = useState<Noticia | null>(null);
  const [versiones, setVersiones] = useState<Version[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedVersions, setExpandedVersions] = useState<Set<number>>(new Set());

  // Estados para generación incremental
  const [diarios, setDiarios] = useState<Diario[]>([]);
  const [diariosExternos, setDiariosExternos] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatingDiarios, setGeneratingDiarios] = useState<Set<number>>(new Set());
  const [generationErrors, setGenerationErrors] = useState<Map<number, string>>(new Map());
  const [showQuickActions, setShowQuickActions] = useState(true);

  // Estados para edición de versiones
  const [editingVersion, setEditingVersion] = useState<number | null>(null);
  const [editForm, setEditForm] = useState({
    titulo: '',
    volanta: '',
    resumen: '',
    contenido: '',
    imagenUrl: ''
  });
  const [contentEditorRef, setContentEditorRef] = useState<HTMLDivElement | null>(null);
  const [inlineContentEditorRef, setInlineContentEditorRef] = useState<HTMLDivElement | null>(null);
  const [isRegenerating, setIsRegenerating] = useState<number | null>(null);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);

  // Estado para el wizard de publicación externa
  const [showExternalPublicationWizard, setShowExternalPublicationWizard] = useState(false);

  // Estados para edición inline
  const [isEditingInline, setIsEditingInline] = useState(false);
  const [editedNoticia, setEditedNoticia] = useState<Partial<Noticia>>({});
  const [categorias, setCategorias] = useState([]);
  const [savingChanges, setSavingChanges] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // Cargar categorías para el selector
  useEffect(() => {
    const fetchCategorias = async () => {
      try {
        const response = await fetch('/api/categorias');
        if (response.ok) {
          const data = await response.json();
          setCategorias(data);
        }
      } catch (error) {
        console.error('Error cargando categorías:', error);
      }
    };

    fetchCategorias();
  }, []);

  useEffect(() => {
    if (session && id) {
      loadData();
    }
  }, [session, id]);

  // Efecto para inicializar el contenido del editor cuando se abre (solo una vez)
  useEffect(() => {
    if (contentEditorRef && editingVersion && editForm.contenido) {
      // Solo actualizar si el contenido del editor está vacío o es diferente
      const currentContent = contentEditorRef.innerHTML;
      if (!currentContent || currentContent === '<p>Escribe el contenido de la noticia aquí...</p>' || currentContent === '<p><br></p>') {
        contentEditorRef.innerHTML = editForm.contenido;
      }
    }
  }, [contentEditorRef, editingVersion]); // Removido editForm.contenido de las dependencias

  // Efecto para inicializar el contenido del editor inline (solo una vez)
  useEffect(() => {
    if (inlineContentEditorRef && isEditingInline && editedNoticia.contenido) {
      // Solo actualizar si el contenido del editor está vacío o es diferente
      const currentContent = inlineContentEditorRef.innerHTML;
      if (!currentContent || currentContent === '<p>Escribe el contenido de la noticia aquí...</p>' || currentContent === '<p><br></p>') {
        inlineContentEditorRef.innerHTML = editedNoticia.contenido;
      }
    }
  }, [inlineContentEditorRef, isEditingInline]); // Removido editedNoticia.contenido de las dependencias

  // Funciones para el editor WYSIWYG
  const executeCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
  };

  const formatText = (command: string, value?: string) => {
    executeCommand(command, value);
    // Actualizar el contenido después del comando con setTimeout para preservar cursor
    if (contentEditorRef) {
      setTimeout(() => {
        const content = contentEditorRef.innerHTML;
        setEditForm(prev => ({ ...prev, contenido: content }));
      }, 0);
    }
  };

  const formatInlineText = (command: string, value?: string) => {
    executeCommand(command, value);
    // Actualizar el contenido después del comando con setTimeout para preservar cursor
    if (inlineContentEditorRef) {
      setTimeout(() => {
        const content = inlineContentEditorRef.innerHTML;
        setEditedNoticia(prev => ({ ...prev, contenido: content }));
      }, 0);
    }
  };

  const insertLink = (isInline = false) => {
    const url = prompt('Ingresa la URL del enlace:');
    if (url) {
      if (isInline) {
        formatInlineText('createLink', url);
      } else {
        formatText('createLink', url);
      }
    }
  };

  const toggleCase = (isInline = false) => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      const selectedText = selection.toString();
      const upperText = selectedText.toUpperCase();

      // Guardar el rango de selección
      const range = selection.getRangeAt(0);

      // Eliminar el texto seleccionado e insertar el texto en mayúsculas
      range.deleteContents();
      range.insertNode(document.createTextNode(upperText));

      // Actualizar el contenido
      if (isInline && inlineContentEditorRef) {
        const content = inlineContentEditorRef.innerHTML;
        setEditedNoticia(prev => ({ ...prev, contenido: content }));
      } else if (!isInline && contentEditorRef) {
        const content = contentEditorRef.innerHTML;
        setEditForm(prev => ({ ...prev, contenido: content }));
      }
    }
  };

  const insertQuote = (isInline = false) => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      // Si hay texto seleccionado, convertirlo en cita
      if (isInline) {
        formatInlineText('formatBlock', 'blockquote');
      } else {
        formatText('formatBlock', 'blockquote');
      }
    } else {
      // Si no hay texto seleccionado, insertar una cita vacía
      const blockquote = document.createElement('blockquote');
      blockquote.innerHTML = 'Escribe tu cita aquí...';
      blockquote.style.borderLeft = '4px solid #e5e7eb';
      blockquote.style.paddingLeft = '1rem';
      blockquote.style.fontStyle = 'italic';
      blockquote.style.margin = '1rem 0';

      const range = selection?.getRangeAt(0);
      if (range) {
        range.insertNode(blockquote);

        // Actualizar el contenido
        if (isInline && inlineContentEditorRef) {
          const content = inlineContentEditorRef.innerHTML;
          setEditedNoticia(prev => ({ ...prev, contenido: content }));
        } else if (!isInline && contentEditorRef) {
          const content = contentEditorRef.innerHTML;
          setEditForm(prev => ({ ...prev, contenido: content }));
        }
      }
    }
  };



  // Función para iniciar edición de versión
  const startEditVersion = (version: Version) => {
    setEditingVersion(version.id);
    setEditForm({
      titulo: version.titulo,
      volanta: version.volanta || '',
      resumen: version.resumen || '',
      contenido: version.contenido,
      imagenUrl: version.imagenUrl || ''
    });
  };

  // Función para cancelar edición
  const cancelEdit = () => {
    setEditingVersion(null);
    setEditForm({
      titulo: '',
      volanta: '',
      resumen: '',
      contenido: '',
      imagenUrl: ''
    });
  };

  // Función para generar imagen con IA para la versión
  const generateImageForVersion = async () => {
    if (!editForm.titulo.trim()) {
      alert('Se necesita un título para generar la imagen');
      return;
    }

    setIsGeneratingImage(true);
    try {
      const response = await fetch('/api/ai/generar-imagen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          titulo: editForm.titulo,
          resumen: editForm.resumen,
          contenido: editForm.contenido,
          estilo: 'periodistico',
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al generar imagen');
      }

      if (data.data?.imagenUrl) {
        setEditForm(prev => ({ ...prev, imagenUrl: data.data.imagenUrl }));
      } else {
        throw new Error('No se pudo generar la imagen');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      alert('Error al generar imagen: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Función para guardar cambios de edición
  const saveEditVersion = async (versionId: number) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        throw new Error('Error al actualizar la versión');
      }

      const updatedVersion = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? updatedVersion : v
      ));

      // Limpiar estado de edición
      cancelEdit();

    } catch (error) {
      console.error('Error saving version:', error);
      alert('Error al guardar los cambios');
    }
  };

  // Función para regenerar versión
  const regenerateVersion = async (versionId: number) => {
    try {
      setIsRegenerating(versionId);

      const response = await fetch(`/api/noticias/${id}/versions/${versionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Error al regenerar la versión');
      }

      const regeneratedVersion = await response.json();
      console.log('Versión regenerada:', regeneratedVersion);

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? regeneratedVersion : v
      ));

      alert('✅ Versión regenerada exitosamente');

    } catch (error) {
      console.error('Error regenerating version:', error);
      alert(`❌ Error al regenerar la versión: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    } finally {
      setIsRegenerating(null);
    }
  };

  // Función para generar versión individual
  const handleGenerateSingle = async (diarioId: number) => {
    try {
      setGeneratingDiarios(prev => new Set([...prev, diarioId]));
      setGenerationErrors(prev => {
        const newMap = new Map(prev);
        newMap.delete(diarioId);
        return newMap;
      });

      // Verificar si ya existe una versión para este diario
      const existingVersion = versiones.find(v => v.diario.id === diarioId);

      if (existingVersion) {
        // Si ya existe una versión, regenerarla en lugar de crear una nueva
        console.log(`🔄 Regenerando versión existente para diario ${diarioId}`);

        const response = await fetch(`/api/noticias/${id}/versions/${existingVersion.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Error al regenerar la versión');
        }

        const regeneratedVersion = await response.json();
        console.log('✅ Versión regenerada:', regeneratedVersion);

        // Actualizar la versión existente en el estado
        setVersiones(prev => prev.map(v =>
          v.id === existingVersion.id ? regeneratedVersion : v
        ));

        // Expandir la versión regenerada
        setExpandedVersions(prev => new Set([...prev, existingVersion.id]));

      } else {
        // Si no existe una versión, crear una nueva
        console.log(`✨ Creando nueva versión para diario ${diarioId}`);

        const response = await fetch(`/api/noticias/${id}/generate-versions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            diarioIds: [diarioId]
          }),
        });

        if (!response.ok) {
          throw new Error('Error al generar versión');
        }

        const result = await response.json();

        // Actualizar las versiones con las nuevas generadas
        setVersiones(prev => [...prev, ...result.versiones]);

        // Expandir las nuevas versiones
        const newVersionIds = result.versiones.map((v: Version) => v.id);
        setExpandedVersions(prev => new Set([...prev, ...newVersionIds]));
      }

    } catch (error) {
      console.error('Error generating/regenerating version:', error);
      setGenerationErrors(prev => new Map(prev.set(diarioId, error instanceof Error ? error.message : 'Error desconocido')));
    } finally {
      setGeneratingDiarios(prev => {
        const newSet = new Set(prev);
        newSet.delete(diarioId);
        return newSet;
      });
    }
  };

  // Función para generar todas las versiones pendientes
  const handleGenerateAll = async () => {
    const diariosDisponibles = getDiariosDisponibles();
    if (diariosDisponibles.length === 0) return;

    try {
      setIsGenerating(true);
      setGenerationErrors(new Map());

      const response = await fetch(`/api/noticias/${id}/generate-versions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioIds: diariosDisponibles.map(d => d.id)
        }),
      });

      if (!response.ok) {
        throw new Error('Error al generar versiones');
      }

      const result = await response.json();

      // Actualizar las versiones con las nuevas generadas
      setVersiones(prev => [...prev, ...result.versiones]);

      // Expandir las nuevas versiones
      const newVersionIds = result.versiones.map((v: Version) => v.id);
      setExpandedVersions(prev => new Set([...prev, ...newVersionIds]));

    } catch (error) {
      console.error('Error generating all versions:', error);
      alert('Error al generar versiones: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para regenerar todas las versiones existentes
  const regenerateAllVersions = async () => {
    if (versiones.length === 0) {
      alert('No hay versiones para regenerar');
      return;
    }

    const confirmRegenerate = confirm(
      `¿Estás seguro de que quieres regenerar todas las ${versiones.length} versiones existentes? Esta acción no se puede deshacer.`
    );

    if (!confirmRegenerate) return;

    try {
      setIsGenerating(true);
      setGenerationErrors(new Map());

      const response = await fetch(`/api/noticias/${id}/regenerate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al regenerar versiones');
      }

      const result = await response.json();

      // Recargar las versiones para obtener las actualizadas
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        setVersiones(versionesData.versiones || []);

        // Expandir todas las versiones regeneradas
        if (versionesData.versiones && versionesData.versiones.length > 0) {
          setExpandedVersions(new Set(versionesData.versiones.map((v: Version) => v.id)));
        }
      }

      alert(`✅ ${result.message}`);

    } catch (error) {
      console.error('Error regenerating all versions:', error);
      alert(`❌ Error al regenerar versiones: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    } finally {
      setIsGenerating(false);
    }
  };



  // Funciones para edición inline
  const startInlineEditing = () => {
    setIsEditingInline(true);
    setEditedNoticia({
      titulo: noticia?.titulo || '',
      subtitulo: noticia?.subtitulo || '',
      volanta: noticia?.volanta || '',
      contenido: noticia?.contenido || '',
      resumen: noticia?.resumen || '',
      categoriaId: noticia?.categoria?.id || null,
    });
  };

  const cancelInlineEditing = () => {
    setIsEditingInline(false);
    setEditedNoticia({});
  };

  const saveInlineChanges = async () => {
    if (!noticia || !editedNoticia) return;

    setSavingChanges(true);
    try {
      // Preparar datos completos para la actualización
      const updateData = {
        volanta: editedNoticia.volanta ?? noticia.volanta ?? '',
        titulo: editedNoticia.titulo ?? noticia.titulo,
        subtitulo: editedNoticia.subtitulo ?? noticia.subtitulo ?? '',
        resumen: editedNoticia.resumen ?? noticia.resumen ?? '',
        contenido: editedNoticia.contenido ?? noticia.contenido,
        imagenUrl: editedNoticia.imagenUrl ?? noticia.imagenUrl ?? '',
        categoriaId: editedNoticia.categoriaId ?? noticia.categoria?.id ?? null,
      };

      console.log('Enviando datos de actualización:', updateData);

      const response = await fetch(`/api/noticias/${noticia.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        await loadData(); // Recargar la noticia
        setIsEditingInline(false);
        setEditedNoticia({});
      } else {
        console.error('Error guardando cambios');
      }
    } catch (error) {
      console.error('Error guardando cambios:', error);
    } finally {
      setSavingChanges(false);
    }
  };

  const loadData = async () => {
    try {
      // Cargar noticia
      const noticiaResponse = await fetch(`/api/noticias/${id}`);
      if (noticiaResponse.ok) {
        const noticiaData = await noticiaResponse.json();
        setNoticia(noticiaData);
      }

      // Cargar versiones
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        console.log('Versiones cargadas:', versionesData);
        setVersiones(versionesData.versiones || []);
        // Expandir todas las versiones por defecto
        if (versionesData.versiones && versionesData.versiones.length > 0) {
          setExpandedVersions(new Set(versionesData.versiones.map((v: Version) => v.id)));
        }
      } else {
        console.error('Error al cargar versiones:', versionesResponse.status);
      }

      // Cargar diarios disponibles
      const diariosResponse = await fetch('/api/diarios');
      if (diariosResponse.ok) {
        const diariosData = await diariosResponse.json();
        setDiarios(diariosData.diarios);
      }

      // Cargar diarios externos
      const diariosExternosResponse = await fetch('/api/diarios-externos?activo=true');
      if (diariosExternosResponse.ok) {
        const diariosExternosData = await diariosExternosResponse.json();
        console.log('📰 Diarios externos cargados:', diariosExternosData.data);
        console.log('📊 Cantidad de diarios externos:', diariosExternosData.data?.length || 0);
        setDiariosExternos(diariosExternosData.data || []);
      } else {
        console.error('Error al cargar diarios externos:', diariosExternosResponse.status);
        setDiariosExternos([]);
      }
    } catch (error) {
      console.error('Error al cargar datos:', error);
      alert('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const handleVersionStateChange = async (versionId: number, nuevoEstado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}/versions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ versionId, estado: nuevoEstado }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al cambiar el estado de la versión');
      }

      const result = await response.json();

      // Actualizar la versión en el estado
      setVersiones(prev => prev.map(v =>
        v.id === versionId ? result.version : v
      ));

      alert(`✅ Estado actualizado a: ${nuevoEstado}`);

    } catch (error) {
      console.error('Error changing version state:', error);
      alert(`Error al cambiar el estado de la versión: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  };

  const handleNoticiaStateChange = async (nuevoEstado: string) => {
    try {
      const response = await fetch(`/api/noticias/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ estado: nuevoEstado }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al cambiar el estado de la noticia');
      }

      // Recargar la noticia para obtener el estado actualizado
      await loadData();
      alert(`✅ Noticia ${nuevoEstado.toLowerCase()}`);

    } catch (error) {
      console.error('Error changing noticia state:', error);
      alert(`Error al cambiar el estado de la noticia: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  };

  const toggleVersionExpansion = (versionId: number) => {
    const newExpanded = new Set(expandedVersions);
    if (newExpanded.has(versionId)) {
      newExpanded.delete(versionId);
    } else {
      newExpanded.add(versionId);
    }
    setExpandedVersions(newExpanded);
  };

  const getEstadoBadge = (estado: string) => {
    const badges = {
      'GENERADA': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'APROBADA': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'RECHAZADA': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'EN_REVISION': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };
    return badges[estado as keyof typeof badges] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'RECHAZADA':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Bot className="h-4 w-4 text-blue-600" />;
    }
  };

  // Obtener diarios que ya tienen versiones generadas
  const getDiariosConVersiones = () => {
    return versiones.map(v => v.diario.id);
  };

  // Obtener diarios disponibles para generar (que no tienen versiones)
  const getDiariosDisponibles = () => {
    const diariosConVersiones = getDiariosConVersiones();
    return diarios.filter(d => !diariosConVersiones.includes(d.id));
  };

  // Función para recargar versiones después de una publicación
  const reloadVersions = async () => {
    try {
      const versionesResponse = await fetch(`/api/noticias/${id}/versions`);
      if (versionesResponse.ok) {
        const versionesData = await versionesResponse.json();
        setVersiones(versionesData.versiones || []);
      }
    } catch (error) {
      console.error('Error al recargar versiones:', error);
    }
  };





  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Cargando revisión...</p>
        </div>
      </div>
    );
  }

  if (!noticia) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400">Noticia no encontrada</p>
        </div>
      </div>
    );
  }

  // Verificar permisos
  const canReview = session?.user?.role === 'ADMIN' || session?.user?.role === 'EDITOR';
  const isAuthor = noticia && session?.user && parseInt(session.user.id) === noticia.user.id;
  const canManageVersions = canReview || isAuthor;



  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/noticias')}
                className="flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Volver
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Revisión de Noticia
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Revisa y gestiona las versiones generadas por IA
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                noticia.estado === 'BORRADOR' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                noticia.estado === 'EN_REVISION' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                noticia.estado === 'PUBLICADA' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                {noticia.estado}
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Layout Responsive: Vertical en móvil, Horizontal en desktop */}
        <div className="flex flex-col xl:grid xl:grid-cols-12 gap-6 xl:gap-8">

          {/* Noticia Original - Izquierda en desktop, arriba en móvil */}
          <div className="xl:col-span-7 order-1">
            <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-l-4 border-blue-500 dark:border-blue-400">
              <div className="px-6 py-4 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-100 dark:border-blue-800">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-blue-900 dark:text-blue-100 flex items-center">
                    <FileText className="h-6 w-6 mr-2" />
                    Noticia Original
                  </h2>
                  <div className="flex items-center space-x-4">
                    {/* Botón de Editar Inline */}
                    {canManageVersions && (
                      <div className="flex items-center space-x-3">
                        {/* Selector de Categoría */}
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Categoría:</span>
                          {isEditingInline ? (
                            <select
                              value={editedNoticia.categoriaId || ''}
                              onChange={(e) => setEditedNoticia(prev => ({ ...prev, categoriaId: parseInt(e.target.value) || null }))}
                              className="text-xs border border-blue-300 dark:border-blue-600 rounded px-2 py-1 bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-300"
                            >
                              <option value="">Sin categoría</option>
                              {categorias.map((cat: any) => (
                                <option key={cat.id} value={cat.id}>
                                  {cat.nombre}
                                </option>
                              ))}
                            </select>
                          ) : (
                            <span className="text-xs font-medium px-2 py-1 rounded" style={{
                              backgroundColor: noticia.categoria?.color + '20',
                              color: noticia.categoria?.color || '#6B7280'
                            }}>
                              {noticia.categoria?.nombre || 'Sin categoría'}
                            </span>
                          )}
                        </div>

                        {/* Botón de Editar Inline */}
                        {!isEditingInline ? (
                          <button
                            onClick={startInlineEditing}
                            className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors border border-blue-200 dark:border-blue-700"
                            title="Editar inline"
                          >
                            <Edit3 className="h-4 w-4" />
                            <span className="hidden sm:inline">Editar</span>
                            <span className="sm:hidden">Editar</span>
                          </button>
                        ) : (
                          <div className="flex gap-2">
                            <button
                              onClick={saveInlineChanges}
                              disabled={savingChanges}
                              className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-lg transition-colors disabled:opacity-50"
                            >
                              {savingChanges ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Save className="h-4 w-4" />
                              )}
                              <span className="hidden sm:inline">Guardar</span>
                            </button>
                            <button
                              onClick={cancelInlineEditing}
                              disabled={savingChanges}
                              className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50"
                            >
                              <X className="h-4 w-4" />
                              <span className="hidden sm:inline">Cancelar</span>
                            </button>
                          </div>
                        )}
                      </div>
                    )}

                  </div>
                </div>
              </div>

              <div className="p-6">






                {/* Contenido de la noticia */}
                <div className="space-y-4">
                  <div>
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Volanta</span>
                    {isEditingInline ? (
                      <input
                        type="text"
                        value={editedNoticia.volanta || ''}
                        onChange={(e) => setEditedNoticia(prev => ({ ...prev, volanta: e.target.value }))}
                        className="mt-1 block w-full text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide"
                        placeholder="Volanta (opcional)"
                      />
                    ) : noticia.volanta ? (
                      <p className="text-sm text-blue-600 dark:text-blue-400 font-medium uppercase tracking-wide">
                        {noticia.volanta}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400">Sin volanta</p>
                    )}
                  </div>

                  <div>
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Título</span>
                    {isEditingInline ? (
                      <input
                        type="text"
                        value={editedNoticia.titulo || ''}
                        onChange={(e) => setEditedNoticia(prev => ({ ...prev, titulo: e.target.value }))}
                        className="mt-1 block w-full text-2xl font-bold border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        placeholder="Título de la noticia"
                      />
                    ) : (
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 leading-tight">
                        {noticia.titulo}
                      </h3>
                    )}
                  </div>



                  <div>
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Resumen</span>
                    {isEditingInline ? (
                      <textarea
                        value={editedNoticia.resumen || ''}
                        onChange={(e) => setEditedNoticia(prev => ({ ...prev, resumen: e.target.value }))}
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        placeholder="Resumen de la noticia (opcional)"
                      />
                    ) : noticia.resumen ? (
                      <div
                        className="text-base text-gray-700 dark:text-gray-300 leading-relaxed"
                        dangerouslySetInnerHTML={{ __html: noticia.resumen }}
                      />
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400">Sin resumen</p>
                    )}
                  </div>

                  {noticia.imagenUrl && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Imagen</span>
                      <img
                        src={noticia.imagenUrl}
                        alt={noticia.imagenAlt || noticia.titulo}
                        className="w-full max-w-2xl h-64 object-cover rounded-lg mt-2"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <div>
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Contenido</span>
                    {isEditingInline ? (
                      <div className="mt-1">
                        {/* Barra de herramientas inline */}
                        <div className="border border-gray-300 dark:border-gray-600 rounded-t-md bg-gray-50 dark:bg-gray-700 p-2 flex flex-wrap gap-1">
                          {/* Formato de texto */}
                          <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2 mr-2">
                            <button
                              type="button"
                              onClick={() => formatInlineText('bold')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Negrita (Ctrl+B)"
                            >
                              <Bold size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('italic')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Cursiva (Ctrl+I)"
                            >
                              <Italic size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('underline')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Subrayado (Ctrl+U)"
                            >
                              <Underline size={16} />
                            </button>
                          </div>

                          {/* Alineación */}
                          <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2 mr-2">
                            <button
                              type="button"
                              onClick={() => formatInlineText('justifyLeft')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Alinear a la izquierda"
                            >
                              <AlignLeft size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('justifyCenter')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Centrar"
                            >
                              <AlignCenter size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('justifyRight')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Alinear a la derecha"
                            >
                              <AlignRight size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('justifyFull')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Justificar"
                            >
                              <AlignJustify size={16} />
                            </button>
                          </div>

                          {/* Listas y sangría */}
                          <div className="flex gap-1 border-r border-gray-300 dark:border-gray-600 pr-2 mr-2">
                            <button
                              type="button"
                              onClick={() => formatInlineText('insertUnorderedList')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Lista con viñetas"
                            >
                              <List size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('insertOrderedList')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Lista numerada"
                            >
                              <ListOrdered size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('indent')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Aumentar sangría"
                            >
                              <Indent size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => formatInlineText('outdent')}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Disminuir sangría"
                            >
                              <Indent size={16} className="rotate-180" />
                            </button>
                          </div>

                          {/* Herramientas especiales */}
                          <div className="flex gap-1">
                            <button
                              type="button"
                              onClick={() => insertQuote(true)}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Cita"
                            >
                              <Quote size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => insertLink(true)}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Insertar enlace"
                            >
                              <Link size={16} />
                            </button>
                            <button
                              type="button"
                              onClick={() => toggleCase(true)}
                              className="p-1.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
                              title="Convertir a mayúsculas"
                            >
                              <Type size={16} />
                            </button>
                          </div>
                        </div>

                        <div
                          ref={(el) => {
                            if (el) {
                              setInlineContentEditorRef(el);
                              // Solo inicializar si el editor está realmente vacío
                              const currentContent = el.innerHTML.trim();
                              if (!currentContent ||
                                  currentContent === '<p>Escribe el contenido de la noticia aquí...</p>' ||
                                  currentContent === '<p><br></p>' ||
                                  currentContent === '<br>' ||
                                  currentContent === '') {
                                el.innerHTML = editedNoticia.contenido || '<p>Escribe el contenido de la noticia aquí...</p>';
                              }
                            }
                          }}
                          contentEditable
                          suppressContentEditableWarning={true}
                          data-placeholder="Escribe el contenido de la noticia aquí..."
                          onInput={(e) => {
                            const content = e.currentTarget.innerHTML;
                            // Usar setTimeout para evitar conflictos con el cursor
                            setTimeout(() => {
                              setEditedNoticia(prev => ({ ...prev, contenido: content }));
                            }, 0);
                          }}
                          className="content-editor block w-full min-h-96 border border-gray-300 dark:border-gray-600 border-t-0 rounded-b-md px-4 py-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 prose prose-base max-w-none dark:prose-invert focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          style={{
                            lineHeight: '1.6'
                          }}
                          onKeyDown={(e) => {
                            // Atajos de teclado
                            if (e.ctrlKey || e.metaKey) {
                              switch (e.key) {
                                case 'b':
                                  e.preventDefault();
                                  formatInlineText('bold');
                                  break;
                                case 'i':
                                  e.preventDefault();
                                  formatInlineText('italic');
                                  break;
                                case 'u':
                                  e.preventDefault();
                                  formatInlineText('underline');
                                  break;
                                case 'k':
                                  e.preventDefault();
                                  insertLink(true);
                                  break;
                              }
                            }
                          }}
                        />
                        <div className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                          💡 Usa la barra de herramientas para formatear el texto o los atajos de teclado tradicionales.
                        </div>
                      </div>
                    ) : (
                      <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div
                          className="text-base text-gray-700 dark:text-gray-300 leading-relaxed prose prose-base max-w-none dark:prose-invert"
                          style={{
                            lineHeight: '1.7'
                          }}
                          dangerouslySetInnerHTML={{ __html: noticia.contenido || '' }}
                        />
                      </div>
                    )}
                  </div>

                  {/* Información adicional */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                      {noticia.autor && (
                        <div>
                          <span className="font-medium">Autor:</span> {noticia.autor}
                        </div>
                      )}
                      {noticia.fuente && (
                        <div>
                          <span className="font-medium">Fuente:</span> {noticia.fuente}
                        </div>
                      )}
                      {noticia.urlFuente && (
                        <div className="md:col-span-2">
                          <span className="font-medium">URL Fuente:</span>{' '}
                          <a
                            href={noticia.urlFuente}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            {noticia.urlFuente}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Versiones Generadas por IA - Derecha en desktop, abajo en móvil */}
          <div className="xl:col-span-5 order-2">
            <div className="mb-6 flex items-center justify-between">
              {/* Información del autor y fecha */}
              <div className="flex items-center space-x-4 text-sm text-gray-700 dark:text-gray-300">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-1" />
                  {noticia.user.name}
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {new Date(noticia.createdAt).toLocaleDateString()}
                </div>
              </div>

              {/* Botón de Publicación Directa */}
              {noticia && noticia.imagenUrl && ['BORRADOR', 'EN_REVISION', 'APROBADA'].includes(noticia.estado) && (
                <button
                  onClick={() => setShowExternalPublicationWizard(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Globe className="h-4 w-4" />
                  <span className="hidden sm:inline">Publicar Directa</span>
                  <span className="sm:hidden">Publicar</span>
                </button>
              )}
            </div>

            {/* Estado de Diarios y Generación Incremental */}
            {diarios.length > 0 && (
              <div className="mb-6 bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div className="mb-4">
                  <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-3">
                    Estado por Diario
                  </h3>

                  {/* Acciones Rápidas de Generación */}
                  {getDiariosDisponibles().length > 0 && showQuickActions && (
                    <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                            Acción Rápida
                          </h4>
                        </div>
                        <span className="text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded-full">
                          {getDiariosDisponibles().length} pendiente{getDiariosDisponibles().length !== 1 ? 's' : ''}
                        </span>
                      </div>

                      {/* Generar Todas - Botón único centrado */}
                      <button
                        onClick={handleGenerateAll}
                        disabled={isGenerating}
                        className="w-full flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span className="hidden sm:inline">Generando todas las versiones...</span>
                            <span className="sm:hidden">Generando...</span>
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4" />
                            <span className="hidden sm:inline">Generar Todas ({getDiariosDisponibles().length})</span>
                            <span className="sm:hidden">Generar ({getDiariosDisponibles().length})</span>
                          </>
                        )}
                      </button>

                      {/* Progreso de generación */}
                      {isGenerating && (
                        <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
                          <div className="flex items-center space-x-2 text-xs text-blue-600 dark:text-blue-400">
                            <Loader2 className="h-3 w-3 animate-spin" />
                            <span>Generando versiones para todos los diarios...</span>
                          </div>
                          <div className="mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-1">
                            <div className="bg-blue-600 dark:bg-blue-400 h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                          </div>
                        </div>
                      )}

                      {/* Hint text */}
                      <p className="mt-3 text-xs text-blue-600 dark:text-blue-400 text-center">
                        💡 También puedes generar versiones individuales usando los botones "Generar" de cada diario
                      </p>
                    </div>
                  )}
                </div>

                {/* Grid de estado de diarios */}
                <div className="grid grid-cols-1 gap-3 mb-4">
                  {diarios.map((diario) => {
                    const tieneVersion = getDiariosConVersiones().includes(diario.id);
                    const version = versiones.find(v => v.diario.id === diario.id);
                    const isGeneratingThis = generatingDiarios.has(diario.id);
                    const hasError = generationErrors.has(diario.id);

                    return (
                      <div
                        key={diario.id}
                        className={`p-4 rounded-lg border transition-all duration-200 ${
                          hasError
                            ? 'border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/20'
                            : tieneVersion
                            ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20'
                            : isGeneratingThis
                            ? 'border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">{diario.nombre}</h4>
                            {diario.descripcion && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">({diario.descripcion})</span>
                            )}
                          </div>

                          {/* Indicador de estado */}
                          {hasError ? (
                            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                          ) : isGeneratingThis ? (
                            <Loader2 className="h-4 w-4 text-blue-600 dark:text-blue-400 animate-spin" />
                          ) : tieneVersion ? (
                            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                          ) : (
                            <Clock className="h-4 w-4 text-gray-400" />
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          {/* Estado y información */}
                          <div className="flex-1">
                            {hasError ? (
                              <div className="space-y-1">
                                <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                  Error
                                </span>
                                <p className="text-xs text-red-600 dark:text-red-400">
                                  {generationErrors.get(diario.id)}
                                </p>
                              </div>
                            ) : isGeneratingThis ? (
                              <div className="space-y-1">
                                <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                  Generando...
                                </span>
                                <p className="text-xs text-blue-600 dark:text-blue-400">
                                  Creando versión con IA
                                </p>
                              </div>
                            ) : tieneVersion && version ? (
                              <div className="space-y-1">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEstadoBadge(version.estado)}`}>
                                  {version.estado}
                                </span>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                  {new Date(version.createdAt).toLocaleDateString('es-ES', {
                                    day: '2-digit',
                                    month: 'short',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </p>
                              </div>
                            ) : (
                              <div className="space-y-1">
                                <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                  Pendiente
                                </span>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Sin versión generada
                                </p>
                              </div>
                            )}
                          </div>

                          {/* Botón de acción */}
                          <div className="ml-3">
                            {hasError ? (
                              <button
                                onClick={() => handleGenerateSingle(diario.id)}
                                disabled={isGenerating || isGeneratingThis}
                                className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                              >
                                <RotateCcw className="h-3 w-3" />
                                <span>Reintentar</span>
                              </button>
                            ) : isGeneratingThis ? (
                              <div className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                                <Loader2 className="h-3 w-3 animate-spin" />
                                <span>Generando</span>
                              </div>
                            ) : tieneVersion ? (
                              <button
                                onClick={() => handleGenerateSingle(diario.id)}
                                disabled={isGenerating || isGeneratingThis}
                                className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                              >
                                <RotateCcw className="h-3 w-3" />
                                <span>Regenerar</span>
                              </button>
                            ) : (
                              <button
                                onClick={() => handleGenerateSingle(diario.id)}
                                disabled={isGenerating || isGeneratingThis}
                                className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-all duration-200 shadow-sm hover:shadow-md"
                              >
                                <PlayCircle className="h-3 w-3" />
                                <span>Generar</span>
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Componente de Programación Simplificada - Al costado después de los diarios */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <ProgramacionSimple
                    noticia={noticia}
                    versiones={versiones}
                  />
                </div>

                {/* Mensaje cuando todas las versiones están completadas */}
                {getDiariosDisponibles().length === 0 && versiones.length > 0 && (
                  <div className="text-center py-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-700">
                    <div className="flex justify-center mb-3">
                      <div className="relative">
                        <CheckCheck className="h-8 w-8 text-green-600 dark:text-green-400" />
                        <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
                      ¡Trabajo Completado!
                    </h3>
                    
                    <p className="text-xs text-green-600 dark:text-green-400">
                      {versiones.length} versión{versiones.length !== 1 ? 'es' : ''} creada{versiones.length !== 1 ? 's' : ''} exitosamente
                    </p>

                    
                  </div>
                )}
              </div>
            )}

            {versiones.length === 0 && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <Bot className="h-8 w-8 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">No hay versiones</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Las versiones aparecerán aquí una vez generadas.
                </p>
              </div>
            )}
          </div>
        </div>



        {/* Versiones Individuales - Debajo del grid principal */}
        {versiones.length > 0 && (
          <div className="mt-8">
            <div className="mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                    <Bot className="h-6 w-6 mr-2 text-green-600 dark:text-green-400" />
                    Versiones Generadas por IA ({versiones.length})
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Versiones reescritas automáticamente para diferentes diarios.
                  </p>
                </div>

                {/* Botón para regenerar todas las versiones */}
                {canManageVersions && versiones.length > 0 && (
                  <button
                    onClick={regenerateAllVersions}
                    disabled={isGenerating}
                    className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                    title="Regenerar todas las versiones existentes con IA"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="hidden sm:inline">Regenerando...</span>
                        <span className="sm:hidden">...</span>
                      </>
                    ) : (
                      <>
                        <RotateCcw className="h-4 w-4" />
                        <span className="hidden sm:inline">Regenerar Todas</span>
                        <span className="sm:hidden">Regenerar</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>

            <div className="space-y-6">
              {versiones.map((version, index) => (
                <div key={version.id} className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-l-4 border-green-500 dark:border-green-400 min-w-0">
                  {/* Header de la versión */}
                  <div className="px-6 py-4 bg-green-50 dark:bg-green-900/20 border-b border-green-100 dark:border-green-800">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => toggleVersionExpansion(version.id)}
                          className="flex items-center space-x-2 text-green-900 hover:text-green-700 transition-colors"
                        >
                          <div className="flex items-center space-x-2">
                            {getEstadoIcon(version.estado)}
                            <h3 className="text-lg font-bold">
                              {version.diario.nombre}
                            </h3>
                          </div>
                          {expandedVersions.has(version.id) ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>

                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${getEstadoBadge(version.estado)}`}>
                          {version.estado}
                        </span>
                      </div>

                      {/* Botones de acción */}
                      {canManageVersions && (
                        <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                          {/* Botones de edición - Disponibles para usuarios y administradores */}
                          <button
                            onClick={() => startEditVersion(version)}
                            className="flex items-center space-x-1 px-2 sm:px-3 py-1 text-xs sm:text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-md transition-colors"
                            title="Editar versión"
                          >
                            <Edit3 className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="hidden sm:inline">Editar</span>
                          </button>

                          <button
                            onClick={() => regenerateVersion(version.id)}
                            disabled={isRegenerating === version.id}
                            className="flex items-center space-x-1 px-2 sm:px-3 py-1 text-xs sm:text-sm font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                            title="Regenerar con IA"
                          >
                            {isRegenerating === version.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-purple-700"></div>
                            ) : (
                              <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4" />
                            )}
                            <span className="hidden sm:inline">{isRegenerating === version.id ? 'Regenerando...' : 'Regenerar'}</span>
                          </button>

                          {/* Botón de poner en revisión - Disponible para usuarios */}
                          {!canReview && (
                            <button
                              onClick={() => handleVersionStateChange(version.id, 'EN_REVISION')}
                              className="flex items-center space-x-1 px-2 sm:px-3 py-1 text-xs sm:text-sm font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 rounded-md transition-colors"
                              title="Enviar a revisión"
                            >
                              <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                              <span className="hidden sm:inline">Enviar a Revisión</span>
                              <span className="sm:hidden">Revisar</span>
                            </button>
                          )}

                          {/* Botones de estado - Solo para administradores y editores */}


                          {/* Botón de publicación externa - Solo para versiones aprobadas o generadas */}
                          {(() => {
                            console.log('🔍 Verificando versión:', {
                              versionId: version.id,
                              estado: version.estado,
                              diario: version.diario.nombre,
                              esAprobadaOGenerada: version.estado === 'APROBADA' || version.estado === 'GENERADA',
                              diariosExternosLength: diariosExternos.length
                            });
                            return (version.estado === 'APROBADA' || version.estado === 'GENERADA') && diariosExternos.length > 0;
                          })() ? (
                            (() => {
                              const filteredDiarios = filterExternalDiariosForVersion(version.diario, diariosExternos);
                              console.log('🔍 Filtro de diarios:', {
                                versionDiario: version.diario,
                                diariosExternos: diariosExternos.map(d => ({ id: d.id, nombre: d.nombre })),
                                filteredDiarios: filteredDiarios.map(d => ({ id: d.id, nombre: d.nombre }))
                              });
                              return filteredDiarios;
                            })().map(diarioExterno => (
                            <PublishVersionButton
                              key={`${version.id}-${diarioExterno.id}`}
                              noticiaId={noticia!.id}
                              versionId={version.id}
                              diarioId={diarioExterno.id}
                              diarioNombre={diarioExterno.nombre}
                              estadoPublicacion={version.estadoPublicacion}
                              urlPublicacion={version.urlPublicacion}
                              onPublicationUpdate={reloadVersions}
                            />
                            ))
                          ) : version.estado !== 'APROBADA' && version.estado !== 'GENERADA' && diariosExternos.length > 0 ? (
                            <div className="text-xs text-gray-500">
                              Publicación disponible cuando la versión esté aprobada
                            </div>
                          ) : diariosExternos.length === 0 ? (
                            <div className="text-xs text-gray-500">
                              No hay diarios externos configurados
                            </div>
                          ) : null}
                        </div>
                      )}
                    </div>

                    {/* Información de generación */}
                    <div className="mt-2 flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-green-700">
                      <div className="flex items-center">
                        <User className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        <span className="truncate">Generado por {version.usuario.name}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        <span className="hidden sm:inline">{new Date(version.createdAt).toLocaleString()}</span>
                        <span className="sm:hidden">{new Date(version.createdAt).toLocaleDateString()}</span>
                      </div>
                      {version.metadatos && (
                        <div className="flex items-center">
                          <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Contenido expandible */}
                  {expandedVersions.has(version.id) && (
                    <div className="p-6">
                      {editingVersion === version.id ? (
                        /* Formulario de edición */
                        <div className="space-y-4">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">Editando versión</h4>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => saveEditVersion(version.id)}
                                className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-green-700 bg-green-100 hover:bg-green-200 rounded-md transition-colors"
                              >
                                <Save className="h-4 w-4" />
                                <span>Guardar</span>
                              </button>
                              <button
                                onClick={cancelEdit}
                                className="flex items-center space-x-1 px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
                              >
                                <X className="h-4 w-4" />
                                <span>Cancelar</span>
                              </button>
                            </div>
                          </div>

                          {/* Campo Volanta */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Volanta
                            </label>
                            <input
                              type="text"
                              value={editForm.volanta}
                              onChange={(e) => setEditForm(prev => ({ ...prev, volanta: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Volanta (opcional)"
                            />
                          </div>

                          {/* Campo Título */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Título *
                            </label>
                            <input
                              type="text"
                              value={editForm.titulo}
                              onChange={(e) => setEditForm(prev => ({ ...prev, titulo: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg font-semibold"
                              placeholder="Título de la noticia"
                              required
                            />
                          </div>

                          {/* Campo de Imagen */}
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Imagen
                              </label>
                              <button
                                type="button"
                                onClick={generateImageForVersion}
                                disabled={isGeneratingImage || !editForm.titulo.trim()}
                                className="flex items-center space-x-1 px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
                                title="Generar imagen con IA"
                              >
                                {isGeneratingImage ? (
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                ) : (
                                  <Image className="h-3 w-3" />
                                )}
                                <span>{isGeneratingImage ? 'Generando...' : 'Generar IA'}</span>
                              </button>
                            </div>

                            {/* Componente de subida de archivos */}
                            <ImageUploader
                              onImageUploaded={(imageUrl) => setEditForm(prev => ({ ...prev, imagenUrl: imageUrl }))}
                              currentImageUrl={editForm.imagenUrl}
                              disabled={isGeneratingImage}
                              className="mb-3"
                            />

                            {/* Campo de URL manual (opcional) */}
                            <div className="mt-3">
                              <label className="block text-xs font-medium text-gray-500 mb-1">
                                O pegar URL de imagen
                              </label>
                              <input
                                type="url"
                                value={editForm.imagenUrl}
                                onChange={(e) => setEditForm(prev => ({ ...prev, imagenUrl: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                placeholder="https://ejemplo.com/imagen.jpg"
                              />
                            </div>
                          </div>

                          {/* Campo Resumen */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Resumen
                            </label>
                            <textarea
                              value={editForm.resumen}
                              onChange={(e) => setEditForm(prev => ({ ...prev, resumen: e.target.value }))}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Resumen de la noticia (opcional)"
                            />
                          </div>

                          {/* Campo Contenido */}
                          <div>
                            <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                              Contenido *
                            </label>

                            {/* Barra de herramientas */}
                            <div className="border border-gray-300 rounded-t-md bg-gray-50 p-2 flex flex-wrap gap-1">
                              {/* Formato de texto */}
                              <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
                                <button
                                  type="button"
                                  onClick={() => formatText('bold')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Negrita (Ctrl+B)"
                                >
                                  <Bold size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('italic')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Cursiva (Ctrl+I)"
                                >
                                  <Italic size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('underline')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Subrayado (Ctrl+U)"
                                >
                                  <Underline size={16} />
                                </button>
                              </div>

                              {/* Alineación */}
                              <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
                                <button
                                  type="button"
                                  onClick={() => formatText('justifyLeft')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Alinear a la izquierda"
                                >
                                  <AlignLeft size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('justifyCenter')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Centrar"
                                >
                                  <AlignCenter size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('justifyRight')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Alinear a la derecha"
                                >
                                  <AlignRight size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('justifyFull')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Justificar"
                                >
                                  <AlignJustify size={16} />
                                </button>
                              </div>

                              {/* Listas y sangría */}
                              <div className="flex gap-1 border-r border-gray-300 pr-2 mr-2">
                                <button
                                  type="button"
                                  onClick={() => formatText('insertUnorderedList')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Lista con viñetas"
                                >
                                  <List size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('insertOrderedList')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Lista numerada"
                                >
                                  <ListOrdered size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('indent')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Aumentar sangría"
                                >
                                  <Indent size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => formatText('outdent')}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Disminuir sangría"
                                >
                                  <Indent size={16} className="rotate-180" />
                                </button>
                              </div>

                              {/* Herramientas especiales */}
                              <div className="flex gap-1">
                                <button
                                  type="button"
                                  onClick={() => insertQuote(false)}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Cita"
                                >
                                  <Quote size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => insertLink(false)}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Insertar enlace"
                                >
                                  <Link size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => toggleCase(false)}
                                  className="p-1.5 hover:bg-gray-200 rounded text-gray-700 hover:text-gray-900 transition-colors"
                                  title="Convertir a mayúsculas"
                                >
                                  <Type size={16} />
                                </button>
                              </div>
                            </div>

                            <div
                              ref={(el) => {
                                if (el) {
                                  setContentEditorRef(el);
                                  // Solo inicializar si el editor está realmente vacío
                                  const currentContent = el.innerHTML.trim();
                                  if (!currentContent ||
                                      currentContent === '<p>Escribe el contenido de la noticia aquí...</p>' ||
                                      currentContent === '<p><br></p>' ||
                                      currentContent === '<br>' ||
                                      currentContent === '') {
                                    el.innerHTML = editForm.contenido || '<p>Escribe el contenido de la noticia aquí...</p>';
                                  }
                                }
                              }}
                              contentEditable
                              suppressContentEditableWarning={true}
                              data-placeholder="Escribe el contenido de la versión aquí..."
                              onInput={(e) => {
                                const content = e.currentTarget.innerHTML;
                                // Usar setTimeout para evitar conflictos con el cursor
                                setTimeout(() => {
                                  setEditForm(prev => ({ ...prev, contenido: content }));
                                }, 0);
                              }}
                              className="content-editor w-full min-h-80 px-4 py-3 border border-gray-300 border-t-0 rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white prose prose-sm max-w-none"
                              style={{
                                lineHeight: '1.6',
                                fontSize: '14px'
                              }}
                              onKeyDown={(e) => {
                                // Atajos de teclado
                                if (e.ctrlKey || e.metaKey) {
                                  switch (e.key) {
                                    case 'b':
                                      e.preventDefault();
                                      formatText('bold');
                                      break;
                                    case 'i':
                                      e.preventDefault();
                                      formatText('italic');
                                      break;
                                    case 'u':
                                      e.preventDefault();
                                      formatText('underline');
                                      break;
                                    case 'k':
                                      e.preventDefault();
                                      insertLink(false);
                                      break;
                                  }
                                }
                              }}
                            />
                            <div className="mt-1 text-xs text-gray-500">
                              💡 Usa la barra de herramientas para formatear el texto o los atajos de teclado tradicionales.
                            </div>
                          </div>
                        </div>
                      ) : (
                        /* Vista normal */
                        <div className="space-y-4">
                          {version.volanta && (
                            <div>
                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Volanta</span>
                              <p className="text-sm text-green-600 dark:text-green-400 font-medium uppercase tracking-wide">
                                {version.volanta}
                              </p>
                            </div>
                          )}

                          <div>
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Título</span>
                            <h4 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100 leading-tight break-words">
                              {version.titulo}
                            </h4>
                          </div>

                          {/* Imagen de la versión */}
                          {version.imagenUrl && (
                            <div>
                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Imagen</span>
                              <div className="mt-2">
                                <img
                                  src={version.imagenUrl}
                                  alt={version.titulo}
                                  className="w-full max-w-md h-auto rounded-lg shadow-md object-cover"
                                />
                              </div>
                            </div>
                          )}

                          {version.resumen && (
                            <div>
                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Resumen</span>
                              <div
                                className="text-sm sm:text-base text-gray-700 dark:text-gray-300 leading-relaxed break-words version-content"
                                dangerouslySetInnerHTML={{ __html: version.resumen }}
                              />
                            </div>
                          )}

                          <div>
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Contenido</span>
                            <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                              <div
                                className="text-sm sm:text-base text-gray-700 dark:text-gray-300 leading-relaxed version-content"
                                style={{
                                  lineHeight: '1.7'
                                }}
                                dangerouslySetInnerHTML={{ __html: version.contenido || '' }}
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Metadatos técnicos - Solo para ADMIN */}
                      {version.metadatos && session?.user?.role === 'ADMIN' && (
                        <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Información Técnica</span>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <div>
                              <span className="font-medium">Modelo:</span> {version.metadatos.modelo}
                            </div>
                            <div>
                              <span className="font-medium">Tokens:</span> {version.metadatos.tokens_usados}
                            </div>
                            <div>
                              <span className="font-medium">Tiempo:</span> {version.metadatos.tiempo_generacion}ms
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Prompt usado - Solo para ADMIN */}
                      {version.promptUsado && session?.user?.role === 'ADMIN' && (
                        <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                          <details className="group">
                            <summary className="cursor-pointer text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide hover:text-gray-700 dark:hover:text-gray-300">
                              Ver Prompt Utilizado
                            </summary>
                            <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded text-sm text-gray-700 dark:text-gray-300 font-mono whitespace-pre-wrap">
                              {version.promptUsado}
                            </div>
                          </details>
                        </div>
                      )}

                      {/* Enlace al diario */}
                      {/* TODO: Agregar campo url al modelo Diario si es necesario */}
                      {false && (
                        <div className="pt-4 border-t border-gray-200">
                          <a
                            href="#"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 transition-colors"
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span>Ver en {version.diario.nombre}</span>
                          </a>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>


          </div>
        )}



        {/* Sección de Publicaciones Externas */}
        <div className="mt-8">
          <PublicacionesExternas noticiaId={noticia.id} />
        </div>
      </main>

      {/* Wizard de Publicación Externa */}
      {noticia && (
        <ExternalPublicationWizard
          isOpen={showExternalPublicationWizard}
          onClose={() => setShowExternalPublicationWizard(false)}
          noticiaId={noticia.id}
          noticia={{
            titulo: noticia.titulo,
            imagenUrl: noticia.imagenUrl,
            // categoriaId: noticia.categoria?.id, // Comentado por problemas de tipo
            categoria: noticia.categoria
          }}
        />
      )}


    </div>
  );
}
