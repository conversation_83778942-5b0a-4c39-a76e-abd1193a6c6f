"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ArrowLeft, Save, Upload, Link, LogOut, User, Newspaper } from "lucide-react";
import { ThemeToggle } from '@/components/theme-toggle';
import { signOut } from 'next-auth/react';
import AIImageGenerator from '@/components/AIImageGenerator';

interface FormData {
  volanta: string;
  titulo: string;
  resumen: string;
  contenido: string;
  imagenUrl: string;
  imagenArchivo: File | null;
  categoriaId: string;
}

export default function NuevaNoticiaPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [categorias, setCategorias] = useState<Array<{ id: string; nombre: string }>>([]);
  const [formData, setFormData] = useState<FormData>({
    volanta: "",
    titulo: "",
    resumen: "",
    contenido: "",
    imagenUrl: "",
    imagenArchivo: null,
    categoriaId: "",
  });

  // Cargar categorías al montar el componente
  useEffect(() => {
    // Cargar categorías
    fetch("/api/categorias")
      .then((res) => res.json())
      .then((data) => setCategorias(data))
      .catch((error) => console.error("Error cargando categorías:", error));
  }, []);

  const handleInputChange = (field: keyof FormData, value: string | File | null) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };



  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    // Limpiar errores previos
    setUploadError(null);

    if (file) {
      // Validaciones del lado del cliente
      const maxSize = 5 * 1024 * 1024; // 5MB
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

      if (file.size > maxSize) {
        setUploadError('El archivo es demasiado grande. Máximo 5MB permitido.');
        return;
      }

      if (!allowedTypes.includes(file.type)) {
        setUploadError('Tipo de archivo no permitido. Solo JPG, PNG y WEBP.');
        return;
      }

      console.log('📁 Archivo seleccionado:', {
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        type: file.type
      });
    }

    setFormData((prev) => ({
      ...prev,
      imagenArchivo: file,
      imagenUrl: file ? '' : prev.imagenUrl // Limpiar URL si se selecciona archivo
    }));
  };

  const handleImageGenerated = (imageUrl: string) => {
    setFormData((prev) => ({
      ...prev,
      imagenUrl: imageUrl,
      imagenArchivo: null // Limpiar archivo si se genera imagen por URL
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setUploadError(null);

    try {
      // Validaciones antes del envío
      if (!formData.titulo.trim()) {
        alert('El título es requerido');
        return;
      }

      if (!formData.contenido.trim()) {
        alert('El contenido es requerido');
        return;
      }

      const formDataToSend = new FormData();
      formDataToSend.append("volanta", formData.volanta);
      formDataToSend.append("titulo", formData.titulo);
      formDataToSend.append("resumen", formData.resumen);
      formDataToSend.append("contenido", formData.contenido);
      formDataToSend.append("categoriaId", formData.categoriaId);

      // Manejar imagen
      if (formData.imagenArchivo) {
        console.log('📤 Enviando archivo de imagen:', formData.imagenArchivo.name);
        setIsUploadingImage(true);
        formDataToSend.append("imagen", formData.imagenArchivo);
      } else if (formData.imagenUrl) {
        console.log('🔗 Enviando URL de imagen:', formData.imagenUrl.substring(0, 50) + '...');
        formDataToSend.append("imagenUrl", formData.imagenUrl);
      }

      console.log('📝 Creando noticia...');
      const response = await fetch("/api/noticias", {
        method: "POST",
        body: formDataToSend,
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Noticia creada exitosamente:', result);
        router.push("/noticias");
      } else {
        const errorData = await response.json();
        console.error('❌ Error del servidor:', errorData);
        setUploadError(errorData.error || 'Error desconocido del servidor');
        alert(`Error: ${errorData.error || 'Error desconocido'}`);
      }
    } catch (error) {
      console.error("❌ Error al crear noticia:", error);
      setUploadError('Error de conexión al crear la noticia');
      alert("Error al crear la noticia");
    } finally {
      setIsLoading(false);
      setIsUploadingImage(false);
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center p-8 bg-white dark:bg-gray-800 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">Acceso Denegado</h2>
          <p className="text-gray-600 dark:text-gray-400">Debes iniciar sesión para crear noticias.</p>
          <button
            onClick={() => router.push('/auth/login')}
            className="mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Ir a Iniciar Sesión
          </button>
        </div>
      </div>
    );
  }

  // Mostrar loading mientras se carga la sesión
  if (status === "loading" as any) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Cargando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center cursor-pointer" onClick={() => router.push('/dashboard')}>
              <Newspaper className="h-8 w-8 text-blue-600 dark:text-blue-500" />
              <h1 className="ml-2 text-xl font-bold text-gray-800 dark:text-gray-200">Panel de Noticias</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {session?.user?.name || 'Usuario'}
                </span>
              </div>
              <ThemeToggle />
              <button
                onClick={() => signOut({ callbackUrl: '/auth/login' })}
                className="flex items-center text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
              >
                <LogOut className="h-5 w-5 mr-1" />
                Cerrar Sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <button
            onClick={() => router.push('/noticias')}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors mb-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Volver
          </button>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">Crear Nueva Noticia</h1>
          <p className="text-gray-600 dark:text-gray-400">Completa todos los campos para publicar una nueva noticia.</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
          <form onSubmit={handleSubmit} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <div>
                <label htmlFor="volanta" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Volanta
                </label>
                <input
                  id="volanta"
                  type="text"
                  value={formData.volanta}
                  onChange={(e) => handleInputChange("volanta", e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="Ej: Elecciones 2025"
                />
              </div>
              <div>
                <label htmlFor="titulo" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Título Principal
                </label>
                <input
                  id="titulo"
                  type="text"
                  value={formData.titulo}
                  onChange={(e) => handleInputChange("titulo", e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="El nuevo candidato presidencial..."
                  required
                />
              </div>
              <div>
                <label htmlFor="resumen" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Resumen
                </label>
                <textarea
                  id="resumen"
                  value={formData.resumen}
                  onChange={(e) => handleInputChange("resumen", e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                  rows={4}
                  placeholder="Un breve resumen de la noticia..."
                ></textarea>
              </div>
              <div>
                <label htmlFor="contenido" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Contenido Completo
                </label>
                <textarea
                  id="contenido"
                  value={formData.contenido}
                  onChange={(e) => handleInputChange("contenido", e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                  rows={12}
                  placeholder="Escribe el cuerpo de la noticia aquí..."
                  required
                ></textarea>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Categoría
                </label>
                <select
                  value={formData.categoriaId}
                  onChange={(e) => handleInputChange("categoriaId", e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100"
                  required
                >
                  <option value="">Selecciona una categoría</option>
                  {categorias.map((categoria) => (
                    <option key={categoria.id} value={categoria.id}>
                      {categoria.nombre}
                    </option>
                  ))}
                </select>
              </div>



              {/* Generador de Imágenes IA */}
              <div className="lg:col-span-3">
                <AIImageGenerator
                  titulo={formData.titulo}
                  resumen={formData.resumen}
                  contenido={formData.contenido}
                  onImageGenerated={handleImageGenerated}
                  disabled={isLoading}
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Subir Imagen
                </label>
                <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  uploadError
                    ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20'
                    : formData.imagenArchivo
                      ? 'border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500'
                }`}>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="file-upload"
                    disabled={isUploadingImage}
                  />
                  <label htmlFor="file-upload" className={`cursor-pointer ${isUploadingImage ? 'opacity-50 cursor-not-allowed' : ''}`}>
                    {isUploadingImage ? (
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    ) : (
                      <Upload className={`w-8 h-8 mx-auto mb-2 ${
                        uploadError
                          ? 'text-red-400'
                          : formData.imagenArchivo
                            ? 'text-green-400'
                            : 'text-gray-400 dark:text-gray-500'
                      }`} />
                    )}
                    <p className={`text-sm ${
                      uploadError
                        ? 'text-red-600 dark:text-red-400'
                        : formData.imagenArchivo
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {isUploadingImage
                        ? 'Subiendo imagen...'
                        : formData.imagenArchivo
                          ? `✓ ${formData.imagenArchivo.name}`
                          : 'Haz clic para subir una imagen'
                      }
                    </p>
                    {!formData.imagenArchivo && !isUploadingImage && (
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">PNG, JPG, WEBP hasta 5MB</p>
                    )}
                  </label>
                </div>

                {/* Mostrar información del archivo */}
                {formData.imagenArchivo && !isUploadingImage && (
                  <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      <strong>Archivo seleccionado:</strong> {formData.imagenArchivo.name}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      Tamaño: {(formData.imagenArchivo.size / 1024 / 1024).toFixed(2)}MB
                    </p>
                  </div>
                )}

                {/* Mostrar errores */}
                {uploadError && (
                  <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                    <p className="text-sm text-red-700 dark:text-red-300">
                      ❌ {uploadError}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  o URL de Imagen
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.imagenUrl}
                    onChange={(e) => handleInputChange("imagenUrl", e.target.value)}
                    className="w-full px-4 py-3 pl-10 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="https://ejemplo.com/imagen.jpg"
                  />
                  <Link className="w-5 h-5 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                </div>
              </div>

              <div className="pt-6 lg:col-span-3 lg:flex lg:justify-end lg:items-center space-y-3 lg:space-y-0 lg:space-x-4">
                <button
                  type="button"
                  onClick={() => router.push('/noticias')}
                  className="w-full lg:w-auto px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full lg:w-auto px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center font-semibold"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Guardando...
                    </>
                  ) : (
                    <>
                      <Save className="w-5 h-5 mr-2" />
                      Publicar Noticia
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}