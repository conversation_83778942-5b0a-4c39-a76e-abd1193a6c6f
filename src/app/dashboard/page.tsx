'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FileText, TrendingUp, Clock, CheckCircle, Webhook, Bell, Newspaper } from 'lucide-react';
import AppHeader from '@/components/AppHeader';
import RedesSocialesStats from '@/components/dashboard/RedesSocialesStats';

interface NoticiaStats {
  total: number;
  publicadas: number;
  borrador: number;
  enRevision: number;
  aprobadas: number;
  destacadas: number;
  webhook: number;
  porCategoria: Array<{
    categoriaId: number | null;
    categoriaNombre: string;
    categoriaColor: string;
    count: number;
  }>;
  recientes: Array<{
    id: number;
    titulo: string;
    estado: string;
    createdAt: string;
    categoria?: {
      nombre: string;
      color: string;
    };
    user?: {
      name: string;
    };
  }>;
  webhookRecientes: Array<{
    id: number;
    titulo: string;
    periodista: string;
    estado: string;
    createdAt: string;
    categoria?: {
      nombre: string;
      color: string;
    };
  }>;
}

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<NoticiaStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session) {
      loadStats();
      checkAdminStatus();
    }
  }, [session]);

  const checkAdminStatus = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=1');
      setIsAdmin(response.ok && response.status !== 403);
    } catch (error) {
      setIsAdmin(false);
    }
  };

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/noticias/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error al cargar estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }





  const navigateToRedesSociales = () => {
    router.push('/redes-sociales');
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return 'text-green-600 dark:text-green-400';
      case 'APROBADA':
        return 'text-blue-600 dark:text-blue-400';
      case 'EN_REVISION':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'BORRADOR':
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PUBLICADA':
        return <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'APROBADA':
        return <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      case 'EN_REVISION':
        return <Clock className="h-4 w-4 text-yellow-500 dark:text-yellow-400" />;
      case 'BORRADOR':
        return <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <AppHeader title="Sapia Noticias v2.0" />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-foreground">Dashboard</h2>
          <p className="text-muted-foreground mt-1">Gestiona tus noticias y contenido desde el panel principal</p>
        </div>

        {/* Stats */}
        <div>
          <h3 className="text-lg font-medium text-foreground">Estadísticas Generales</h3>
          <div className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-card overflow-hidden shadow rounded-lg border border-border">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Newspaper className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-muted-foreground truncate">
                        Noticias Totales
                      </dt>
                      <dd className="text-lg font-medium text-card-foreground">
                        {stats?.total || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Publicadas
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.publicadas || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        En Revisión
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.enRevision || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Destacadas
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.destacadas || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Webhook Stats */}
          {stats?.webhook && stats.webhook > 0 && (
            <div className="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Webhook className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                        Noticias vía WhatsApp
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {stats?.webhook || 0}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Webhook Notifications */}
          {stats?.webhookRecientes && stats.webhookRecientes.length > 0 && (
            <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="px-6 py-4 border-b border-blue-200 dark:border-blue-800">
                <div className="flex items-center">
                  <Bell className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                  <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100">
                    Noticias Recibidas vía WhatsApp
                  </h3>
                </div>
              </div>
              <div className="divide-y divide-blue-200 dark:divide-blue-800">
                {stats.webhookRecientes.map((noticia) => (
                  <div key={noticia.id} className="px-6 py-4 hover:bg-blue-100 dark:hover:bg-blue-900/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 truncate max-w-md">
                            {noticia.titulo}
                          </h4>
                          <p className="text-xs text-blue-700 dark:text-blue-300">
                            Por: {noticia.periodista} • {noticia.categoria?.nombre || 'Sin categoría'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          noticia.estado === 'EN_REVISION'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                            : noticia.estado === 'PUBLICADA'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                        }`}>
                          {noticia.estado}
                        </span>
                        <span className="text-xs text-blue-600 dark:text-blue-400">
                          {new Date(noticia.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Redes Sociales Stats */}
          <div className="mt-8">
            <RedesSocialesStats onNavigateToRedes={navigateToRedesSociales} />
          </div>

          {/* Recent News */}
          <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Noticias Recientes</h3>
            </div>
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {stats?.recientes && stats.recientes.length > 0 ? (
                stats.recientes.map((noticia) => (
                  <div key={noticia.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getEstadoIcon(noticia.estado)}
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-md">
                            {noticia.titulo}
                          </h4>
                          <div className="flex items-center space-x-2 mt-1">
                            {noticia.categoria && (
                              <span
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                style={{ backgroundColor: `${noticia.categoria.color}20`, color: noticia.categoria.color }}
                              >
                                {noticia.categoria.nombre}
                              </span>
                            )}
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {new Date(noticia.createdAt).toLocaleDateString()}
                            </span>
                            {noticia.user && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                por {noticia.user.name}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <span className={`text-xs font-medium ${getEstadoColor(noticia.estado)}`}>
                        {noticia.estado.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                  No hay noticias recientes
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}