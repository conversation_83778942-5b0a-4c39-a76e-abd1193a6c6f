import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// GET /uploads/images/[...path] - <PERSON><PERSON> imágenes estáticas
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params;
  try {
    // Construir la ruta del archivo
    const filePath = join(process.cwd(), 'public', 'uploads', 'images', ...resolvedParams.path);
    
    // Verificar que el archivo existe
    if (!existsSync(filePath)) {
      console.log('❌ Archivo no encontrado:', filePath);
      console.log('📁 Directorio de trabajo:', process.cwd());
      console.log('📂 Ruta completa buscada:', filePath);

      // Verificar si el directorio padre existe
      const parentDir = join(process.cwd(), 'public', 'uploads', 'images');
      console.log('📂 Directorio padre:', parentDir, 'existe:', existsSync(parentDir));

      // Listar archivos en el directorio si existe
      if (existsSync(parentDir)) {
        try {
          const fs = require('fs');
          const files = fs.readdirSync(parentDir);
          console.log('📋 Archivos en directorio:', files.slice(0, 10)); // Solo primeros 10
        } catch (error) {
          console.log('❌ Error listando archivos:', error);
        }
      }

      return new NextResponse('Archivo no encontrado', { status: 404 });
    }

    // Leer el archivo
    const fileBuffer = await readFile(filePath);
    
    // Determinar el tipo de contenido basado en la extensión
    const extension = resolvedParams.path[resolvedParams.path.length - 1].split('.').pop()?.toLowerCase();
    let contentType = 'application/octet-stream';
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'gif':
        contentType = 'image/gif';
        break;
      case 'webp':
        contentType = 'image/webp';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
    }

    // Configurar headers de cache
    const headers = new Headers();
    headers.set('Content-Type', contentType);
    headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    headers.set('Content-Length', fileBuffer.length.toString());

    console.log('✅ Sirviendo imagen:', {
      path: resolvedParams.path.join('/'),
      contentType,
      size: fileBuffer.length
    });

    return new NextResponse(fileBuffer, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('❌ Error sirviendo imagen:', error);
    return new NextResponse('Error interno del servidor', { status: 500 });
  }
}
