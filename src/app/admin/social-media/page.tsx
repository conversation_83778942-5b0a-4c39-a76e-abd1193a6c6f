'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import DiarioConfigCard from '@/components/social-media/DiarioConfigCard';

interface SocialAccount {
  id: number;
  platform: string;
  username: string;
  isActive: boolean;
  lastSync: string | null;
}

interface SyncStatus {
  hasApiKey: boolean;
  connectionStatus: 'connected' | 'failed' | 'error' | 'not_tested';
  lastSync: string | null;
  profileCount: number;
}

interface DiarioProfile {
  diario: {
    id: number;
    nombre: string;
    descripcion: string | null;
    activo: boolean;
  };
  configuracion: {
    id: number;
    uploadPostProfile: string;
    facebookEnabled: boolean;
    twitterEnabled: boolean;
    instagramEnabled: boolean;
    linkedinEnabled: boolean;
    facebookPageId: string | null;
    configuradoEn: string;
    activo: boolean;
  } | null;
  estadoConexion: 'configurado' | 'pendiente';
}

interface UploadPostProfile {
  username: string;
  displayName: string;
  connectedPlatforms: string[];
  platformDetails: {
    facebook: { pageId: string; pageName: string } | null;
    twitter: { userId: string; username: string } | null;
    instagram: { userId: string; username: string } | null;
    linkedin: { pageId: string; pageName: string } | null;
  };
  isActive: boolean;
  lastSync: string;
}

export default function SocialMediaAdminPage() {
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);

  // Estados para gestión de perfiles de diarios
  const [diarioProfiles, setDiarioProfiles] = useState<DiarioProfile[]>([]);
  const [uploadPostProfiles, setUploadPostProfiles] = useState<UploadPostProfile[]>([]);
  const [loadingProfiles, setLoadingProfiles] = useState(false);
  const [configuringDiario, setConfiguringDiario] = useState<number | null>(null);

  // Estados para herramientas esenciales
  const [testingFinalSystem, setTestingFinalSystem] = useState(false);
  const [checkingScheduledStatus, setCheckingScheduledStatus] = useState(false);
  const [debuggingJobStatus, setDebuggingJobStatus] = useState(false);
  const [testingApiKey, setTestingApiKey] = useState(false);
  const [testingCaptionValidation, setTestingCaptionValidation] = useState(false);
  const [creatingTable, setCreatingTable] = useState(false);
  const [regeneratingPrisma, setRegeneratingPrisma] = useState(false);
  const [buildingProject, setBuildingProject] = useState(false);
  const [checkingTypeScript, setCheckingTypeScript] = useState(false);

  // Cargar datos
  const loadData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/social-media/accounts');
      const data = await response.json();

      if (data.success) {
        setAccounts(data.accounts || []);
        setSyncStatus(data.syncStatus || null);
      }

      // Cargar también los perfiles de diarios
      await loadDiarioProfiles();
    } catch (error) {
      console.error('Error cargando datos:', error);
      toast.error('Error cargando datos de redes sociales');
    } finally {
      setLoading(false);
    }
  };

  // Cargar perfiles de diarios
  const loadDiarioProfiles = async () => {
    try {
      const response = await fetch('/api/social-media/diario-profiles');
      const data = await response.json();

      if (data.success) {
        setDiarioProfiles(data.perfiles || []);
      }
    } catch (error) {
      console.error('Error cargando perfiles de diarios:', error);
    }
  };

  // Cargar perfiles disponibles de upload-post
  const loadUploadPostProfiles = async () => {
    try {
      setLoadingProfiles(true);
      const response = await fetch('/api/social-media/upload-post-profiles');
      const data = await response.json();

      if (data.success) {
        setUploadPostProfiles(data.profiles || []);
        toast.success(`${data.profiles.length} perfiles cargados de upload-post`);
      } else {
        toast.error('Error cargando perfiles de upload-post');
      }
    } catch (error) {
      console.error('Error cargando perfiles de upload-post:', error);
      toast.error('Error conectando con upload-post');
    } finally {
      setLoadingProfiles(false);
    }
  };

  // Configurar perfil de diario
  const configureDiarioProfile = async (
    diarioId: number,
    uploadPostProfile: string,
    platforms: { [key: string]: boolean },
    facebookPageId?: string
  ) => {
    try {
      setConfiguringDiario(diarioId);

      const response = await fetch('/api/social-media/diario-profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          diarioId,
          uploadPostProfile,
          facebookEnabled: platforms.facebook || false,
          twitterEnabled: platforms.twitter || false,
          instagramEnabled: platforms.instagram || false,
          linkedinEnabled: platforms.linkedin || false,
          facebookPageId
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        await loadDiarioProfiles(); // Recargar datos
      } else {
        toast.error(data.error || 'Error configurando diario');
      }
    } catch (error) {
      console.error('Error configurando diario:', error);
      toast.error('Error configurando diario');
    } finally {
      setConfiguringDiario(null);
    }
  };

  // Sincronizar perfiles
  const syncProfiles = async () => {
    try {
      setSyncing(true);
      const response = await fetch('/api/social-media/sync-profiles', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        toast.success('Perfiles sincronizados correctamente');
        await loadData();
      } else {
        toast.error('Error sincronizando perfiles');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error en sincronización');
    } finally {
      setSyncing(false);
    }
  };

  // Test final del sistema completo
  const testFinalSystem = async () => {
    try {
      setTestingFinalSystem(true);

      const confirmed = window.confirm(
        '🎉 Test del Sistema Completo\n\n' +
        'Esto probará:\n' +
        '• Conexión con upload-post\n' +
        '• Publicación en Twitter\n' +
        '• Publicación en Facebook\n' +
        '• Programación nativa\n\n' +
        '¿Continuar?'
      );

      if (!confirmed) {
        setTestingFinalSystem(false);
        return;
      }

      console.log('🎉 Iniciando test final del sistema...');
      toast.info('🎉 Probando sistema completo...');

      const response = await fetch('/api/social-media/test-final-system', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('🎉 Test final exitoso:', data);
        toast.success('🎉 ¡Sistema funcionando perfectamente!');

        // Mostrar resultados detallados
        setTimeout(() => {
          toast.success(`✅ ${data.summary.successCount}/${data.summary.totalTests} tests exitosos`);
        }, 1000);

        data.results.forEach((result: any, index: number) => {
          setTimeout(() => {
            if (result.success) {
              toast.success(`✅ ${result.test}: ${result.message}`);
            } else {
              toast.error(`❌ ${result.test}: ${result.error}`);
            }
          }, (index + 2) * 1000);
        });

      } else {
        console.error('❌ Error en test final:', data);
        toast.error('❌ Error en test del sistema');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando test final');
    } finally {
      setTestingFinalSystem(false);
    }
  };

  // Verificar estado de publicaciones programadas
  const checkScheduledStatus = async () => {
    try {
      setCheckingScheduledStatus(true);
      
      const jobId = window.prompt(
        '🔍 Verificar estado de publicación programada\n\n' +
        'Ingresa el Job ID (opcional):\n' +
        'O deja vacío para ver todas las programadas:'
      );
      
      if (jobId === null) {
        setCheckingScheduledStatus(false);
        return;
      }

      console.log('🔍 Verificando estado de publicaciones programadas...');
      toast.info('🔍 Verificando estado en upload-post...');
      
      const response = await fetch('/api/social-media/simple-status-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId: jobId.trim() || undefined,
          scheduledTime: '2025-09-09T01:56:00.000Z'
        })
      });
      const data = await response.json();

      if (data.success) {
        console.log('🎉 Verificación exitosa:', data);
        
        setTimeout(() => {
          toast.info(`🕐 Hora actual Argentina: ${data.timeInfo.current.argentina}`);
        }, 500);
        
        if (data.schedulingAnalysis) {
          setTimeout(() => {
            toast.info(`📅 Programado para: ${data.schedulingAnalysis.scheduled.argentina}`);
          }, 1000);
          
          setTimeout(() => {
            const status = data.schedulingAnalysis.status;
            if (status.isPast) {
              toast.warning(`⏰ ${status.timeUntil} - debería haberse publicado`);
            } else {
              toast.info(`⏰ ${status.timeUntil}`);
            }
          }, 1500);
        }
        
        setTimeout(() => {
          if (data.quickChecks.shouldCheckTwitter) {
            toast.info('👀 ¡Revisa tu Twitter ahora!');
          } else {
            toast.info('⏳ Aún no es hora de publicar');
          }
        }, 2000);
        
      } else {
        console.error('Error verificando estado:', data);
        toast.error('❌ Error verificando estado');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando verificación');
    } finally {
      setCheckingScheduledStatus(false);
    }
  };

  // Debug específico del Job ID
  const debugJobStatus = async () => {
    try {
      setDebuggingJobStatus(true);
      
      console.log('🔍 Iniciando debug específico del Job ID...');
      toast.info('🔍 Debugging Job ID específico...');
      
      const response = await fetch('/api/social-media/debug-job-status', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('🎉 Debug exitoso:', data);
        
        setTimeout(() => {
          const time = data.timeAnalysis;
          if (time.status.isPast) {
            toast.warning(`⏰ Pasaron ${time.status.minutesPast} minutos desde programación`);
          } else {
            toast.info('⏰ Aún no es la hora programada');
          }
        }, 500);
        
        setTimeout(() => {
          const summary = data.summary;
          toast.info(`📡 Endpoints: ${summary.workingEndpoints}/${summary.totalEndpoints} funcionando`);
        }, 1000);
        
        data.diagnosis.forEach((diag: string, index: number) => {
          setTimeout(() => {
            toast.info(diag);
          }, (index + 2) * 1000);
        });
        
        setTimeout(() => {
          toast.success('🔗 Abriendo búsqueda en Twitter...');
          window.open(data.twitterSearchUrl, '_blank');
        }, (data.diagnosis.length + 2) * 1000);
        
      } else {
        console.error('Error en debug:', data);
        toast.error('❌ Error en debug');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando debug');
    } finally {
      setDebuggingJobStatus(false);
    }
  };

  // Test de detección de API Key
  const testApiKeyDetection = async () => {
    try {
      setTestingApiKey(true);

      console.log('🔍 Probando detección de API Key...');
      toast.info('🔍 Probando detección de API Key...');

      const response = await fetch('/api/social-media/test-api-key-detection', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('🎉 Test de API Key exitoso:', data);

        setTimeout(() => {
          const detection = data.apiKeyDetection;
          if (detection.hasApiKey) {
            toast.success(`✅ API Key detectada (${detection.apiKeyLength} caracteres)`);
          } else {
            toast.error('❌ API Key no detectada');
          }
        }, 500);

        setTimeout(() => {
          if (data.connectionTest?.success) {
            toast.success('✅ Conexión con upload-post exitosa');
          } else {
            toast.warning('⚠️ Problema de conexión con upload-post');
          }
        }, 1000);

        data.recommendations.forEach((rec: string, index: number) => {
          setTimeout(() => {
            toast.info(rec);
          }, (index + 2) * 1000);
        });

      } else {
        console.error('Error en test de API Key:', data);
        toast.error('❌ Error en test de API Key');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando test');
    } finally {
      setTestingApiKey(false);
    }
  };

  // Test de validación de caption
  const testCaptionValidation = async () => {
    try {
      setTestingCaptionValidation(true);

      console.log('🔍 Probando validación de caption...');
      toast.info('🔍 Probando validación de caption...');

      const response = await fetch('/api/social-media/test-caption-validation', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('🎉 Test de validación exitoso:', data);

        setTimeout(() => {
          const summary = data.summary;
          if (summary.allValid) {
            toast.success(`✅ Todas las validaciones pasaron (${summary.totalTests} tests)`);
          } else {
            toast.warning(`⚠️ ${summary.invalidCaptions}/${summary.totalTests} validaciones fallaron`);
          }
        }, 500);

        setTimeout(() => {
          if (data.summary.allValid) {
            toast.success('✅ No se encontró "undefined" en ningún caption');
          } else {
            toast.error('❌ Se encontró "undefined" en algunos captions');
          }
        }, 1000);

        data.recommendations.forEach((rec: string, index: number) => {
          setTimeout(() => {
            toast.info(rec);
          }, (index + 2) * 1000);
        });

      } else {
        console.error('Error en test de validación:', data);
        toast.error('❌ Error en test de validación');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando test');
    } finally {
      setTestingCaptionValidation(false);
    }
  };

  // Crear tabla de configuración de diarios
  const createDiarioSocialTable = async () => {
    try {
      setCreatingTable(true);

      console.log('🔧 Creando tabla diario_social_configs...');
      toast.info('🔧 Creando tabla de configuración...');

      const response = await fetch('/api/admin/create-diario-social-table', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('✅ Tabla creada exitosamente:', data);
        toast.success('✅ Tabla creada exitosamente');

        // Recargar datos
        setTimeout(() => {
          loadData();
        }, 1000);

      } else {
        console.error('Error creando tabla:', data);
        toast.error('❌ Error creando tabla');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando creación');
    } finally {
      setCreatingTable(false);
    }
  };

  // Regenerar cliente de Prisma
  const regeneratePrisma = async () => {
    try {
      setRegeneratingPrisma(true);

      console.log('🔄 Regenerando cliente de Prisma...');
      toast.info('🔄 Regenerando cliente de Prisma...');

      const response = await fetch('/api/admin/regenerate-prisma', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('✅ Cliente regenerado exitosamente:', data);
        toast.success('✅ Cliente de Prisma regenerado exitosamente');

        // Recargar datos después de regenerar
        setTimeout(() => {
          loadData();
        }, 1000);

      } else {
        console.error('Error regenerando cliente:', data);
        toast.warning('⚠️ ' + data.error);
        toast.info('💡 ' + data.suggestion);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando regeneración');
    } finally {
      setRegeneratingPrisma(false);
    }
  };

  // Compilar proyecto para deploy
  const buildProject = async () => {
    try {
      setBuildingProject(true);

      console.log('🔨 Compilando proyecto...');
      toast.info('🔨 Compilando proyecto para deploy...');

      const response = await fetch('/api/admin/build-project', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('✅ Compilación exitosa:', data);
        toast.success('✅ Proyecto compilado exitosamente');

        // Mostrar información del build
        setTimeout(() => {
          if (data.buildInfo.duration) {
            toast.success(`⏱️ Compilado en: ${data.buildInfo.duration}`);
          }
        }, 1000);

        setTimeout(() => {
          toast.info('🚀 Proyecto listo para deploy');
        }, 2000);

        // Mostrar próximos pasos
        data.nextSteps.forEach((step: string, index: number) => {
          setTimeout(() => {
            toast.info(step);
          }, (index + 3) * 1000);
        });

      } else {
        console.error('Error en compilación:', data);
        toast.error('❌ Error en compilación');

        if (data.suggestions) {
          data.suggestions.forEach((suggestion: string, index: number) => {
            setTimeout(() => {
              toast.warning(`💡 ${suggestion}`);
            }, (index + 1) * 1000);
          });
        }
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error ejecutando compilación');
    } finally {
      setBuildingProject(false);
    }
  };

  // Verificar errores de TypeScript
  const checkTypeScript = async () => {
    try {
      setCheckingTypeScript(true);

      console.log('🔍 Verificando TypeScript...');
      toast.info('🔍 Verificando errores de TypeScript...');

      const response = await fetch('/api/admin/check-typescript', {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        console.log('✅ TypeScript OK:', data);
        toast.success('✅ Sin errores de TypeScript');
        toast.info('🚀 Listo para compilar');

      } else {
        console.error('❌ Errores de TypeScript:', data);
        toast.error('❌ Errores de TypeScript encontrados');

        if (data.suggestions) {
          data.suggestions.forEach((suggestion: string, index: number) => {
            setTimeout(() => {
              toast.warning(`💡 ${suggestion}`);
            }, (index + 1) * 1000);
          });
        }
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('❌ Error verificando TypeScript');
    } finally {
      setCheckingTypeScript(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const getConnectionStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'failed':
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getPlatformBadge = (platform: string) => {
    const colors = {
      twitter: 'bg-blue-100 text-blue-800',
      facebook: 'bg-blue-100 text-blue-800',
      instagram: 'bg-pink-100 text-pink-800',
      linkedin: 'bg-blue-100 text-blue-800'
    };
    
    return (
      <Badge className={colors[platform as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {platform}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">🌐 Administración de Redes Sociales</h1>
        <Button onClick={loadData} disabled={loading} size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      {/* Herramientas de administración */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🛠️ Herramientas de Administración
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={testFinalSystem}
                disabled={testingFinalSystem}
                variant="default"
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${testingFinalSystem ? 'animate-spin' : ''}`} />
                {testingFinalSystem ? 'Probando...' : '🎉 Test Sistema Completo'}
              </Button>
              
              <Button
                onClick={checkScheduledStatus}
                disabled={checkingScheduledStatus}
                variant="default"
                size="sm"
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${checkingScheduledStatus ? 'animate-spin' : ''}`} />
                {checkingScheduledStatus ? 'Verificando...' : '🔍 Estado Programadas'}
              </Button>
              
              <Button
                onClick={debugJobStatus}
                disabled={debuggingJobStatus}
                variant="default"
                size="sm"
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${debuggingJobStatus ? 'animate-spin' : ''}`} />
                {debuggingJobStatus ? 'Debugging...' : '🐛 Debug Job ID'}
              </Button>
              
              <Button
                onClick={syncProfiles}
                disabled={syncing || !syncStatus?.hasApiKey}
                size="sm"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
                {syncing ? 'Sincronizando...' : 'Sincronizar Perfiles'}
              </Button>

              <Button
                onClick={loadUploadPostProfiles}
                disabled={loadingProfiles || !syncStatus?.hasApiKey}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loadingProfiles ? 'animate-spin' : ''}`} />
                {loadingProfiles ? 'Cargando...' : `Cargar Perfiles Upload-Post ${uploadPostProfiles.length > 0 ? `(${uploadPostProfiles.length} cargados)` : ''}`}
              </Button>

              <Button
                onClick={testApiKeyDetection}
                disabled={testingApiKey}
                variant="outline"
                size="sm"
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${testingApiKey ? 'animate-spin' : ''}`} />
                {testingApiKey ? 'Probando...' : '🔑 Test API Key'}
              </Button>

              <Button
                onClick={testCaptionValidation}
                disabled={testingCaptionValidation}
                variant="outline"
                size="sm"
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${testingCaptionValidation ? 'animate-spin' : ''}`} />
                {testingCaptionValidation ? 'Probando...' : '📝 Test Caption'}
              </Button>

              <Button
                onClick={createDiarioSocialTable}
                disabled={creatingTable}
                variant="outline"
                size="sm"
                className="bg-gray-600 hover:bg-gray-700 text-white"
              >
                <AlertCircle className={`h-4 w-4 mr-2 ${creatingTable ? 'animate-spin' : ''}`} />
                {creatingTable ? 'Creando...' : '🔧 Crear Tabla'}
              </Button>

              <Button
                onClick={regeneratePrisma}
                disabled={regeneratingPrisma}
                variant="outline"
                size="sm"
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${regeneratingPrisma ? 'animate-spin' : ''}`} />
                {regeneratingPrisma ? 'Regenerando...' : '🔄 Regenerar Prisma'}
              </Button>

              <Button
                onClick={checkTypeScript}
                disabled={checkingTypeScript}
                variant="outline"
                size="sm"
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${checkingTypeScript ? 'animate-spin' : ''}`} />
                {checkingTypeScript ? 'Verificando...' : '🔍 Verificar TypeScript'}
              </Button>

              <Button
                onClick={buildProject}
                disabled={buildingProject}
                variant="outline"
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${buildingProject ? 'animate-spin' : ''}`} />
                {buildingProject ? 'Compilando...' : '🔨 Compilar para Deploy'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estado de configuración */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            ⚙️ Estado de Configuración
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">API Key de Upload-Post</div>
                <div className="text-sm text-muted-foreground">
                  {syncStatus?.hasApiKey ? 'Configurada' : 'No configurada'}
                </div>
              </div>
              {syncStatus?.hasApiKey ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <div className="font-medium">Conexión con Upload-Post</div>
                <div className="text-sm text-muted-foreground">
                  {syncStatus?.connectionStatus === 'connected' && 'Conectado'}
                  {syncStatus?.connectionStatus === 'failed' && 'Falló la conexión'}
                  {syncStatus?.connectionStatus === 'error' && 'Error de conexión'}
                  {syncStatus?.connectionStatus === 'not_tested' && 'No probado'}
                </div>
              </div>
              {getConnectionStatusIcon(syncStatus?.connectionStatus || 'not_tested')}
            </div>
          </div>

          {!syncStatus?.hasApiKey && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <div className="font-medium text-yellow-800">API Key Requerida</div>
                  <div className="text-sm text-yellow-700 mt-1">
                    Para usar la funcionalidad de redes sociales, necesitas configurar tu API key de upload-post.com
                    en las variables de entorno como <code>UPLOAD_POST_API_KEY</code>.
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => window.open('https://app.upload-post.com', '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Ir a Upload-Post
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Lista de cuentas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>📱 Cuentas Conectadas ({accounts.length})</span>
            <div className="text-sm font-normal text-muted-foreground">
              Activas: {accounts.filter(a => a.isActive).length}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {accounts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No hay cuentas conectadas. Sincroniza los perfiles para cargar las cuentas.
            </div>
          ) : (
            <div className="space-y-3">
              {accounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getPlatformBadge(account.platform)}
                    <div>
                      <div className="font-medium">@{account.username}</div>
                      <div className="text-sm text-muted-foreground">
                        {account.lastSync ? `Última sincronización: ${new Date(account.lastSync).toLocaleString()}` : 'Nunca sincronizado'}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {account.isActive ? (
                      <Badge className="bg-green-100 text-green-800">Activa</Badge>
                    ) : (
                      <Badge className="bg-gray-100 text-gray-800">Inactiva</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuración de Diarios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>📰 Configuración de Diarios</span>
            <div className="text-sm font-normal text-muted-foreground">
              {diarioProfiles.filter(p => p.estadoConexion === 'configurado').length}/{diarioProfiles.length} configurados
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {diarioProfiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Cargando configuración de diarios...
            </div>
          ) : (
            <div className="space-y-4">
              {diarioProfiles.map((diarioProfile) => (
                <DiarioConfigCard
                  key={diarioProfile.diario.id}
                  diarioProfile={diarioProfile}
                  uploadPostProfiles={uploadPostProfiles}
                  onConfigure={configureDiarioProfile}
                  isConfiguring={configuringDiario === diarioProfile.diario.id}
                />
              ))}
            </div>
          )}

          {uploadPostProfiles.length === 0 && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-800">Cargar Perfiles de Upload-Post</div>
                  <div className="text-sm text-blue-700 mt-1">
                    Para configurar los diarios, primero carga los perfiles disponibles de upload-post.com
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
