'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Save, Loader2, RefreshCw, Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface CategoriaLocal {
  id: number;
  nombre: string;
  color: string;
}

interface CategoriaExterna {
  id: number;
  name: string;
  description?: string;
}

interface CategoriaMapeo {
  id: number;
  categoriaLocalId: number;
  categoriaExternaId: number;
  categoriaLocal: CategoriaLocal;
}

interface DiarioExterno {
  id: number;
  nombre: string;
  urlBase: string;
}

export default function ConfigurarCategorias() {
  const params = useParams();
  const router = useRouter();
  const diarioId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [loadingExternas, setLoadingExternas] = useState(false);
  
  const [diario, setDiario] = useState<DiarioExterno | null>(null);
  const [categoriasLocales, setCategoriasLocales] = useState<CategoriaLocal[]>([]);
  const [categoriasExternas, setCategoriasExternas] = useState<CategoriaExterna[]>([]);
  const [mapeos, setMapeos] = useState<CategoriaMapeo[]>([]);
  const [nuevosMapeos, setNuevosMapeos] = useState<{categoriaLocalId: number, categoriaExternaId: number}[]>([]);

  useEffect(() => {
    if (diarioId) {
      cargarDatos();
    }
  }, [diarioId]);

  const cargarDatos = async () => {
    try {
      setLoading(true);
      
      // Cargar datos en paralelo
      const [diarioRes, categoriasLocalesRes, mapeosRes] = await Promise.all([
        fetch(`/api/admin/diarios-externos/${diarioId}`),
        fetch('/api/categorias'),
        fetch(`/api/admin/diarios-externos/${diarioId}/mapeos`)
      ]);

      if (diarioRes.ok) {
        const diarioData = await diarioRes.json();
        setDiario(diarioData.data);
      }

      if (categoriasLocalesRes.ok) {
        const categoriasData = await categoriasLocalesRes.json();
        setCategoriasLocales(categoriasData);
      }

      if (mapeosRes.ok) {
        const mapeosData = await mapeosRes.json();
        setMapeos(mapeosData.data || []);
      }

      // Cargar categorías externas
      await cargarCategoriasExternas();

    } catch (error) {
      console.error('Error al cargar datos:', error);
      toast.error('Error al cargar los datos');
    } finally {
      setLoading(false);
    }
  };

  const cargarCategoriasExternas = async () => {
    try {
      setLoadingExternas(true);
      console.log(`🔄 Cargando categorías externas para diario ID: ${diarioId}`);

      const response = await fetch(`/api/diarios-externos/${diarioId}/categorias`);
      console.log(`📡 Respuesta del servidor: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Datos recibidos:', data);
        setCategoriasExternas(data.data || []);
        toast.success(`${data.data?.length || 0} categorías externas cargadas`);
      } else {
        const error = await response.json();
        console.error('❌ Error del servidor:', error);
        toast.error(error.error || 'Error al cargar categorías externas');
      }
    } catch (error) {
      console.error('❌ Error al cargar categorías externas:', error);
      toast.error('Error al conectar con el diario externo');
    } finally {
      setLoadingExternas(false);
    }
  };

  const agregarMapeo = () => {
    setNuevosMapeos([...nuevosMapeos, { categoriaLocalId: 0, categoriaExternaId: 0 }]);
  };

  const actualizarNuevoMapeo = (index: number, field: 'categoriaLocalId' | 'categoriaExternaId', value: number) => {
    const nuevos = [...nuevosMapeos];
    nuevos[index][field] = value;
    setNuevosMapeos(nuevos);
  };

  const eliminarNuevoMapeo = (index: number) => {
    const nuevos = [...nuevosMapeos];
    nuevos.splice(index, 1);
    setNuevosMapeos(nuevos);
  };

  const eliminarMapeo = async (mapeoId: number) => {
    try {
      const response = await fetch(`/api/admin/diarios-externos/${diarioId}/mapeos/${mapeoId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setMapeos(mapeos.filter(m => m.id !== mapeoId));
        toast.success('Mapeo eliminado exitosamente');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al eliminar mapeo');
      }
    } catch (error) {
      console.error('Error al eliminar mapeo:', error);
      toast.error('Error al eliminar mapeo');
    }
  };

  const guardarMapeos = async () => {
    try {
      setSaving(true);

      // Filtrar mapeos válidos
      const mapeosValidos = nuevosMapeos.filter(m => m.categoriaLocalId > 0 && m.categoriaExternaId > 0);

      if (mapeosValidos.length === 0) {
        toast.error('No hay mapeos válidos para guardar');
        return;
      }

      const response = await fetch(`/api/admin/diarios-externos/${diarioId}/mapeos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mapeos: mapeosValidos }),
      });

      if (response.ok) {
        toast.success('Mapeos guardados exitosamente');
        setNuevosMapeos([]);
        await cargarDatos(); // Recargar datos
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al guardar mapeos');
      }
    } catch (error) {
      console.error('Error al guardar mapeos:', error);
      toast.error('Error al guardar mapeos');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Cargando configuración...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/admin/diarios-externos')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Volver</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Mapeo de Categorías
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {diario?.nombre} - Configurar correspondencia entre categorías
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={cargarCategoriasExternas}
              disabled={loadingExternas}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${loadingExternas ? 'animate-spin' : ''}`} />
              <span>Actualizar Externas</span>
            </Button>
          </div>
        </div>

        {/* Mapeos Existentes */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Mapeos Configurados</CardTitle>
            <CardDescription>
              Correspondencias activas entre categorías locales y externas
            </CardDescription>
          </CardHeader>
          <CardContent>
            {mapeos.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                No hay mapeos configurados
              </p>
            ) : (
              <div className="space-y-2">
                {mapeos.map((mapeo) => (
                  <div key={mapeo.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: mapeo.categoriaLocal.color }}
                        />
                        <span className="font-medium">{mapeo.categoriaLocal.nombre}</span>
                      </div>
                      <span className="text-gray-400">→</span>
                      <span className="text-gray-600 dark:text-gray-300">
                        ID Externa: {mapeo.categoriaExternaId}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => eliminarMapeo(mapeo.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Nuevos Mapeos */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Agregar Nuevos Mapeos</CardTitle>
                <CardDescription>
                  Crear nuevas correspondencias entre categorías
                </CardDescription>
              </div>
              <Button onClick={agregarMapeo} className="flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Agregar Mapeo</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {nuevosMapeos.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Haz clic en "Agregar Mapeo" para crear una nueva correspondencia
              </p>
            ) : (
              <div className="space-y-4">
                {nuevosMapeos.map((mapeo, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex-1">
                      <label className="block text-sm font-medium mb-1">Categoría Local</label>
                      <select
                        value={mapeo.categoriaLocalId}
                        onChange={(e) => actualizarNuevoMapeo(index, 'categoriaLocalId', parseInt(e.target.value))}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                      >
                        <option value={0}>Seleccionar categoría local</option>
                        {categoriasLocales.map((cat) => (
                          <option key={cat.id} value={cat.id}>
                            {cat.nombre}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="flex-1">
                      <label className="block text-sm font-medium mb-1">Categoría Externa</label>
                      <select
                        value={mapeo.categoriaExternaId}
                        onChange={(e) => actualizarNuevoMapeo(index, 'categoriaExternaId', parseInt(e.target.value))}
                        className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                        disabled={loadingExternas}
                      >
                        <option value={0}>Seleccionar categoría externa</option>
                        {categoriasExternas.map((cat) => (
                          <option key={cat.id} value={cat.id}>
                            {cat.name} (ID: {cat.id})
                          </option>
                        ))}
                      </select>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => eliminarNuevoMapeo(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setNuevosMapeos([])}
                    disabled={saving}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={guardarMapeos}
                    disabled={saving}
                    className="flex items-center space-x-2"
                  >
                    {saving ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    <span>Guardar Mapeos</span>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
