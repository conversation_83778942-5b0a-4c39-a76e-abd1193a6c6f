'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface DiarioExternoForm {
  nombre: string;
  urlBase: string;
  bearerToken: string;
  categoriaImagenId: string;
}

export default function NuevoDiarioExterno() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<DiarioExternoForm>({
    nombre: '',
    urlBase: '',
    bearerToken: '',
    categoriaImagenId: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Preparar los datos para enviar
      const dataToSend = {
        nombre: formData.nombre,
        urlBase: formData.urlBase,
        bearerToken: formData.bearerToken,
        categoriaImagenId: formData.categoriaImagenId,
      };

      console.log('Enviando datos:', dataToSend);

      const response = await fetch('/api/admin/diarios-externos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSend),
      });

      const responseData = await response.json();
      console.log('Respuesta del servidor:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || 'Error al crear el diario externo');
      }

      toast.success('Diario externo creado exitosamente');
      router.push('/admin/diarios-externos');
    } catch (error) {
      console.error('Error completo:', error);
      toast.error(error instanceof Error ? error.message : 'Error al crear el diario externo');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver
        </Button>
        <h1 className="text-3xl font-bold">Nuevo Diario Externo</h1>
        <p className="text-muted-foreground">
          Configura un nuevo diario externo para recibir y publicar noticias
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Información Básica</CardTitle>
            <CardDescription>
              Datos principales del diario externo
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nombre">Nombre del Diario *</Label>
                <Input
                  id="nombre"
                  value={formData.nombre}
                  onChange={(e) => updateFormData('nombre', e.target.value)}
                  placeholder="Ej: El Diario Digital"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="urlBase">URL Base del API *</Label>
                <Input
                  id="urlBase"
                  type="url"
                  value={formData.urlBase}
                  onChange={(e) => updateFormData('urlBase', e.target.value)}
                  placeholder="https://backend-dsur.modlayer.com"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bearerToken">Token de Autenticación *</Label>
                <Input
                  id="bearerToken"
                  type="password"
                  value={formData.bearerToken}
                  onChange={(e) => updateFormData('bearerToken', e.target.value)}
                  placeholder="Bearer token para autenticación"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="categoriaImagenId">ID Categoría de Imagen *</Label>
                <Input
                  id="categoriaImagenId"
                  type="number"
                  value={formData.categoriaImagenId}
                  onChange={(e) => updateFormData('categoriaImagenId', e.target.value)}
                  placeholder="ID de la categoría para imágenes"
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>



        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancelar
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creando...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Crear Diario Externo
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
