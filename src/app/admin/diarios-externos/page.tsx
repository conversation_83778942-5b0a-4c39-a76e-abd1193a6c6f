'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { 
  ArrowLeft, 
  Plus, 
  Globe, 
  Settings, 
  CheckCircle, 
  XCircle, 
  Edit, 
  Trash2, 
  TestTube,
  Loader2,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import AppHeader from '@/components/AppHeader';
import { DiarioExterno } from '@/types/external-publication';

export default function DiariosExternosAdmin() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [diarios, setDiarios] = useState<DiarioExterno[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testingConnection, setTestingConnection] = useState<number | null>(null);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (status === 'authenticated') {
      checkAdminAccess();
    }
  }, [status, router]);

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/admin/users?limit=1');
      if (!response.ok || response.status === 403) {
        router.push('/dashboard');
        return;
      }
      loadDiarios();
    } catch (error) {
      router.push('/dashboard');
    }
  };

  const loadDiarios = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/diarios-externos?includeMapeos=true');
      const data = await response.json();
      
      if (data.success) {
        setDiarios(data.data);
      } else {
        setError('Error al cargar diarios externos');
      }
    } catch (error) {
      setError('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async (diarioId: number) => {
    try {
      setTestingConnection(diarioId);
      const response = await fetch(`/api/diarios-externos/${diarioId}/test-connection`, {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        alert(`✅ ${result.data.message}`);
        // Recargar diarios para actualizar estado
        loadDiarios();
      } else {
        alert(`❌ Error: ${result.data?.message || 'Error de conexión'}`);
      }
    } catch (error) {
      alert('❌ Error al probar conexión');
    } finally {
      setTestingConnection(null);
    }
  };

  const handleToggleActive = async (diarioId: number, activo: boolean) => {
    try {
      const response = await fetch('/api/diarios-externos', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: [diarioId],
          activo: !activo
        }),
      });

      if (response.ok) {
        loadDiarios();
      } else {
        alert('Error al cambiar estado del diario');
      }
    } catch (error) {
      alert('Error de conexión');
    }
  };

  const handleDelete = async (diarioId: number, nombre: string) => {
    if (!confirm(`¿Estás seguro de que quieres eliminar el diario "${nombre}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/diarios-externos/${diarioId}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        alert('✅ Diario eliminado exitosamente');
        loadDiarios();
      } else {
        alert(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      alert('❌ Error al eliminar diario');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-background">
        <AppHeader 
          title="Diarios Externos" 
          showBackButton={true}
          backUrl="/admin"
          backText="Volver al Panel Admin"
        />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando...</span>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AppHeader 
        title="Gestión de Diarios Externos" 
        subtitle="Configurar diarios para publicación externa"
        showBackButton={true}
        backUrl="/admin"
        backText="Volver al Panel Admin"
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header con botón de agregar */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground">Diarios Externos</h2>
            <p className="text-muted-foreground">
              Gestiona los diarios donde se pueden publicar noticias externamente
            </p>
          </div>
          <button
            onClick={() => router.push('/admin/diarios-externos/nuevo')}
            className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Agregar Diario</span>
          </button>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <span className="text-destructive">{error}</span>
            </div>
          </div>
        )}

        {/* Lista de Diarios */}
        {diarios.length === 0 ? (
          <div className="text-center py-12">
            <Globe className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              No hay diarios configurados
            </h3>
            <p className="text-muted-foreground mb-6">
              Agrega tu primer diario externo para comenzar a publicar noticias.
            </p>
            <button
              onClick={() => router.push('/admin/diarios-externos/nuevo')}
              className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors mx-auto"
            >
              <Plus className="h-4 w-4" />
              <span>Agregar Primer Diario</span>
            </button>
          </div>
        ) : (
          <div className="grid gap-6">
            {diarios.map((diario) => (
              <div
                key={diario.id}
                className="bg-card border border-border rounded-lg p-6 shadow-sm"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg ${
                      diario.activo 
                        ? 'bg-green-100 dark:bg-green-900/30' 
                        : 'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      <Globe className={`h-6 w-6 ${
                        diario.activo 
                          ? 'text-green-600 dark:text-green-400' 
                          : 'text-red-600 dark:text-red-400'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-lg font-semibold text-card-foreground">
                          {diario.nombre}
                        </h3>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          diario.activo
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}>
                          {diario.activo ? 'Activo' : 'Inactivo'}
                        </div>
                      </div>
                      <p className="text-muted-foreground mb-2">{diario.urlBase}</p>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{(diario as {_count?: {categoriaMapeos?: number}})._count?.categoriaMapeos || 0} categorías mapeadas</span>
                        <span>{(diario as {_count?: {publicacionesExternas?: number}})._count?.publicacionesExternas || 0} publicaciones</span>
                        <span>ID Imagen: {diario.categoriaImagenId}</span>
                      </div>
                    </div>
                  </div>

                  {/* Botones de acción */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleTestConnection(diario.id)}
                      disabled={testingConnection === diario.id}
                      className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                      title="Probar conexión"
                    >
                      {testingConnection === diario.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4" />
                      )}
                    </button>
                    
                    <button
                      onClick={() => router.push(`/admin/diarios-externos/${diario.id}/categorias`)}
                      className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                      title="Configurar mapeo de categorías"
                    >
                      <Settings className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => router.push(`/admin/diarios-externos/${diario.id}/editar`)}
                      className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                      title="Editar diario"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleToggleActive(diario.id, diario.activo)}
                      className="p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-lg transition-colors"
                      title={diario.activo ? 'Desactivar' : 'Activar'}
                    >
                      {diario.activo ? (
                        <XCircle className="h-4 w-4" />
                      ) : (
                        <CheckCircle className="h-4 w-4" />
                      )}
                    </button>

                    <button
                      onClick={() => handleDelete(diario.id, diario.nombre)}
                      className="p-2 text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
                      title="Eliminar diario"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
