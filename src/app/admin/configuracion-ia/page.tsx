'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  Bot, 
  Plus, 
  Settings, 
  ArrowLeft
} from 'lucide-react';

export default function ConfiguracionIAPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session?.user?.role !== 'ADMIN') {
      router.push('/');
      return;
    }
    setLoading(false);
  }, [session, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-2">
                <button
                  onClick={() => router.push('/admin/ai-config')}
                  className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  Volver a Configuración IA
                </button>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <Bot className="h-8 w-8 mr-3 text-purple-600 dark:text-purple-400" />
                Gestión de Configuraciones IA
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Administra las configuraciones de modelos IA para generación de imágenes
              </p>
            </div>
            <button
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
            >
              <Plus className="h-5 w-5" />
              <span>Nueva Configuración</span>
            </button>
          </div>
        </div>

        {/* Contenido */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Configuraciones de IA para Generación de Imágenes
            </h2>
          </div>

          <div className="p-8 text-center">
            <Bot className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Sistema de Generación de Imágenes
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              El sistema de generación de imágenes con Veo3 de Google Gemini está configurado y funcionando correctamente.
            </p>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4">
              <p className="text-green-700 dark:text-green-300 text-sm">
                ✅ Sistema activo y listo para generar imágenes automáticamente
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
