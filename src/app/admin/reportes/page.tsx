'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  BarChart3, 
  Users, 
  FileText, 
  Bot, 
  TrendingUp, 
  Calendar,
  Download,
  RefreshCw,
  ArrowLeft,
  Activity,
  Database,
  Cpu,
  Image
} from 'lucide-react';

interface SystemStats {
  usuarios: {
    total: number;
    activos: number;
    porRol: { [key: string]: number };
  };
  noticias: {
    total: number;
    publicadas: number;
    borrador: number;
    porCategoria: Array<{ nombre: string; count: number; color: string }>;
  };
  ia: {
    configuraciones: {
      activas: number;
      total: number;
      inactivas: number;
      porProveedor: { [key: string]: number };
    };
    generacionesImagen: {
      total: number;
      esteMes: number;
      estaSemana: number;
      promedioDiario: number;
    };
    generacionesTexto: {
      total: number;
      esteMes: number;
    };
    limites: any;
    usosPorConfiguracion: any[];
    metricas: any;
  };
  correcciones: {
    total: number;
    pendientes: number;
    completadas: number;
    porPrioridad: { [key: string]: number };
  };
}

export default function AdminReportesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    } else if (session?.user?.role !== 'ADMIN') {
      router.push('/dashboard');
    }
  }, [status, session, router]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      
      // Obtener estadísticas de diferentes endpoints
      const [usuariosRes, noticiasRes, iaRes, correccionesRes] = await Promise.all([
        fetch('/api/admin/stats/usuarios'),
        fetch('/api/noticias/stats'),
        fetch('/api/admin/stats/ia'),
        fetch('/api/correcciones/stats')
      ]);

      const [usuarios, noticias, ia, correcciones] = await Promise.all([
        usuariosRes.ok ? usuariosRes.json() : { total: 0, activos: 0, porRol: {} },
        noticiasRes.ok ? noticiasRes.json() : { total: 0, publicadas: 0, borrador: 0, porCategoria: [] },
        iaRes.ok ? iaRes.json() : {
          configuraciones: { activas: 0, total: 0, inactivas: 0, porProveedor: {} },
          generacionesImagen: { total: 0, esteMes: 0, estaSemana: 0, promedioDiario: 0 },
          generacionesTexto: { total: 0, esteMes: 0 },
          limites: { total: 0, estadisticas: { criticos: 0, altos: 0, normales: 0 }, detalles: [] },
          usosPorConfiguracion: [],
          metricas: { eficienciaUso: 0, crecimientoSemanal: 0 }
        },
        correccionesRes.ok ? correccionesRes.json() : { total: 0, pendientes: 0, completadas: 0, porPrioridad: {} }
      ]);

      setStats({ usuarios, noticias, ia, correcciones });
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error al cargar estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user?.role === 'ADMIN') {
      fetchStats();
    }
  }, [session]);

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando reportes...</p>
        </div>
      </div>
    );
  }

  if (session?.user?.role !== 'ADMIN') {
    return null;
  }

  const StatCard = ({ title, value, icon: Icon, color, subtitle }: any) => (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin')}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Volver al Panel Admin
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <BarChart3 className="h-6 w-6 mr-2 text-blue-600" />
                  Reportes y Analytics
                </h1>
                <p className="text-sm text-gray-600">
                  Estadísticas y métricas del sistema
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={fetchStats}
                className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Actualizar
              </button>
              <button className="flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Exportar
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Última actualización */}
        <div className="mb-6 text-sm text-gray-600">
          <Calendar className="w-4 h-4 inline mr-2" />
          Última actualización: {lastUpdate.toLocaleString()}
        </div>

        {/* Estadísticas Generales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Usuarios"
            value={stats?.usuarios.total || 0}
            subtitle={`${stats?.usuarios.activos || 0} activos`}
            icon={Users}
            color="bg-blue-500"
          />
          <StatCard
            title="Total Noticias"
            value={stats?.noticias.total || 0}
            subtitle={`${stats?.noticias.publicadas || 0} publicadas`}
            icon={FileText}
            color="bg-green-500"
          />
          <StatCard
            title="Generaciones IA"
            value={stats?.ia.generacionesImagen?.esteMes || 0}
            subtitle="Imágenes este mes"
            icon={Image}
            color="bg-purple-500"
          />
          <StatCard
            title="Correcciones"
            value={stats?.correcciones.total || 0}
            subtitle={`${stats?.correcciones.pendientes || 0} pendientes`}
            icon={Activity}
            color="bg-orange-500"
          />
        </div>

        {/* Gráficos y Detalles */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Usuarios por Rol */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Usuarios por Rol</h3>
            <div className="space-y-3">
              {Object.entries(stats?.usuarios.porRol || {}).map(([rol, count]) => (
                <div key={rol} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">{rol}</span>
                  <span className="text-sm font-bold text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Noticias por Categoría */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Noticias por Categoría</h3>
            <div className="space-y-3">
              {stats?.noticias.porCategoria.slice(0, 5).map((categoria, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-3"
                      style={{ backgroundColor: categoria.color }}
                    ></div>
                    <span className="text-sm font-medium text-gray-600">{categoria.nombre}</span>
                  </div>
                  <span className="text-sm font-bold text-gray-900">{categoria.count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Configuraciones de IA */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Estado de IA</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Configuraciones Activas</span>
                <span className="text-lg font-bold text-green-600">{stats?.ia.configuraciones?.activas || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Generaciones Este Mes</span>
                <span className="text-lg font-bold text-blue-600">{stats?.ia.generacionesImagen?.esteMes || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Generaciones</span>
                <span className="text-lg font-bold text-purple-600">{stats?.ia.generacionesImagen?.total || 0}</span>
              </div>
            </div>
          </div>

          {/* Correcciones por Prioridad */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Correcciones por Prioridad</h3>
            <div className="space-y-3">
              {Object.entries(stats?.correcciones.porPrioridad || {}).map(([prioridad, count]) => (
                <div key={prioridad} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 capitalize">{prioridad}</span>
                  <span className="text-sm font-bold text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
