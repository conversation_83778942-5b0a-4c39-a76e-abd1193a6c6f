'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  Settings,
  CheckCircle,
  XCircle,
  Loader2,
  Save,
  TestTube,
  AlertTriangle,
  Eye,
  EyeOff,
  Refresh<PERSON><PERSON>,
  Clock
} from 'lucide-react';
import ModelSelector from '@/components/admin/ModelSelector';

interface AIConfig {
  id?: number;
  openaiApiKey?: string;
  openaiModel: string;
  openaiMaxTokens: number;
  openaiTemperature: number;
  geminiApiKey?: string;
  geminiModel: string;
  geminiMaxTokens: number;
  geminiTemperature: number;
  defaultProvider: 'OPENAI' | 'GEMINI';
  isActive: boolean;
}

interface ConnectionStatus {
  provider: string;
  isConnected: boolean;
  error?: string;
  timestamp?: string;
}

// Models are now dynamically loaded via ModelSelector component

export default function AIConfigPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const [config, setConfig] = useState<AIConfig>({
    openaiModel: 'gpt-3.5-turbo',
    openaiMaxTokens: 2000,
    openaiTemperature: 0.7,
    geminiModel: 'gemini-2.5-flash',
    geminiMaxTokens: 2000,
    geminiTemperature: 0.7,
    defaultProvider: 'OPENAI',
    isActive: true
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<Record<string, ConnectionStatus>>({});
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});
  const [refreshingModels, setRefreshingModels] = useState(false);
  const [modelCacheStats, setModelCacheStats] = useState<any>(null);

  // Verificar autenticación y permisos
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user) {
      router.push('/auth/signin');
      return;
    }
    
    if (session.user.role !== 'ADMIN') {
      router.push('/admin');
      return;
    }
    
    loadConfig();
    loadModelCacheStats();
  }, [session, status, router]);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/ai-config');
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data);
      } else {
        console.error('Error loading AI config');
      }
    } catch (error) {
      console.error('Error loading AI config:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/admin/ai-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        const savedConfig = await response.json();
        setConfig(savedConfig);
        alert('✅ Configuración guardada exitosamente');
      } else {
        const error = await response.json();
        alert(`❌ Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error saving config:', error);
      alert('❌ Error al guardar la configuración');
    } finally {
      setSaving(false);
    }
  };

  const testConnection = async (provider: 'OPENAI' | 'GEMINI') => {
    try {
      setTesting(provider);
      
      const apiKey = provider === 'OPENAI' ? config.openaiApiKey : config.geminiApiKey;
      
      const response = await fetch('/api/admin/ai-config/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider,
          apiKey: apiKey === '***' ? undefined : apiKey
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setConnectionStatus(prev => ({
          ...prev,
          [provider]: result
        }));
      } else {
        const error = await response.json();
        setConnectionStatus(prev => ({
          ...prev,
          [provider]: {
            provider,
            isConnected: false,
            error: error.error,
            timestamp: new Date().toISOString()
          }
        }));
      }
    } catch (error) {
      console.error(`Error testing ${provider} connection:`, error);
      setConnectionStatus(prev => ({
        ...prev,
        [provider]: {
          provider,
          isConnected: false,
          error: 'Error de conexión',
          timestamp: new Date().toISOString()
        }
      }));
    } finally {
      setTesting(null);
    }
  };

  const toggleApiKeyVisibility = (provider: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const refreshAllModels = async () => {
    try {
      setRefreshingModels(true);

      const response = await fetch('/api/admin/ai-config/models', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'refresh',
          provider: 'ALL'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert('✅ Modelos actualizados exitosamente');

        // Reload the page to refresh model selectors
        window.location.reload();
      } else {
        const error = await response.json();
        alert(`❌ Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error refreshing models:', error);
      alert('❌ Error al actualizar modelos');
    } finally {
      setRefreshingModels(false);
    }
  };

  const loadModelCacheStats = async () => {
    try {
      const response = await fetch('/api/admin/ai-config/models?stats=true');
      if (response.ok) {
        const result = await response.json();
        setModelCacheStats(result.cacheStats);
      }
    } catch (error) {
      console.error('Error loading model cache stats:', error);
    }
  };

  const renderConnectionStatus = (provider: 'OPENAI' | 'GEMINI') => {
    const status = connectionStatus[provider];
    
    if (!status) return null;
    
    return (
      <div className={`flex items-center space-x-2 text-sm ${
        status.isConnected ? 'text-green-600' : 'text-red-600'
      }`}>
        {status.isConnected ? (
          <CheckCircle className="h-4 w-4" />
        ) : (
          <XCircle className="h-4 w-4" />
        )}
        <span>
          {status.isConnected ? 'Conectado' : `Error: ${status.error}`}
        </span>
        {status.timestamp && (
          <span className="text-gray-500">
            ({new Date(status.timestamp).toLocaleTimeString()})
          </span>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Cargando configuración...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Bot className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              Configuración de IA
            </h1>
          </div>
          <p className="text-gray-600">
            Configura los proveedores de inteligencia artificial para la reescritura de noticias.
          </p>
        </div>

        <div className="space-y-8">
          {/* Model Discovery Status */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-indigo-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Descubrimiento Automático de Modelos
                </h2>
              </div>
              <button
                onClick={refreshAllModels}
                disabled={refreshingModels}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 disabled:opacity-50 rounded-md transition-colors"
              >
                {refreshingModels ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                <span>Actualizar Todos los Modelos</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Estado del Sistema</span>
                </div>
                <p className="text-sm text-blue-700">
                  Los modelos se actualizan automáticamente cada 24 horas y se almacenan en cache para un acceso rápido.
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Cache de OpenAI</span>
                </div>
                <p className="text-sm text-green-700">
                  {modelCacheStats?.openaiCacheAge !== undefined
                    ? `Actualizado hace ${Math.round(modelCacheStats.openaiCacheAge)} horas`
                    : 'Cargando...'
                  }
                </p>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-900">Cache de Gemini</span>
                </div>
                <p className="text-sm text-purple-700">
                  {modelCacheStats?.geminiCacheAge !== undefined
                    ? `Actualizado hace ${Math.round(modelCacheStats.geminiCacheAge)} horas`
                    : 'Cargando...'
                  }
                </p>
              </div>
            </div>

            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600">
                <strong>Funcionalidades:</strong> Los modelos se descubren automáticamente desde las APIs de OpenAI y Google Gemini.
                Los nuevos modelos aparecen automáticamente con indicadores de estado (Nuevo, Beta, Activo, Obsoleto).
                El cache se actualiza automáticamente pero puedes forzar una actualización manual usando el botón de arriba.
              </p>
            </div>
          </div>

          {/* Configuración Global */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-6">
              <Settings className="h-5 w-5 text-gray-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Configuración Global
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Proveedor por Defecto
                </label>
                <select
                  value={config.defaultProvider}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    defaultProvider: e.target.value as 'OPENAI' | 'GEMINI'
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="OPENAI">OpenAI</option>
                  <option value="GEMINI">Google Gemini</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Proveedor utilizado por defecto para todos los diarios
                </p>
              </div>
            </div>
          </div>

          {/* Configuración OpenAI */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-green-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  OpenAI Configuration
                </h2>
              </div>
              <button
                onClick={() => testConnection('OPENAI')}
                disabled={testing === 'OPENAI'}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 rounded-md transition-colors"
              >
                {testing === 'OPENAI' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4" />
                )}
                <span>Probar Conexión</span>
              </button>
            </div>

            {renderConnectionStatus('OPENAI')}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys.openai ? 'text' : 'password'}
                    value={config.openaiApiKey || ''}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      openaiApiKey: e.target.value
                    }))}
                    placeholder="sk-..."
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => toggleApiKeyVisibility('openai')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKeys.openai ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <ModelSelector
                provider="OPENAI"
                selectedModel={config.openaiModel}
                onModelChange={(model) => setConfig(prev => ({
                  ...prev,
                  openaiModel: model
                }))}
                apiKey={config.openaiApiKey}
                disabled={saving}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Tokens
                </label>
                <input
                  type="number"
                  value={config.openaiMaxTokens}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    openaiMaxTokens: parseInt(e.target.value) || 2000
                  }))}
                  min="100"
                  max="4000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Temperature
                </label>
                <input
                  type="number"
                  value={config.openaiTemperature}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    openaiTemperature: parseFloat(e.target.value) || 0.7
                  }))}
                  min="0"
                  max="2"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Configuración Google Gemini */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-purple-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Google Gemini Configuration
                </h2>
              </div>
              <button
                onClick={() => testConnection('GEMINI')}
                disabled={testing === 'GEMINI'}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50 rounded-md transition-colors"
              >
                {testing === 'GEMINI' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <TestTube className="h-4 w-4" />
                )}
                <span>Probar Conexión</span>
              </button>
            </div>

            {renderConnectionStatus('GEMINI')}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API Key
                </label>
                <div className="relative">
                  <input
                    type={showApiKeys.gemini ? 'text' : 'password'}
                    value={config.geminiApiKey || ''}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      geminiApiKey: e.target.value
                    }))}
                    placeholder="AIza..."
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                  <button
                    type="button"
                    onClick={() => toggleApiKeyVisibility('gemini')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKeys.gemini ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <ModelSelector
                provider="GEMINI"
                selectedModel={config.geminiModel}
                onModelChange={(model) => setConfig(prev => ({
                  ...prev,
                  geminiModel: model
                }))}
                apiKey={config.geminiApiKey}
                disabled={saving}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Tokens
                </label>
                <input
                  type="number"
                  value={config.geminiMaxTokens}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    geminiMaxTokens: parseInt(e.target.value) || 2000
                  }))}
                  min="100"
                  max="8000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Temperature
                </label>
                <input
                  type="number"
                  value={config.geminiTemperature}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    geminiTemperature: parseFloat(e.target.value) || 0.7
                  }))}
                  min="0"
                  max="2"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>
          </div>

          {/* Generación de Imágenes */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-indigo-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Generación de Imágenes con IA
                </h2>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/admin/create-ai-tables', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                      });
                      const data = await response.json();
                      if (response.ok) {
                        alert('✅ Tablas de IA creadas exitosamente');
                        window.location.reload();
                      } else {
                        alert(`❌ Error: ${data.error}`);
                      }
                    } catch (error) {
                      alert(`❌ Error: ${error}`);
                    }
                  }}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
                >
                  <Bot className="h-4 w-4" />
                  <span>Inicializar IA</span>
                </button>
                <button
                  onClick={() => router.push('/admin/configuracion-ia')}
                  className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 hover:bg-indigo-100 rounded-md transition-colors"
                >
                  <Settings className="h-4 w-4" />
                  <span>Gestionar Configuraciones</span>
                </button>
              </div>
            </div>

            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <Bot className="h-6 w-6 text-indigo-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Google Gemini - Generación de Imágenes
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Sistema avanzado de generación de imágenes usando los modelos Imagen 3/4 y Gemini 2.0 Flash de Google.
                    Permite crear imágenes automáticamente basadas en el contenido de las noticias con calidad profesional.
                    <strong> Utiliza la misma API key de Gemini configurada arriba.</strong>
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-white rounded-lg p-4 border border-indigo-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-900">Características</span>
                      </div>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Imagen 3/4 (fotorrealismo)</li>
                        <li>• Gemini 2.0 Flash (conversacional)</li>
                        <li>• Múltiples relaciones de aspecto</li>
                        <li>• Marca de agua SynthID</li>
                      </ul>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-indigo-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Settings className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-gray-900">Control</span>
                      </div>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Límites por usuario</li>
                        <li>• Configuración flexible</li>
                        <li>• Monitoreo de uso</li>
                        <li>• Historial completo</li>
                      </ul>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-indigo-200">
                      <div className="flex items-center space-x-2 mb-2">
                        <Bot className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-gray-900">Integración</span>
                      </div>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Formulario de noticias</li>
                        <li>• API REST completa</li>
                        <li>• Panel administrativo</li>
                        <li>• Estadísticas detalladas</li>
                      </ul>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">Estado del sistema:</span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Activo
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Configurado y listo para usar
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Información y Advertencias */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800 mb-2">
                  Información Importante
                </h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Las API keys se almacenan de forma segura en la base de datos</li>
                  <li>• Puedes usar variables de entorno (OPENAI_API_KEY, GEMINI_API_KEY) como respaldo</li>
                  <li>• Los diarios pueden configurarse individualmente para usar un proveedor específico</li>
                  <li>• La configuración global se aplica a todos los diarios que no tengan configuración específica</li>
                  <li>• La generación de imágenes tiene límites configurables por usuario y período</li>
                  <li>• Asegúrate de probar las conexiones antes de guardar</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Botones de Acción */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={() => router.push('/admin')}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={saveConfig}
              disabled={saving}
              className="flex items-center space-x-2 px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-md transition-colors"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{saving ? 'Guardando...' : 'Guardar Configuración'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
