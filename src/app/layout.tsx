import type { Metada<PERSON> } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import "../styles/editor.css";
import ClientSessionProvider from "@/components/providers/ClientSessionProvider";
import { ThemeProvider } from "@/components/theme-provider";
import CronProvider from "@/components/providers/CronProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Panel Unificado V2",
  description: "Sistema moderno de gestión de correcciones",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background font-sans`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <ClientSessionProvider>
            <CronProvider>
              {children}
            </CronProvider>
          </ClientSessionProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
