import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🧪 Creando publicación de prueba...');

    // Buscar una noticia existente
    const noticia = await prisma.noticia.findFirst({
      where: {
        estado: {
          in: ['EN_REVISION', 'APROBADA', 'PUBLICADA']
        }
      },
      include: {
        categoria: true,
        user: true
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'No hay noticias disponibles para crear publicación de prueba' }, { status: 400 });
    }

    // Buscar un diario externo activo
    const diario = await prisma.diarioExterno.findFirst({
      where: { activo: true }
    });

    if (!diario) {
      return NextResponse.json({ error: 'No hay diarios externos activos' }, { status: 400 });
    }

    // Crear publicación externa de prueba
    const publicacionPrueba = await prisma.publicacionExterna.create({
      data: {
        noticiaId: noticia.id,
        diarioExternoId: diario.id,
        estado: 'EXITOSO',
        urlPublicacion: `https://ejemplo-diario.com/noticia/${noticia.id}`,
        metadatos: JSON.stringify({
          test: true,
          creadoPor: 'API de prueba',
          fecha: new Date().toISOString(),
          tituloPublicado: noticia.titulo,
          imagenPublicadaUrl: noticia.imagenUrl || null,
          fechaPublicacion: new Date().toISOString() // Guardamos en metadatos
        })
      }
    });

    // Actualizar estado de la noticia si no está publicada
    if (noticia.estado !== 'PUBLICADA') {
      await prisma.noticia.update({
        where: { id: noticia.id },
        data: {
          estado: 'PUBLICADA',
          fechaPublicacion: new Date(),
          publicada: true
        }
      });
    }

    console.log(`✅ Publicación de prueba creada: ID ${publicacionPrueba.id}`);

    return NextResponse.json({
      success: true,
      message: 'Publicación de prueba creada exitosamente',
      data: {
        publicacion: {
          id: publicacionPrueba.id,
          noticiaId: publicacionPrueba.noticiaId,
          noticiaTitulo: noticia.titulo,
          diarioNombre: diario.nombre,
          estado: publicacionPrueba.estado,
          urlPublicacion: publicacionPrueba.urlPublicacion,
          fechaCreacion: publicacionPrueba.createdAt
        },
        noticia: {
          id: noticia.id,
          titulo: noticia.titulo,
          estadoAnterior: noticia.estado,
          estadoNuevo: 'PUBLICADA'
        }
      }
    });

  } catch (error) {
    console.error('❌ Error al crear publicación de prueba:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar si ya hay publicaciones exitosas
    const publicacionesExitosas = await prisma.publicacionExterna.count({
      where: { estado: 'EXITOSO' }
    });

    // Contar noticias disponibles
    const noticiasDisponibles = await prisma.noticia.count({
      where: {
        estado: {
          in: ['EN_REVISION', 'APROBADA', 'PUBLICADA']
        }
      }
    });

    // Contar diarios activos
    const diariosActivos = await prisma.diarioExterno.count({
      where: { activo: true }
    });

    return NextResponse.json({
      success: true,
      data: {
        publicacionesExitosas,
        noticiasDisponibles,
        diariosActivos,
        puedeCrearPrueba: noticiasDisponibles > 0 && diariosActivos > 0,
        mensaje: publicacionesExitosas > 0 
          ? `Ya hay ${publicacionesExitosas} publicaciones exitosas`
          : 'No hay publicaciones exitosas. Puedes crear una de prueba.'
      }
    });

  } catch (error) {
    console.error('❌ Error al verificar estado:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
