import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/test-scheduling-debug - Debug de programación
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { testDate, testTime } = body;

    console.log('🐛 [SCHEDULING DEBUG] Probando construcción de fechas...');

    const now = new Date();
    const results = [];

    // Test 1: Fecha y hora actual + 5 minutos
    const futureTime = new Date(now.getTime() + 5 * 60000);
    const futureDateStr = futureTime.toISOString().split('T')[0];
    const futureTimeStr = futureTime.toTimeString().slice(0, 5);

    results.push({
      test: 'Fecha automática (+5 min)',
      input: {
        date: futureDateStr,
        time: futureTimeStr
      },
      construction: `${futureDateStr}T${futureTimeStr}:00`,
      dateObject: new Date(`${futureDateStr}T${futureTimeStr}:00`),
      iso: new Date(`${futureDateStr}T${futureTimeStr}:00`).toISOString(),
      isValid: !isNaN(new Date(`${futureDateStr}T${futureTimeStr}:00`).getTime()),
      isFuture: new Date(`${futureDateStr}T${futureTimeStr}:00`) > now
    });

    // Test 2: Fecha y hora proporcionadas por el usuario (si las hay)
    if (testDate && testTime) {
      results.push({
        test: 'Fecha del usuario',
        input: {
          date: testDate,
          time: testTime
        },
        construction: `${testDate}T${testTime}:00`,
        dateObject: new Date(`${testDate}T${testTime}:00`),
        iso: new Date(`${testDate}T${testTime}:00`).toISOString(),
        isValid: !isNaN(new Date(`${testDate}T${testTime}:00`).getTime()),
        isFuture: new Date(`${testDate}T${testTime}:00`) > now
      });
    }

    // Test 3: Hoy a las 22:31 (ejemplo del usuario)
    const todayStr = now.toISOString().split('T')[0];
    const testTimeStr = '22:31';
    const todayTest = new Date(`${todayStr}T${testTimeStr}:00`);

    results.push({
      test: 'Hoy 22:31 (ejemplo)',
      input: {
        date: todayStr,
        time: testTimeStr
      },
      construction: `${todayStr}T${testTimeStr}:00`,
      dateObject: todayTest,
      iso: todayTest.toISOString(),
      isValid: !isNaN(todayTest.getTime()),
      isFuture: todayTest > now,
      diffMinutes: Math.round((todayTest.getTime() - now.getTime()) / (1000 * 60))
    });

    // Test 4: Validaciones de upload-post
    const uploadPostValidations = results.map(result => {
      const scheduledDate = result.dateObject;
      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate() + 365);

      return {
        test: result.test,
        validations: {
          isFuture: scheduledDate > now,
          isWithin365Days: scheduledDate <= maxDate,
          isAtLeast1MinuteFuture: scheduledDate > new Date(now.getTime() + 60000),
          passesAllValidations: (
            scheduledDate > now &&
            scheduledDate <= maxDate &&
            scheduledDate > new Date(now.getTime() + 60000)
          )
        }
      };
    });

    return NextResponse.json({
      success: true,
      currentTime: {
        now: now.toISOString(),
        local: now.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      },
      dateConstructionTests: results,
      uploadPostValidations,
      recommendations: [
        '📅 Usar fecha de hoy + hora futura para pruebas',
        '⏰ Asegurar que la hora sea al menos 1 minuto en el futuro',
        '🌍 Considerar zona horaria del servidor vs cliente',
        '✅ Verificar que la construcción de fecha sea correcta'
      ],
      debugInfo: {
        serverTimezone: process.env.TZ || 'No configurada',
        nodeVersion: process.version,
        platform: process.platform
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [SCHEDULING DEBUG] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en debug de programación',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
