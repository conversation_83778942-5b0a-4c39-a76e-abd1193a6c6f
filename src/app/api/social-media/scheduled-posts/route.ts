import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/social-media/scheduled-posts - Listar publicaciones programadas usando API nativa de upload-post
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📅 [SCHEDULED-POSTS] Obteniendo publicaciones programadas...');

    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key de upload-post no configurada'
      }, { status: 500 });
    }

    try {
      // Llamar a la API nativa de upload-post para obtener publicaciones programadas
      const response = await fetch('https://api.upload-post.com/api/uploadposts/schedule', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error obteniendo publicaciones programadas:', errorText);
        
        return NextResponse.json({
          success: false,
          error: 'Error obteniendo publicaciones programadas',
          details: errorText
        }, { status: response.status });
      }

      const scheduledPosts = await response.json();
      console.log('✅ Publicaciones programadas obtenidas:', scheduledPosts);

      // Transformar los datos para nuestro formato
      const transformedPosts = scheduledPosts.map((post: any) => ({
        jobId: post.job_id,
        scheduledDate: post.scheduled_date,
        postType: post.post_type,
        profileUsername: post.profile_username,
        title: post.title,
        previewUrl: post.preview_url,
        platform: post.post_type === 'text' ? 'twitter/facebook' : 'unknown',
        canCancel: true,
        source: 'upload-post-native'
      }));

      return NextResponse.json({
        success: true,
        scheduledPosts: transformedPosts,
        count: transformedPosts.length,
        source: 'upload-post-native',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error llamando API de upload-post:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error conectando con upload-post',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [SCHEDULED-POSTS] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// DELETE /api/social-media/scheduled-posts - Cancelar publicación programada
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'jobId es requerido'
      }, { status: 400 });
    }

    console.log('🗑️ [CANCEL-SCHEDULED] Cancelando publicación programada:', jobId);

    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key de upload-post no configurada'
      }, { status: 500 });
    }

    try {
      // Llamar a la API nativa de upload-post para cancelar
      const response = await fetch(`https://api.upload-post.com/api/uploadposts/schedule/${jobId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error cancelando publicación:', errorText);
        
        return NextResponse.json({
          success: false,
          error: 'Error cancelando publicación',
          details: errorText
        }, { status: response.status });
      }

      console.log('✅ Publicación cancelada exitosamente');

      return NextResponse.json({
        success: true,
        message: 'Publicación cancelada exitosamente',
        jobId: jobId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error llamando API de upload-post:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error conectando con upload-post',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [CANCEL-SCHEDULED] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
