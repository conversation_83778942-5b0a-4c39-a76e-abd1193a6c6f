import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/social-media/check-db - Verificar estado de la base de datos
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 Verificando estado de la base de datos...');

    const checks = [];

    // Test 1: Conexión básica a Prisma
    try {
      await prisma.$connect();
      checks.push({
        test: 'Prisma Connection',
        status: 'success',
        message: 'Conexión exitosa con Prisma'
      });
    } catch (error) {
      checks.push({
        test: 'Prisma Connection',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error desconocido'
      });
    }

    // Test 2: Verificar tabla social_media_accounts
    try {
      const accountsCount = await prisma.socialMediaAccount.count();
      checks.push({
        test: 'SocialMediaAccount Table',
        status: 'success',
        message: `Tabla existe, ${accountsCount} registros encontrados`
      });
    } catch (error) {
      checks.push({
        test: 'SocialMediaAccount Table',
        status: 'error',
        message: error instanceof Error ? error.message : 'Tabla no existe o error de acceso'
      });
    }

    // Test 3: Verificar tabla social_media_publications
    try {
      const publicationsCount = await prisma.socialMediaPublication.count();
      checks.push({
        test: 'SocialMediaPublication Table',
        status: 'success',
        message: `Tabla existe, ${publicationsCount} registros encontrados`
      });
    } catch (error) {
      checks.push({
        test: 'SocialMediaPublication Table',
        status: 'error',
        message: error instanceof Error ? error.message : 'Tabla no existe o error de acceso'
      });
    }

    // Test 4: Verificar otras tablas principales
    try {
      const noticiasCount = await prisma.noticia.count();
      checks.push({
        test: 'Main Tables (noticias)',
        status: 'success',
        message: `Base de datos principal funcional, ${noticiasCount} noticias`
      });
    } catch (error) {
      checks.push({
        test: 'Main Tables (noticias)',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error en tablas principales'
      });
    }

    // Test 5: Verificar variables de entorno
    const envChecks = {
      DATABASE_URL: !!process.env.DATABASE_URL,
      UPLOAD_POST_API_KEY: !!process.env.UPLOAD_POST_API_KEY,
      NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET
    };

    checks.push({
      test: 'Environment Variables',
      status: Object.values(envChecks).every(Boolean) ? 'success' : 'warning',
      message: `Variables: ${JSON.stringify(envChecks)}`
    });

    const hasErrors = checks.some(check => check.status === 'error');
    const allSuccess = checks.every(check => check.status === 'success');

    return NextResponse.json({
      success: allSuccess,
      hasErrors,
      checks,
      summary: {
        total: checks.length,
        success: checks.filter(c => c.status === 'success').length,
        errors: checks.filter(c => c.status === 'error').length,
        warnings: checks.filter(c => c.status === 'warning').length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error verificando base de datos:', error);
    return NextResponse.json({
      success: false,
      error: 'Error verificando base de datos',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
