import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/debug-job-status - Debug específico del Job ID
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [DEBUG JOB] Debugging específico del Job ID...');

    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key no configurada'
      }, { status: 500 });
    }

    const jobId = 'b087a1a80e8e4890b8cb7b33f31e713a';
    const scheduledTime = '2025-09-09T01:56:00.000Z';
    const now = new Date();

    // Análisis de tiempo
    const scheduledDate = new Date(scheduledTime);
    const isPast = scheduledDate <= now;
    const diffMinutes = Math.round((now.getTime() - scheduledDate.getTime()) / (1000 * 60));

    const timeAnalysis = {
      now: {
        utc: now.toISOString(),
        argentina: now.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })
      },
      scheduled: {
        utc: scheduledTime,
        argentina: scheduledDate.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })
      },
      status: {
        isPast,
        minutesPast: isPast ? diffMinutes : 0,
        shouldHavePublished: isPast
      }
    };

    // Intentar múltiples endpoints de upload-post
    const debugResults = [];

    // 1. Verificar publicaciones programadas
    try {
      console.log('🔍 Verificando /api/uploadposts/schedule...');
      const scheduleResponse = await fetch('https://api.upload-post.com/api/uploadposts/schedule', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      const scheduleText = await scheduleResponse.text();
      debugResults.push({
        endpoint: '/api/uploadposts/schedule',
        status: scheduleResponse.status,
        ok: scheduleResponse.ok,
        contentType: scheduleResponse.headers.get('content-type'),
        rawResponse: scheduleText.substring(0, 500) + (scheduleText.length > 500 ? '...' : ''),
        length: scheduleText.length
      });
    } catch (error) {
      debugResults.push({
        endpoint: '/api/uploadposts/schedule',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }

    // 2. Verificar historial de publicaciones
    try {
      console.log('🔍 Verificando /api/uploadposts...');
      const historyResponse = await fetch('https://api.upload-post.com/api/uploadposts?limit=10', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      const historyText = await historyResponse.text();
      debugResults.push({
        endpoint: '/api/uploadposts (history)',
        status: historyResponse.status,
        ok: historyResponse.ok,
        contentType: historyResponse.headers.get('content-type'),
        rawResponse: historyText.substring(0, 500) + (historyText.length > 500 ? '...' : ''),
        length: historyText.length
      });
    } catch (error) {
      debugResults.push({
        endpoint: '/api/uploadposts (history)',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }

    // 3. Intentar obtener información específica del job
    try {
      console.log('🔍 Verificando job específico...');
      const jobResponse = await fetch(`https://api.upload-post.com/api/uploadposts/schedule/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      const jobText = await jobResponse.text();
      debugResults.push({
        endpoint: `/api/uploadposts/schedule/${jobId}`,
        status: jobResponse.status,
        ok: jobResponse.ok,
        contentType: jobResponse.headers.get('content-type'),
        rawResponse: jobText.substring(0, 500) + (jobText.length > 500 ? '...' : ''),
        length: jobText.length
      });
    } catch (error) {
      debugResults.push({
        endpoint: `/api/uploadposts/schedule/${jobId}`,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
    }

    // Análisis de resultados
    const workingEndpoints = debugResults.filter(r => r.ok);
    const errorEndpoints = debugResults.filter(r => !r.ok || r.error);

    return NextResponse.json({
      success: true,
      jobId,
      timeAnalysis,
      debugResults,
      summary: {
        workingEndpoints: workingEndpoints.length,
        errorEndpoints: errorEndpoints.length,
        totalEndpoints: debugResults.length
      },
      diagnosis: [
        isPast ? `⏰ Pasaron ${diffMinutes} minutos desde la hora programada` : '⏰ Aún no es la hora programada',
        workingEndpoints.length > 0 ? '✅ Al menos un endpoint de upload-post responde' : '❌ Ningún endpoint de upload-post responde',
        '🔍 Revisa las respuestas raw para más detalles',
        '👀 Verifica manualmente en Twitter: @DiariosLive'
      ],
      recommendations: [
        '1. Revisa Twitter manualmente: https://twitter.com/DiariosLive',
        '2. Busca el texto: "Así estará el tiempo este martes"',
        '3. Si no aparece, puede ser problema de upload-post',
        '4. Contacta soporte de upload-post con Job ID: ' + jobId,
        '5. El sistema de programación funciona (upload-post acepta)'
      ],
      twitterSearchUrl: 'https://twitter.com/search?q=from%3ADiariosLive%20tiempo%20martes&src=typed_query&f=live',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [DEBUG JOB] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en debug de Job ID',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
