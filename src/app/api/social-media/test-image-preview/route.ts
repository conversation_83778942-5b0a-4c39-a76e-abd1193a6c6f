import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-image-preview - Test de imagen y vista previa
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🖼️ [TEST IMAGE PREVIEW] Probando imagen y vista previa...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Obtener Page ID
    const facebookPageId = await uploadPostClient.getFacebookPageId('jpablovila');
    if (!facebookPageId) {
      return NextResponse.json({
        success: false,
        error: 'No se pudo obtener Facebook Page ID'
      }, { status: 400 });
    }

    const timestamp = new Date().toISOString();
    const results = [];

    // Test 1: Publicación con imagen (usando upload_photos)
    console.log('🖼️ Test 1: Publicación con imagen...');
    try {
      const imageTestContent = `🖼️ TEST CON IMAGEN: Publicación con foto desde el panel

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Endpoint: /api/upload_photos
🔗 Enlace: https://diarios.live
📱 Página: La Bang fm

#TestConImagen #DiarioDelSur`;

      const imageResult = await uploadPostClient.publishPost({
        text: imageTestContent,
        url: 'https://diarios.live',
        platforms: ['facebook'],
        profiles: ['jpablovila'],
        imageUrl: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=600&fit=crop' // Imagen de ejemplo
      });

      results.push({
        test: 'Con Imagen (upload_photos)',
        success: imageResult.success === true,
        endpoint: '/api/upload_photos',
        result: imageResult.data,
        content: imageTestContent,
        imageUrl: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=600&fit=crop'
      });

      console.log('✅ Test con imagen completado');
    } catch (error) {
      results.push({
        test: 'Con Imagen (upload_photos)',
        success: false,
        endpoint: '/api/upload_photos',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test con imagen:', error);
    }

    // Esperar un poco entre tests
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test 2: Publicación solo texto con vista previa (usando upload_text + facebook_link_url)
    console.log('🔗 Test 2: Publicación con vista previa de enlace...');
    try {
      const linkTestContent = `🔗 TEST CON VISTA PREVIA: Publicación con enlace preview

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Endpoint: /api/upload_text
🔗 Vista previa: facebook_link_url
📱 Página: La Bang fm

#TestVistaPrevia #DiarioDelSur`;

      const linkResult = await uploadPostClient.publishPost({
        text: linkTestContent,
        url: 'https://diarios.live',
        platforms: ['facebook'],
        profiles: ['jpablovila']
        // Sin imageUrl para que use vista previa
      });

      results.push({
        test: 'Vista Previa (upload_text)',
        success: linkResult.success === true,
        endpoint: '/api/upload_text',
        result: linkResult.data,
        content: linkTestContent,
        linkPreview: 'https://diarios.live'
      });

      console.log('✅ Test con vista previa completado');
    } catch (error) {
      results.push({
        test: 'Vista Previa (upload_text)',
        success: false,
        endpoint: '/api/upload_text',
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test con vista previa:', error);
    }

    // Analizar resultados
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);

    return NextResponse.json({
      success: successfulTests.length > 0,
      summary: {
        total: results.length,
        successful: successfulTests.length,
        failed: failedTests.length
      },
      results,
      recommendations: [
        successfulTests.length > 0 ? '✅ Al menos un método funciona' : '❌ Ningún método funciona',
        successfulTests.find(r => r.test.includes('Imagen')) ? '🖼️ Publicación con imagen funciona' : '⚠️ Publicación con imagen no funciona',
        successfulTests.find(r => r.test.includes('Vista Previa')) ? '🔗 Vista previa de enlace funciona' : '⚠️ Vista previa de enlace no funciona',
        'Ve a Facebook "La Bang fm" para verificar las publicaciones'
      ],
      instructions: [
        '📱 Ve a Facebook y busca "La Bang fm"',
        '👀 Deberías ver las publicaciones de prueba',
        '🖼️ Una con imagen (si funciona)',
        '🔗 Una con vista previa de enlace (si funciona)',
        '📊 Compara cuál se ve mejor'
      ],
      pageId: facebookPageId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST IMAGE PREVIEW] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de imagen y vista previa',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
