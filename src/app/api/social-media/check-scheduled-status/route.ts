import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/check-scheduled-status - Verificar estado de publicación programada
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { jobId } = await request.json();

    console.log('🔍 [CHECK SCHEDULED] Verificando estado de publicación programada...');

    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key de upload-post no configurada'
      }, { status: 500 });
    }

    const now = new Date();
    const argentinaTime = now.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' });

    // Verificar publicaciones programadas
    try {
      const response = await fetch('https://api.upload-post.com/api/uploadposts/schedule', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        return NextResponse.json({
          success: false,
          error: 'Error obteniendo publicaciones programadas',
          details: errorText
        }, { status: response.status });
      }

      const scheduledResponse = await response.json();
      console.log('📋 Respuesta de upload-post:', scheduledResponse);

      // Manejar diferentes formatos de respuesta
      let scheduledPosts = [];
      if (Array.isArray(scheduledResponse)) {
        scheduledPosts = scheduledResponse;
      } else if (scheduledResponse.data && Array.isArray(scheduledResponse.data)) {
        scheduledPosts = scheduledResponse.data;
      } else if (scheduledResponse.posts && Array.isArray(scheduledResponse.posts)) {
        scheduledPosts = scheduledResponse.posts;
      } else if (scheduledResponse.scheduled_posts && Array.isArray(scheduledResponse.scheduled_posts)) {
        scheduledPosts = scheduledResponse.scheduled_posts;
      } else {
        console.log('⚠️ Formato de respuesta inesperado:', typeof scheduledResponse);
        scheduledPosts = [];
      }

      console.log('📋 Publicaciones programadas procesadas:', {
        originalType: typeof scheduledResponse,
        isArray: Array.isArray(scheduledResponse),
        processedCount: scheduledPosts.length,
        posts: scheduledPosts
      });

      // Buscar nuestro job específico
      const ourJob = jobId && scheduledPosts.length > 0 ?
        scheduledPosts.find((post: any) => post.job_id === jobId) : null;

      // Analizar todas las publicaciones programadas
      const analysis = scheduledPosts.map((post: any) => {
        const scheduledUTC = new Date(post.scheduled_date);
        const scheduledArgentina = scheduledUTC.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' });
        const isPast = scheduledUTC <= now;
        const minutesUntil = Math.round((scheduledUTC.getTime() - now.getTime()) / (1000 * 60));

        return {
          job_id: post.job_id,
          scheduled_utc: post.scheduled_date,
          scheduled_argentina: scheduledArgentina,
          title: post.title?.substring(0, 50) + '...',
          platform: post.post_type,
          status: isPast ? 'Debería haberse publicado' : `Faltan ${minutesUntil} minutos`,
          isPast,
          minutesUntil,
          isOurJob: post.job_id === jobId
        };
      });

      return NextResponse.json({
        success: true,
        currentTime: {
          utc: now.toISOString(),
          argentina: argentinaTime
        },
        searchedJobId: jobId,
        ourJobFound: !!ourJob,
        ourJobDetails: ourJob ? {
          job_id: ourJob.job_id,
          scheduled_utc: ourJob.scheduled_date,
          scheduled_argentina: new Date(ourJob.scheduled_date).toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
          title: ourJob.title,
          status: new Date(ourJob.scheduled_date) <= now ? 'Debería haberse publicado' : 'Pendiente',
          isPast: new Date(ourJob.scheduled_date) <= now,
          minutesUntil: Math.round((new Date(ourJob.scheduled_date).getTime() - now.getTime()) / (1000 * 60))
        } : null,
        allScheduledPosts: {
          total: scheduledPosts.length,
          posts: analysis,
          rawResponse: scheduledResponse // Para debugging
        },
        recommendations: [
          scheduledPosts.length === 0 ? '📋 No hay publicaciones programadas en la cola' : `📋 ${scheduledPosts.length} publicaciones en cola`,
          jobId ? (ourJob ? '✅ Tu publicación está en la cola de upload-post' : '⚠️ Tu publicación no aparece en la cola') : '💡 Ingresa Job ID para búsqueda específica',
          ourJob && new Date(ourJob.scheduled_date) <= now ? '⏰ Ya pasó la hora programada - debería haberse publicado' : '⏰ Aún no es la hora programada',
          '👀 Verifica tu cuenta de Twitter para confirmar',
          '🔄 Upload-post maneja la ejecución automáticamente'
        ],
        instructions: [
          '1. Verifica la hora actual vs hora programada',
          '2. Revisa tu cuenta de Twitter (@DiariosLive)',
          '3. Si ya pasó la hora y no se publicó, puede haber un delay',
          '4. Upload-post puede tomar unos minutos extra',
          '5. Si persiste el problema, contacta soporte de upload-post'
        ],
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error verificando estado:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error conectando con upload-post',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [CHECK SCHEDULED] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
