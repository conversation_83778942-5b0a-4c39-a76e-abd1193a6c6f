import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostInvestigator } from '@/lib/social-media/upload-post-investigator';

// GET /api/social-media/investigate - Investigación completa de upload-post
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [INVESTIGATE] Iniciando investigación completa...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key no configurada',
        note: 'Configura UPLOAD_POST_API_KEY en las variables de entorno'
      }, { status: 400 });
    }

    const investigator = new UploadPostInvestigator(apiKey);
    const results = await investigator.investigateAPI();

    console.log('✅ [INVESTIGATE] Investigación completada');

    return NextResponse.json({
      success: true,
      message: 'Investigación de upload-post completada',
      timestamp: new Date().toISOString(),
      results,
      summary: {
        apiKeyValid: !results.apiKeyInfo.error,
        jsonResponsesFound: results.baseURLTests.filter((r: any) => r.isJson).length,
        workingAuthMethods: results.authMethodTests.filter((r: any) => r.isJson).length,
        totalTestsRun: results.baseURLTests.length + results.authMethodTests.length + results.headerTests.length
      }
    });

  } catch (error) {
    console.error('❌ [INVESTIGATE] Error en investigación:', error);
    return NextResponse.json({
      success: false,
      error: 'Error ejecutando investigación',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
