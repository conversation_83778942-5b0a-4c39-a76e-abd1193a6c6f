import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

const prisma = new PrismaClient();

// POST /api/social-media/schedule-native - Programar publicaciones usando programación nativa de upload-post
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      publicacionExternaId, 
      platforms, 
      accountIds, 
      customCaption,
      scheduledFor // ISO-8601 date string
    } = body;

    console.log('📅 [SCHEDULE-NATIVE] Programando publicación con upload-post nativo:', {
      publicacionExternaId,
      platforms,
      accountIds: Array.isArray(accountIds) ? accountIds : [accountIds],
      hasCustomCaption: !!customCaption,
      scheduledFor,
      user: session.user?.email
    });

    // Validaciones
    if (!publicacionExternaId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'publicacionExternaId es requerido' 
        },
        { status: 400 }
      );
    }

    if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'platforms debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    if (!scheduledFor) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'scheduledFor es requerido para programación' 
        },
        { status: 400 }
      );
    }

    // Validar que la fecha sea futura
    const scheduledDate = new Date(scheduledFor);
    const now = new Date();
    
    if (scheduledDate <= now) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'La fecha programada debe ser futura' 
        },
        { status: 400 }
      );
    }

    // Validar que no sea más de 365 días en el futuro (límite de upload-post)
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 365);
    
    if (scheduledDate > maxDate) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'La fecha programada no puede ser más de 365 días en el futuro' 
        },
        { status: 400 }
      );
    }

    // Obtener datos de la publicación externa
    const publicacionExterna = await prisma.publicacionExterna.findUnique({
      where: { id: parseInt(publicacionExternaId) },
      include: {
        noticia: {
          select: {
            titulo: true,
            contenido: true,
            imagenUrl: true
          }
        }
      }
    });

    if (!publicacionExterna) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Publicación externa no encontrada' 
        },
        { status: 404 }
      );
    }

    // Verificar API key
    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'API key de upload-post no configurada' 
        },
        { status: 500 }
      );
    }

    console.log('🚀 Programando con upload-post nativo...');
    
    const uploadPostClient = new UploadPostClient(uploadPostApiKey);
    
    // Generar caption
    const titulo = publicacionExterna.noticia?.titulo || 'Sin título';
    const url = publicacionExterna.urlPublicacion || '';
    const caption = customCaption || `📰 ${titulo}

¡Lee la noticia completa! 👆

${url}

#Noticias #Actualidad #DiarioDelSur`;

    try {
      // Usar programación nativa de upload-post
      const result = await uploadPostClient.publishPost({
        text: caption,
        url: publicacionExterna.urlPublicacion || undefined,
        platforms: platforms,
        profiles: ['jpablovila'],
        scheduledDate: scheduledFor // ISO-8601 date string
      });

      console.log('✅ Programación nativa exitosa:', result);

      // Crear registros en la base de datos con estado SCHEDULED
      const results = [];
      for (const platform of platforms) {
        // Para programación nativa, no tenemos post_id inmediatamente
        // Upload-post nos dará un job_id para gestionar la programación
        
        const publication = await prisma.socialMediaPublication.create({
          data: {
            publicacionExternaId: parseInt(publicacionExternaId),
            socialMediaAccountId: 1, // Mock account ID
            platform: platform,
            caption,
            status: 'SCHEDULED', // Estado programado
            scheduledFor: scheduledDate,
            externalPostId: result.data?.id || null, // Job ID de upload-post para gestión
            createdBy: session.user?.email || 'system'
          }
        });

        results.push({
          platform: platform,
          accountId: 1,
          accountName: `Cuenta ${platform}`,
          success: true,
          publicationId: publication.id,
          jobId: result.data?.id || 'unknown',
          scheduledFor: scheduledFor,
          message: `Programado exitosamente en ${platform} para ${scheduledDate.toLocaleString('es-ES')}`,
          nativeScheduling: true
        });
      }

      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;

      return NextResponse.json({
        success: successCount > 0,
        results,
        summary: {
          total: totalCount,
          successful: successCount,
          failed: totalCount - successCount
        },
        schedulingInfo: {
          method: 'upload-post native',
          scheduledFor: scheduledFor,
          jobId: result.data?.id || 'unknown',
          canCancel: true,
          cancelEndpoint: `/api/social-media/cancel-scheduled/${result.data?.id || 'unknown'}`
        },
        message: successCount === totalCount
          ? `¡Programado exitosamente en ${successCount} cuenta${successCount > 1 ? 's' : ''} para ${scheduledDate.toLocaleString('es-ES')}!`
          : successCount > 0
            ? `Programado en ${successCount}/${totalCount} cuentas. ${totalCount - successCount} fallaron.`
            : `Error: No se pudo programar en ninguna cuenta.`,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error en programación nativa:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error en programación nativa',
        details: error instanceof Error ? error.message : 'Error desconocido',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [SCHEDULE-NATIVE] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
