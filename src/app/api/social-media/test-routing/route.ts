import { NextRequest, NextResponse } from 'next/server';

// POST /api/social-media/test-routing - Test de routing
export async function POST(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'Routing funcionando correctamente',
    timestamp: new Date().toISOString(),
    path: '/api/social-media/test-routing'
  });
}

// GET /api/social-media/test-routing - Test de routing
export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'GET routing funcionando correctamente',
    timestamp: new Date().toISOString(),
    path: '/api/social-media/test-routing'
  });
}
