import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// GET /api/social-media/get-facebook-page-id - Obtener Facebook Page ID
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [GET PAGE ID] Obteniendo Facebook Page ID...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Obtener perfiles completos
    const profiles = await uploadPostClient.getProfiles();
    console.log('📱 Perfiles obtenidos:', profiles.length);

    // Obtener páginas de Facebook usando endpoint oficial
    const response = await fetch('https://api.upload-post.com/api/uploadposts/facebook/pages', {
      method: 'GET',
      headers: {
        'Authorization': `Apikey ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    const facebookPages = await response.json();
    console.log('📘 Páginas de Facebook:', JSON.stringify(facebookPages, null, 2));

    // Analizar páginas de Facebook
    const facebookAnalysis = [];

    if (Array.isArray(facebookPages)) {
      for (const page of facebookPages) {
        facebookAnalysis.push({
          pageId: page.id,
          pageName: page.name,
          picture: page.picture || null,
          accountId: page.account_id || null,
          vanityName: page.vanityName || null,
          allFields: Object.keys(page)
        });
      }
    }

    // Intentar obtener Page ID usando el método del cliente
    let pageIdFromClient = null;
    try {
      pageIdFromClient = await uploadPostClient.getFacebookPageId('jpablovila');
    } catch (error) {
      console.error('Error obteniendo Page ID del cliente:', error);
    }

    return NextResponse.json({
      success: true,
      analysis: {
        totalProfiles: profiles.length,
        facebookPagesFound: facebookAnalysis.length,
        pageIdFromClient,
        facebookPages: facebookAnalysis,
        recommendedPageId: facebookAnalysis.length > 0 ? facebookAnalysis[0].pageId : null
      },
      rawFacebookPages: facebookPages,
      recommendations: facebookAnalysis.length > 0 ? [
        `Se encontraron ${facebookAnalysis.length} páginas de Facebook`,
        `Page ID recomendado: ${facebookAnalysis[0].pageId}`,
        `Página: ${facebookAnalysis[0].pageName}`,
        'Usar este Page ID en las publicaciones'
      ] : [
        'No se encontraron páginas de Facebook',
        'Verificar que la cuenta esté conectada en upload-post.com',
        'Asegurar que la cuenta tenga páginas de Facebook configuradas'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [GET PAGE ID] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error obteniendo Facebook Page ID',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
