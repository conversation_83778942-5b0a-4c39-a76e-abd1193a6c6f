import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

const prisma = new PrismaClient();

// POST /api/social-media/publish - Publicar inmediatamente en redes sociales
// Actualizado: Corregido para usar API real en lugar de modo demo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      publicacionExternaId, 
      platforms, 
      accountIds, 
      customCaption 
    } = body;

    // Validaciones
    if (!publicacionExternaId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'publicacionExternaId es requerido' 
        },
        { status: 400 }
      );
    }

    if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'platforms debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'accountIds debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    // Validar plataformas
    const validPlatforms = ['facebook', 'twitter', 'instagram'];
    const invalidPlatforms = platforms.filter(p => !validPlatforms.includes(p));
    if (invalidPlatforms.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Plataformas inválidas: ${invalidPlatforms.join(', ')}. Válidas: ${validPlatforms.join(', ')}`
        },
        { status: 400 }
      );
    }

    console.log('🚀 Iniciando publicación inmediata en redes sociales:', {
      publicacionExternaId,
      platforms,
      accountIds: accountIds.length,
      hasCustomCaption: !!customCaption,
      user: session.user.email
    });

    // Obtener datos de la publicación externa
    const publicacionExterna = await prisma.publicacionExterna.findUnique({
      where: { id: parseInt(publicacionExternaId) },
      include: {
        noticia: {
          select: {
            titulo: true,
            contenido: true,
            imagenUrl: true
          }
        }
      }
    });

    if (!publicacionExterna) {
      return NextResponse.json(
        {
          success: false,
          error: 'Publicación externa no encontrada'
        },
        { status: 404 }
      );
    }

    // Verificar si tenemos API key de upload-post
    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    let useAdvancedDemo = false;

    if (!uploadPostApiKey) {
      console.log('⚠️ API key no configurada, usando modo demo avanzado');
      useAdvancedDemo = true;
    } else {
      // Probar conexión rápida con upload-post usando endpoint correcto
      try {
        console.log('🔍 Probando conexión rápida con upload-post...');
        const testResponse = await fetch('https://api.upload-post.com/api/uploadposts/facebook/pages', {
          method: 'GET',
          headers: {
            'Authorization': `Apikey ${uploadPostApiKey}`,
          },
        });

        console.log('📡 Respuesta de upload-post:', {
          status: testResponse.status,
          contentType: testResponse.headers.get('content-type'),
          ok: testResponse.ok
        });

        const contentType = testResponse.headers.get('content-type');
        const isJson = contentType?.includes('application/json');

        if (!testResponse.ok) {
          console.log('⚠️ Upload-post responde con error, usando modo demo avanzado');
          useAdvancedDemo = true;
        } else if (!isJson) {
          console.log('⚠️ Upload-post devuelve HTML, usando modo demo avanzado');
          useAdvancedDemo = true;
        } else {
          console.log('✅ Upload-post disponible, usando API real');
        }
      } catch (error) {
        console.log('⚠️ Error conectando con upload-post, usando modo demo avanzado:', error);
        useAdvancedDemo = true;
      }
    }

    // Modo demo avanzado o API real
    if (useAdvancedDemo) {
      console.log('🎭 Ejecutando modo demo avanzado...');
      return await executeAdvancedDemo({
        publicacionExterna,
        platforms,
        accountIds,
        customCaption,
        session,
        prisma
      });
    }

    // Obtener cuentas de redes sociales reales
    const accounts = await prisma.socialMediaAccount.findMany({
      where: {
        id: { in: accountIds.map(id => parseInt(id.toString())) },
        isActive: true,
        platform: { in: platforms }
      }
    });

    if (accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No se encontraron cuentas activas para las plataformas seleccionadas',
        note: 'Sincroniza tus perfiles de upload-post primero'
      }, { status: 404 });
    }

    // Inicializar cliente de upload-post
    const uploadPostClient = new UploadPostClient(uploadPostApiKey);

    // Preparar datos para publicación
    const titulo = publicacionExterna.noticia?.titulo || 'Sin título';
    const caption = customCaption || `📰 ${titulo}\n\n¡Lee la noticia completa! 👆\n\n#Noticias #Actualidad`;

    const results = [];

    // Publicar en cada cuenta
    for (const account of accounts) {
      try {
        console.log(`📤 Publicando en ${account.platform} (${account.accountName})...`);

        // Crear registro en BD antes de publicar
        const publication = await prisma.socialMediaPublication.create({
          data: {
            publicacionExternaId: parseInt(publicacionExternaId),
            socialMediaAccountId: account.id,
            platform: account.platform,
            caption,
            status: 'PENDING',
            createdBy: session.user.email || 'system'
          }
        });

        // Publicar en upload-post
        const uploadResponse = await uploadPostClient.publishPost({
          text: caption,
          url: publicacionExterna.urlPublicacion || undefined,
          platforms: [account.platform],
          profiles: [account.profileId],
          imageUrl: publicacionExterna.noticia.imagenUrl || undefined // Incluir imagen de la noticia
        });

        // Actualizar registro con éxito
        await prisma.socialMediaPublication.update({
          where: { id: publication.id },
          data: {
            status: 'SUCCESS',
            publishedAt: new Date(),
            externalPostId: uploadResponse.data?.id || 'unknown'
          }
        });

        results.push({
          platform: account.platform,
          accountId: account.id,
          accountName: account.accountName,
          success: true,
          publicationId: publication.id,
          externalPostId: uploadResponse.data?.id || 'unknown',
          message: `Publicado exitosamente en ${account.platform}`,
          publishedAt: new Date().toISOString()
        });

        console.log(`✅ Publicación exitosa en ${account.platform}`);

      } catch (error) {
        console.error(`❌ Error publicando en ${account.platform}:`, error);

        // Actualizar registro con error si existe
        try {
          const publication = await prisma.socialMediaPublication.findFirst({
            where: {
              publicacionExternaId: parseInt(publicacionExternaId),
              socialMediaAccountId: account.id,
              status: 'PENDING'
            },
            orderBy: { createdAt: 'desc' }
          });

          if (publication) {
            await prisma.socialMediaPublication.update({
              where: { id: publication.id },
              data: {
                status: 'ERROR',
                errorMessage: error instanceof Error ? error.message : 'Error desconocido'
              }
            });
          }
        } catch (updateError) {
          console.warn('⚠️ Error actualizando registro de error:', updateError);
        }

        results.push({
          platform: account.platform,
          accountId: account.id,
          accountName: account.accountName,
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido',
          message: `Error publicando en ${account.platform}`
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    console.log(`✅ Publicación completada: ${successCount} exitosas, ${errorCount} errores`);

    // Determinar el estado general
    const overallSuccess = successCount > 0;
    const message = successCount === results.length
      ? `¡Publicado exitosamente en ${successCount} cuenta${successCount > 1 ? 's' : ''}!`
      : successCount > 0
        ? `Publicado en ${successCount}/${results.length} cuentas. ${errorCount} fallaron.`
        : `Error: No se pudo publicar en ninguna cuenta.`;

    return NextResponse.json({
      success: overallSuccess,
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount,
        platforms: platforms,
        publishedAt: new Date().toISOString(),
        accounts: accounts.map(a => ({
          id: a.id,
          platform: a.platform,
          accountName: a.accountName
        }))
      },
      message,
      realPublication: true,
      note: successCount > 0
        ? 'Publicación real completada usando upload-post.com'
        : 'Revisa los errores y vuelve a intentar'
    });
  } catch (error) {
    console.error('❌ Error en publicación inmediata:', error);

    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message.includes('no encontrada')) {
        statusCode = 404;
      } else if (error.message.includes('API key')) {
        statusCode = 401;
      } else if (error.message.includes('conexión')) {
        statusCode = 503;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        timestamp: new Date().toISOString(),
        demo: true,
        note: 'Error en modo demostración. Verifica la configuración de la base de datos.'
      },
      { status: statusCode }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// Función para modo demo avanzado
async function executeAdvancedDemo(params: {
  publicacionExterna: any;
  platforms: string[];
  accountIds: any[];
  customCaption?: string;
  session: any;
  prisma: any;
}) {
  const { publicacionExterna, platforms, accountIds, customCaption, session, prisma } = params;

  console.log('🎭 Iniciando simulación avanzada de publicación...');

  // Simular tiempo de procesamiento real (1-3 segundos)
  const processingTime = Math.random() * 2000 + 1000;
  await new Promise(resolve => setTimeout(resolve, processingTime));

  // Crear cuentas demo si no existen
  const demoAccounts = await createDemoAccountsIfNeeded(platforms, prisma);

  // Preparar caption
  const titulo = publicacionExterna.noticia?.titulo || publicacionExterna.titulo || 'Sin título';
  const caption = customCaption || `📰 ${titulo}\n\n¡Lee la noticia completa! 👆\n\n#Noticias #Actualidad`;

  const results = [];

  // Simular publicación en cada plataforma
  for (const platform of platforms) {
    const account = demoAccounts.find(acc => acc.platform === platform);
    if (!account) continue;

    try {
      console.log(`📤 Simulando publicación en ${platform}...`);

      // Crear registro en BD (real)
      const publication = await prisma.socialMediaPublication.create({
        data: {
          publicacionExternaId: publicacionExterna.id,
          socialMediaAccountId: account.id,
          platform: platform,
          caption: caption,
          status: 'SUCCESS',
          publishedAt: new Date(),
          externalPostId: `demo_${platform}_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
          createdBy: session.user.email || 'demo'
        }
      });

      results.push({
        platform: platform,
        accountId: account.id,
        accountName: account.accountName,
        success: true,
        publicationId: publication.id,
        externalPostId: publication.externalPostId,
        message: `Publicado exitosamente en ${platform} (modo demo)`,
        publishedAt: new Date().toISOString(),
        demoMode: true
      });

      console.log(`✅ Simulación exitosa en ${platform}`);

    } catch (error) {
      console.error(`❌ Error en simulación de ${platform}:`, error);

      results.push({
        platform: platform,
        accountId: account?.id || 0,
        accountName: account?.accountName || `Demo ${platform}`,
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
        message: `Error simulando publicación en ${platform}`,
        demoMode: true
      });
    }
  }

  const successCount = results.filter(r => r.success).length;
  const errorCount = results.filter(r => !r.success).length;

  console.log(`✅ Simulación completada: ${successCount} exitosas, ${errorCount} errores`);

  const overallSuccess = successCount > 0;
  const message = successCount === results.length
    ? `¡Publicado exitosamente en ${successCount} cuenta${successCount > 1 ? 's' : ''}! (modo demo)`
    : successCount > 0
      ? `Publicado en ${successCount}/${results.length} cuentas (modo demo). ${errorCount} fallaron.`
      : `Error: No se pudo simular publicación en ninguna cuenta.`;

  return NextResponse.json({
    success: overallSuccess,
    results,
    summary: {
      total: results.length,
      successful: successCount,
      failed: errorCount,
      platforms: platforms,
      publishedAt: new Date().toISOString(),
      accounts: demoAccounts.map(a => ({
        id: a.id,
        platform: a.platform,
        accountName: a.accountName
      }))
    },
    message,
    demoMode: true,
    advancedDemo: true,
    note: 'Publicación simulada con registros reales en base de datos. Configura upload-post.com para publicación real.'
  });
}

// Función para crear cuentas demo si no existen
async function createDemoAccountsIfNeeded(platforms: string[], prisma: any) {
  const accounts = [];

  for (const platform of platforms) {
    // Buscar cuenta existente
    let account = await prisma.socialMediaAccount.findFirst({
      where: {
        platform: platform,
        profileId: { startsWith: 'demo_' }
      }
    });

    // Crear cuenta demo si no existe
    if (!account) {
      console.log(`📱 Creando cuenta demo para ${platform}...`);

      account = await prisma.socialMediaAccount.create({
        data: {
          platform: platform,
          accountName: `Demo ${platform.charAt(0).toUpperCase() + platform.slice(1)} Account`,
          profileId: `demo_${platform}_${Date.now()}`,
          isActive: true
        }
      });
    }

    accounts.push(account);
  }

  return accounts;
}
