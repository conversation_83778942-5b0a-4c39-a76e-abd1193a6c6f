import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { SocialMediaService } from '@/lib/social-media/social-media-service';
import { ScheduleParams, Platform } from '@/lib/social-media/types';

const prisma = new PrismaClient();

// POST /api/social-media/schedule - Programar publicación en redes sociales
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      publicacionExternaId, 
      platforms, 
      accountIds, 
      scheduledFor,
      customCaption 
    } = body;

    // Validaciones
    if (!publicacionExternaId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'publicacionExternaId es requerido' 
        },
        { status: 400 }
      );
    }

    if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'platforms debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    if (!accountIds || !Array.isArray(accountIds) || accountIds.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'accountIds debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    if (!scheduledFor) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'scheduledFor es requerido' 
        },
        { status: 400 }
      );
    }

    // Validar fecha de programación
    const scheduledDate = new Date(scheduledFor);
    if (isNaN(scheduledDate.getTime())) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'scheduledFor debe ser una fecha válida' 
        },
        { status: 400 }
      );
    }

    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'scheduledFor debe ser una fecha futura' 
        },
        { status: 400 }
      );
    }

    // Validar plataformas
    const validPlatforms: Platform[] = ['facebook', 'twitter', 'instagram'];
    const invalidPlatforms = platforms.filter(p => !validPlatforms.includes(p));
    if (invalidPlatforms.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Plataformas inválidas: ${invalidPlatforms.join(', ')}. Válidas: ${validPlatforms.join(', ')}` 
        },
        { status: 400 }
      );
    }

    console.log('⏰ Programando publicación en redes sociales:', {
      publicacionExternaId,
      platforms,
      accountIds: accountIds.length,
      scheduledFor: scheduledDate.toISOString(),
      hasCustomCaption: !!customCaption,
      user: session.user.email
    });

    const socialMediaService = new SocialMediaService();

    const scheduleParams: ScheduleParams = {
      publicacionExternaId: parseInt(publicacionExternaId),
      platforms: platforms as Platform[],
      accountIds: accountIds.map((id: any) => parseInt(id)),
      scheduledFor: scheduledDate,
      customCaption
    };

    const results = await socialMediaService.schedulePublication(scheduleParams);

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    console.log(`✅ Programación completada: ${successCount} exitosas, ${errorCount} errores`);

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount,
        platforms: platforms,
        scheduledFor: scheduledDate.toISOString(),
        scheduledAt: new Date().toISOString()
      },
      message: `Programación completada: ${successCount}/${results.length} exitosas para ${scheduledDate.toLocaleString()}`
    });
  } catch (error) {
    console.error('❌ Error en programación:', error);
    
    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes('no encontrada')) {
        statusCode = 404;
      } else if (error.message.includes('API key')) {
        statusCode = 401;
      } else if (error.message.includes('conexión')) {
        statusCode = 503;
      }
    }

    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: statusCode }
    );
  }
}

// GET /api/social-media/schedule - Obtener publicaciones programadas
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') as Platform | null;
    const accountId = searchParams.get('accountId');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    console.log('📋 Obteniendo publicaciones programadas:', {
      platform,
      accountId,
      dateFrom,
      dateTo
    });

    const socialMediaService = new SocialMediaService();

    const filters: any = {};
    if (platform) filters.platform = platform;
    if (accountId) filters.accountId = parseInt(accountId);
    if (dateFrom) filters.dateFrom = new Date(dateFrom);
    if (dateTo) filters.dateTo = new Date(dateTo);

    const publications = await socialMediaService.getScheduledPublications(filters);

    console.log(`✅ ${publications.length} publicaciones programadas encontradas`);

    return NextResponse.json({
      success: true,
      publications,
      count: publications.length,
      filters
    });
  } catch (error) {
    console.error('❌ Error obteniendo publicaciones programadas:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/social-media/schedule - Cancelar publicación programada
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const publicationId = searchParams.get('id');

    if (!publicationId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de publicación requerido' 
        },
        { status: 400 }
      );
    }

    console.log('🚫 Cancelando publicación programada:', publicationId);

    const socialMediaService = new SocialMediaService();
    await socialMediaService.cancelScheduledPublication(parseInt(publicationId));

    console.log('✅ Publicación cancelada exitosamente');

    return NextResponse.json({
      success: true,
      message: 'Publicación cancelada exitosamente'
    });
  } catch (error) {
    console.error('❌ Error cancelando publicación:', error);
    
    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes('no encontrada')) {
        statusCode = 404;
      } else if (error.message.includes('Solo se pueden cancelar')) {
        statusCode = 400;
      }
    }

    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}
