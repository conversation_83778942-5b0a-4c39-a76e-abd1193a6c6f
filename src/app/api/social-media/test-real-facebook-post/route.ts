import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-real-facebook-post - Publicación REAL en Facebook
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🚀 [REAL FACEBOOK POST] Iniciando publicación REAL en Facebook...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Paso 1: Obtener Page ID real
    console.log('🔍 Paso 1: Obteniendo Facebook Page ID...');
    const pageId = await uploadPostClient.getFacebookPageId('jpablovila');
    
    if (!pageId) {
      return NextResponse.json({
        success: false,
        error: 'No se pudo obtener Facebook Page ID',
        step: 'get_page_id',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    console.log(`✅ Page ID obtenido: ${pageId}`);

    // Paso 2: Crear contenido único para la publicación
    const timestamp = new Date().toISOString();
    const testContent = `🚀 TEST REAL: Publicación automática desde el panel de Diarios del Sur

📅 Fecha: ${new Date().toLocaleString('es-ES', { 
      timeZone: 'America/Argentina/Buenos_Aires',
      year: 'numeric',
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })}

✅ Sistema: Panel de administración funcionando
🔗 Sitio: https://diarios.live
📱 Página: La Bang fm

#DiarioDelSur #TestAutomatico #SistemaFuncionando`;

    console.log('📝 Contenido a publicar:', testContent);

    // Paso 3: Hacer publicación REAL usando endpoint oficial
    console.log('🚀 Paso 3: Publicando en Facebook REAL...');
    
    const formData = new URLSearchParams({
      user: "jpablovila",
      'platform[]': "facebook",
      title: testContent,
      facebook_page_id: pageId  // Usar parámetro oficial
    });

    console.log('📤 Datos enviados:', Object.fromEntries(formData));

    const response = await fetch('https://api.upload-post.com/api/upload_text', {
      method: 'POST',
      headers: {
        'Authorization': `Apikey ${apiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString()
    });

    const result = await response.json();
    console.log('📥 Respuesta de upload-post:', JSON.stringify(result, null, 2));

    // Paso 4: Analizar resultado
    const facebookResult = result.results?.facebook;
    const isSuccess = facebookResult?.success === true;
    const postId = facebookResult?.post_id;
    const postUrl = facebookResult?.url;

    if (isSuccess) {
      console.log('🎉 ¡PUBLICACIÓN EXITOSA EN FACEBOOK!');
      console.log(`📱 Post ID: ${postId}`);
      console.log(`🔗 URL: ${postUrl}`);
      
      return NextResponse.json({
        success: true,
        message: '¡Publicación exitosa en Facebook!',
        facebook: {
          success: true,
          post_id: postId,
          url: postUrl,
          page_id: pageId,
          page_name: "La Bang fm"
        },
        content: testContent,
        steps: {
          step1_page_id: '✅ Page ID obtenido',
          step2_content: '✅ Contenido creado',
          step3_publish: '✅ Publicación exitosa',
          step4_verification: '✅ Resultado confirmado'
        },
        instructions: [
          '🎉 ¡La publicación fue exitosa!',
          '📱 Ve a Facebook y busca la página "La Bang fm"',
          '👀 Deberías ver el post publicado hace unos segundos',
          '🔗 También puedes usar la URL proporcionada si está disponible'
        ],
        timestamp: new Date().toISOString()
      });

    } else {
      console.log('❌ Error en la publicación:', facebookResult?.error);
      
      return NextResponse.json({
        success: false,
        error: 'Error en la publicación de Facebook',
        facebook: facebookResult,
        details: {
          page_id_used: pageId,
          content_sent: testContent,
          api_response: result
        },
        troubleshooting: [
          'Verificar que la página "La Bang fm" esté correctamente conectada',
          'Confirmar permisos de publicación en Facebook',
          'Revisar configuración de la cuenta en upload-post.com'
        ],
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ [REAL FACEBOOK POST] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en publicación real de Facebook',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
