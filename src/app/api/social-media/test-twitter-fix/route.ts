import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-twitter-fix - Test de corrección de Twitter
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🐦 [TEST TWITTER FIX] Probando corrección de Twitter (x)...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    const timestamp = new Date().toISOString();
    const results = [];

    // Test 1: Solo Twitter (mapeado a 'x')
    console.log('🐦 Test 1: Solo Twitter (mapeado a x)...');
    try {
      const twitterContent = `🐦 TEST TWITTER CORREGIDO: Usando 'x' en lugar de 'twitter'

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataforma: twitter → x (mapeado automáticamente)
🔗 Enlace: https://diarios.live
📱 Detección automática de enlaces en X

#TestTwitterCorregido #DiarioDelSur #XTwitter`;

      const twitterResult = await uploadPostClient.publishPost({
        text: twitterContent,
        url: 'https://diarios.live',
        platforms: ['twitter'], // Enviamos 'twitter', se mapea a 'x' internamente
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Solo Twitter (twitter → x)',
        originalPlatform: 'twitter',
        mappedPlatform: 'x',
        success: twitterResult.success === true,
        result: twitterResult.data,
        content: twitterContent,
        fullResponse: twitterResult
      });

      console.log('✅ Test Twitter (x) completado');
    } catch (error) {
      results.push({
        test: 'Solo Twitter (twitter → x)',
        originalPlatform: 'twitter',
        mappedPlatform: 'x',
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test Twitter (x):', error);
    }

    // Esperar entre tests
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 2: Facebook y Twitter juntos (con mapeo)
    console.log('🌐 Test 2: Facebook + Twitter (con mapeo a x)...');
    try {
      const multiContent = `🌐 TEST MULTI CORREGIDO: Facebook + Twitter (x)

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataformas: Facebook + Twitter (mapeado a x)
📘 Facebook: Open Graph automático
🐦 X: Detección automática de enlaces
🔗 Enlace: https://diarios.live

#TestMultiCorregido #DiarioDelSur #FacebookX`;

      const multiResult = await uploadPostClient.publishPost({
        text: multiContent,
        url: 'https://diarios.live',
        platforms: ['facebook', 'twitter'], // 'twitter' se mapea a 'x'
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Facebook + Twitter (multi)',
        originalPlatforms: ['facebook', 'twitter'],
        mappedPlatforms: ['facebook', 'x'],
        success: multiResult.success === true,
        facebookResult: multiResult.data,
        xResult: multiResult.data,
        content: multiContent,
        fullResponse: multiResult
      });

      console.log('✅ Test multi-plataforma (con mapeo) completado');
    } catch (error) {
      results.push({
        test: 'Facebook + Twitter (multi)',
        originalPlatforms: ['facebook', 'twitter'],
        mappedPlatforms: ['facebook', 'x'],
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test multi-plataforma:', error);
    }

    // Analizar resultados
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);

    return NextResponse.json({
      success: successfulTests.length > 0,
      summary: {
        total: results.length,
        successful: successfulTests.length,
        failed: failedTests.length,
        twitterFixed: results.some(r => r.mappedPlatform === 'x' && r.success),
        multiPlatformFixed: results.some(r => r.mappedPlatforms?.includes('x') && r.success)
      },
      results,
      platformMapping: {
        explanation: 'Twitter se mapea automáticamente a "x" según documentación de upload-post',
        mapping: {
          'twitter': 'x',
          'facebook': 'facebook'
        },
        supportedPlatforms: ['linkedin', 'x', 'facebook', 'threads', 'reddit']
      },
      recommendations: [
        successfulTests.length > 0 ? '✅ Corrección de Twitter funciona' : '❌ Corrección de Twitter no funciona',
        results.some(r => r.mappedPlatform === 'x' && r.success) ? '🐦 Twitter (x) funcionando' : '❌ Twitter (x) no funciona',
        results.some(r => r.mappedPlatforms?.includes('x') && r.success) ? '🌐 Multi-plataforma con Twitter funcionando' : '❌ Multi-plataforma con Twitter no funciona',
        'El mapeo automático twitter → x está implementado'
      ],
      instructions: [
        '📱 Ve a tus redes sociales para verificar las publicaciones:',
        '🐦 X (Twitter): Revisa tu timeline',
        '📘 Facebook: Busca "La Bang fm" (si incluido)',
        '👀 Confirma que los enlaces funcionan correctamente',
        '✅ El sistema ahora mapea twitter → x automáticamente'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST TWITTER FIX] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de corrección de Twitter',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
