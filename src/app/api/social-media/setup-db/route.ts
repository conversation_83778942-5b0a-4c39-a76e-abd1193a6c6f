import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/social-media/setup-db - Configurar tablas de redes sociales
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 Configurando tablas de redes sociales...');

    const results = [];

    // Paso 1: Verificar conexión con Prisma
    try {
      await prisma.$connect();
      results.push({
        step: 'Prisma Connection',
        status: 'success',
        message: 'Conexión exitosa con Prisma'
      });
    } catch (error) {
      results.push({
        step: 'Prisma Connection',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error de conexión'
      });
      throw error;
    }

    // Paso 2: Crear tabla social_media_accounts si no existe
    try {
      // Intentar crear un registro de prueba para verificar si la tabla existe
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS social_media_accounts (
          id SERIAL PRIMARY KEY,
          platform VARCHAR(50) NOT NULL,
          account_name VARCHAR(255) NOT NULL,
          profile_id VARCHAR(255) NOT NULL,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      results.push({
        step: 'SocialMediaAccount Table',
        status: 'success',
        message: 'Tabla social_media_accounts creada/verificada'
      });
    } catch (error) {
      results.push({
        step: 'SocialMediaAccount Table',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error creando tabla'
      });
    }

    // Paso 3: Crear tabla social_media_publications si no existe
    try {
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS social_media_publications (
          id SERIAL PRIMARY KEY,
          publicacion_externa_id INTEGER NOT NULL,
          social_media_account_id INTEGER NOT NULL,
          platform VARCHAR(50) NOT NULL,
          caption TEXT NOT NULL,
          status VARCHAR(50) NOT NULL,
          scheduled_for TIMESTAMP,
          published_at TIMESTAMP,
          external_post_id VARCHAR(255),
          error_message TEXT,
          retry_count INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_by VARCHAR(255) NOT NULL,
          FOREIGN KEY (social_media_account_id) REFERENCES social_media_accounts(id)
        );
      `;
      
      results.push({
        step: 'SocialMediaPublication Table',
        status: 'success',
        message: 'Tabla social_media_publications creada/verificada'
      });
    } catch (error) {
      results.push({
        step: 'SocialMediaPublication Table',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error creando tabla'
      });
    }

    // Paso 4: Verificar que las tablas funcionan
    try {
      const accountsCount = await prisma.socialMediaAccount.count();
      const publicationsCount = await prisma.socialMediaPublication.count();
      
      results.push({
        step: 'Tables Verification',
        status: 'success',
        message: `Tablas funcionando: ${accountsCount} cuentas, ${publicationsCount} publicaciones`
      });
    } catch (error) {
      results.push({
        step: 'Tables Verification',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error verificando tablas'
      });
    }

    // Paso 5: Crear índices para optimización
    try {
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_social_media_accounts_platform ON social_media_accounts(platform);
      `;
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_social_media_accounts_profile_id ON social_media_accounts(profile_id);
      `;
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_social_media_publications_account_id ON social_media_publications(social_media_account_id);
      `;
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS idx_social_media_publications_status ON social_media_publications(status);
      `;
      
      results.push({
        step: 'Database Indexes',
        status: 'success',
        message: 'Índices de optimización creados'
      });
    } catch (error) {
      results.push({
        step: 'Database Indexes',
        status: 'warning',
        message: 'Algunos índices ya existían o no se pudieron crear'
      });
    }

    const hasErrors = results.some(r => r.status === 'error');
    const allSuccess = results.every(r => r.status === 'success');

    console.log('✅ Configuración de base de datos completada');

    return NextResponse.json({
      success: !hasErrors,
      allSuccess,
      results,
      summary: {
        total: results.length,
        success: results.filter(r => r.status === 'success').length,
        errors: results.filter(r => r.status === 'error').length,
        warnings: results.filter(r => r.status === 'warning').length
      },
      message: hasErrors 
        ? 'Configuración completada con algunos errores'
        : 'Base de datos configurada exitosamente',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error configurando base de datos:', error);
    return NextResponse.json({
      success: false,
      error: 'Error configurando base de datos',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
