import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-multi-platform - Test de Facebook y Twitter
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🌐 [TEST MULTI-PLATFORM] Probando Facebook y Twitter...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Obtener Page ID para Facebook
    const facebookPageId = await uploadPostClient.getFacebookPageId('jpablovila');
    if (!facebookPageId) {
      return NextResponse.json({
        success: false,
        error: 'No se pudo obtener Facebook Page ID'
      }, { status: 400 });
    }

    const timestamp = new Date().toISOString();
    const results = [];

    // Test 1: Solo Facebook
    console.log('📘 Test 1: Solo Facebook...');
    try {
      const facebookContent = `📘 TEST FACEBOOK: Publicación solo en Facebook

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataforma: Facebook únicamente
🏷️ Open Graph: Detección automática
🔗 Enlace: https://diarios.live
📱 Página: La Bang fm

#TestFacebook #DiarioDelSur`;

      const facebookResult = await uploadPostClient.publishPost({
        text: facebookContent,
        url: 'https://diarios.live',
        platforms: ['facebook'],
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Solo Facebook',
        platforms: ['facebook'],
        success: facebookResult.success === true,
        result: facebookResult.data,
        content: facebookContent
      });

      console.log('✅ Test Facebook completado');
    } catch (error) {
      results.push({
        test: 'Solo Facebook',
        platforms: ['facebook'],
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test Facebook:', error);
    }

    // Esperar entre tests
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 2: Solo Twitter
    console.log('🐦 Test 2: Solo Twitter...');
    try {
      const twitterContent = `🐦 TEST TWITTER: Publicación solo en Twitter

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataforma: Twitter únicamente
🔗 Enlace: https://diarios.live
📱 Detección automática de enlaces

#TestTwitter #DiarioDelSur`;

      const twitterResult = await uploadPostClient.publishPost({
        text: twitterContent,
        url: 'https://diarios.live',
        platforms: ['twitter'],
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Solo Twitter',
        platforms: ['twitter'],
        success: twitterResult.success === true,
        result: twitterResult.data,
        content: twitterContent
      });

      console.log('✅ Test Twitter completado');
    } catch (error) {
      results.push({
        test: 'Solo Twitter',
        platforms: ['twitter'],
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test Twitter:', error);
    }

    // Esperar entre tests
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 3: Facebook y Twitter juntos
    console.log('🌐 Test 3: Facebook y Twitter juntos...');
    try {
      const multiContent = `🌐 TEST MULTI-PLATAFORMA: Facebook + Twitter

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataformas: Facebook y Twitter simultáneamente
📘 Facebook: Open Graph automático
🐦 Twitter: Detección automática de enlaces
🔗 Enlace: https://diarios.live

#TestMultiPlataforma #DiarioDelSur #FacebookTwitter`;

      const multiResult = await uploadPostClient.publishPost({
        text: multiContent,
        url: 'https://diarios.live',
        platforms: ['facebook', 'twitter'],
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Facebook + Twitter',
        platforms: ['facebook', 'twitter'],
        success: multiResult.success === true,
        facebookResult: multiResult.data,
        twitterResult: multiResult.data,
        content: multiContent,
        fullResult: multiResult.data
      });

      console.log('✅ Test multi-plataforma completado');
    } catch (error) {
      results.push({
        test: 'Facebook + Twitter',
        platforms: ['facebook', 'twitter'],
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test multi-plataforma:', error);
    }

    // Analizar resultados
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);

    return NextResponse.json({
      success: successfulTests.length > 0,
      summary: {
        total: results.length,
        successful: successfulTests.length,
        failed: failedTests.length,
        facebookWorking: results.some(r => r.platforms?.includes('facebook') && r.success),
        twitterWorking: results.some(r => r.platforms?.includes('twitter') && r.success),
        multiPlatformWorking: results.some(r => r.platforms?.length > 1 && r.success)
      },
      results,
      platformStatus: {
        facebook: {
          available: true,
          pageId: facebookPageId,
          method: 'facebook_link_url + Open Graph',
          working: results.some(r => r.platforms?.includes('facebook') && r.success)
        },
        twitter: {
          available: true,
          method: 'Detección automática de enlaces',
          working: results.some(r => r.platforms?.includes('twitter') && r.success)
        }
      },
      recommendations: [
        successfulTests.length > 0 ? '✅ Al menos una plataforma funciona' : '❌ Ninguna plataforma funciona',
        results.some(r => r.platforms?.includes('facebook') && r.success) ? '📘 Facebook funcionando' : '❌ Facebook no funciona',
        results.some(r => r.platforms?.includes('twitter') && r.success) ? '🐦 Twitter funcionando' : '❌ Twitter no funciona',
        results.some(r => r.platforms?.length > 1 && r.success) ? '🌐 Multi-plataforma funcionando' : '⚠️ Multi-plataforma no funciona'
      ],
      instructions: [
        '📱 Ve a tus redes sociales para verificar las publicaciones:',
        '📘 Facebook: Busca "La Bang fm"',
        '🐦 Twitter: Revisa tu timeline',
        '👀 Compara cómo se ven en cada plataforma',
        '✅ Confirma que los enlaces funcionan correctamente'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST MULTI-PLATFORM] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test multi-plataforma',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
