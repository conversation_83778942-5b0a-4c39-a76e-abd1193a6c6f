import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/test-argentina-dates - Test de fechas en Argentina
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🇦🇷 [ARGENTINA DATES] Probando fechas en Argentina...');

    const serverNow = new Date();
    const results = [];

    // Test 1: Fecha actual del servidor vs Argentina
    const argentinaDate = new Date().toLocaleDateString('en-CA', { 
      timeZone: 'America/Argentina/Buenos_Aires' 
    });
    
    const argentinaTime = new Date().toLocaleTimeString('en-GB', { 
      timeZone: 'America/Argentina/Buenos_Aires',
      hour12: false
    });

    const serverDate = serverNow.toISOString().split('T')[0];
    const serverTime = serverNow.toTimeString().slice(0, 8);

    results.push({
      test: 'Comparación Servidor vs Argentina',
      server: {
        date: serverDate,
        time: serverTime,
        timezone: process.env.TZ || 'No configurada',
        iso: serverNow.toISOString()
      },
      argentina: {
        date: argentinaDate,
        time: argentinaTime,
        timezone: 'America/Argentina/Buenos_Aires',
        fullDateTime: new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })
      },
      comparison: {
        sameDateAsServer: argentinaDate === serverDate,
        dateDifference: argentinaDate !== serverDate ? 'Argentina está en día diferente' : 'Mismo día',
        canScheduleToday: true // Siempre permitir programar para "hoy" en Argentina
      }
    });

    // Test 2: Conversión de fecha argentina a UTC
    const testCases = [
      { time: '09:00', description: 'Mañana' },
      { time: '12:00', description: 'Mediodía' },
      { time: '18:00', description: 'Tarde' },
      { time: '22:00', description: 'Noche' },
      { time: '23:59', description: 'Casi medianoche' }
    ];

    const conversionTests = testCases.map(testCase => {
      // Simular lo que hace el frontend
      const inputAsUTC = new Date(`${argentinaDate}T${testCase.time}:00.000Z`);
      const argentinaOffsetMs = -3 * 60 * 60 * 1000; // UTC-3
      const utcDateTime = new Date(inputAsUTC.getTime() - argentinaOffsetMs);

      return {
        description: testCase.description,
        userInput: `${argentinaDate} ${testCase.time} (Argentina)`,
        conversion: {
          inputAsUTC: inputAsUTC.toISOString(),
          utcResult: utcDateTime.toISOString(),
          backToArgentina: utcDateTime.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })
        },
        verification: {
          expectedArgentinaTime: `${testCase.time}`,
          actualArgentinaTime: utcDateTime.toLocaleString('en-GB', { 
            timeZone: 'America/Argentina/Buenos_Aires',
            hour12: false 
          }).slice(0, 5),
          matches: utcDateTime.toLocaleString('en-GB', { 
            timeZone: 'America/Argentina/Buenos_Aires',
            hour12: false 
          }).slice(0, 5) === testCase.time
        }
      };
    });

    results.push({
      test: 'Conversiones Argentina → UTC',
      conversions: conversionTests
    });

    // Test 3: Casos edge (cambio de día)
    const edgeCases = [
      { date: argentinaDate, time: '22:00' }, // Puede cambiar a día siguiente en UTC
      { date: argentinaDate, time: '23:30' }, // Definitivamente cambia a día siguiente en UTC
    ];

    const edgeTests = edgeCases.map(edge => {
      const inputAsUTC = new Date(`${edge.date}T${edge.time}:00.000Z`);
      const argentinaOffsetMs = -3 * 60 * 60 * 1000;
      const utcDateTime = new Date(inputAsUTC.getTime() - argentinaOffsetMs);

      const utcDate = utcDateTime.toISOString().split('T')[0];
      const utcTime = utcDateTime.toTimeString().slice(0, 5);

      return {
        input: `${edge.date} ${edge.time} Argentina`,
        utcResult: `${utcDate} ${utcTime} UTC`,
        dayChanged: utcDate !== edge.date,
        explanation: utcDate !== edge.date ? 
          `${edge.time} Argentina = ${utcTime} UTC del día siguiente` :
          `${edge.time} Argentina = ${utcTime} UTC del mismo día`
      };
    });

    results.push({
      test: 'Casos Edge (Cambio de Día)',
      edgeCases: edgeTests
    });

    // Test 4: Validación de fecha mínima
    const minDateTest = {
      serverMinDate: new Date().toISOString().split('T')[0],
      argentinaMinDate: new Date().toLocaleDateString('en-CA', { 
        timeZone: 'America/Argentina/Buenos_Aires' 
      }),
      problem: 'Si servidor está en UTC y Argentina en UTC-3, pueden ser días diferentes',
      solution: 'Usar fecha mínima de Argentina, no del servidor'
    };

    results.push({
      test: 'Validación Fecha Mínima',
      minDateValidation: minDateTest
    });

    // Análisis final
    const allConversionsCorrect = conversionTests.every(test => test.verification.matches);
    const systemReady = allConversionsCorrect;

    return NextResponse.json({
      success: true,
      currentTimes: {
        server: {
          iso: serverNow.toISOString(),
          local: serverNow.toLocaleString(),
          timezone: process.env.TZ || 'Sistema'
        },
        argentina: {
          date: argentinaDate,
          time: argentinaTime,
          full: new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' }),
          timezone: 'America/Argentina/Buenos_Aires'
        }
      },
      tests: results,
      summary: {
        allConversionsCorrect,
        systemReady,
        canScheduleToday: true,
        usesArgentinaTime: true
      },
      recommendations: [
        systemReady ? '✅ Conversiones de fecha funcionando correctamente' : '❌ Conversiones de fecha necesitan corrección',
        '🇦🇷 Sistema usa fecha y hora de Argentina',
        '📅 Fecha mínima basada en Argentina, no en servidor',
        '🌍 Conversión automática Argentina → UTC para upload-post',
        '⏰ Usuario puede programar para "hoy" en Argentina'
      ],
      instructions: [
        '📱 Ve a /redes-sociales para programar',
        '📅 Selecciona fecha argentina (incluye hoy)',
        '⏰ Ingresa hora argentina',
        '🔄 Sistema convierte automáticamente a UTC',
        '✅ Upload-post publica en hora argentina correcta'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [ARGENTINA DATES] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de fechas de Argentina',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
