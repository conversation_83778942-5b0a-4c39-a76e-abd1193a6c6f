import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/simple-status-check - Verificación simple de estado
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { jobId, scheduledTime } = await request.json();

    console.log('🔍 [SIMPLE STATUS] Verificación simple de estado...');

    const now = new Date();
    const argentinaTime = now.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' });
    const utcTime = now.toISOString();

    // Información básica de tiempo
    const timeInfo = {
      current: {
        utc: utcTime,
        argentina: argentinaTime,
        timestamp: now.getTime()
      }
    };

    // Si se proporciona información de programación, analizarla
    let schedulingAnalysis = null;
    if (scheduledTime) {
      const scheduledDate = new Date(scheduledTime);
      const scheduledArgentina = scheduledDate.toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' });
      const isPast = scheduledDate <= now;
      const diffMinutes = Math.round((scheduledDate.getTime() - now.getTime()) / (1000 * 60));

      schedulingAnalysis = {
        scheduled: {
          utc: scheduledTime,
          argentina: scheduledArgentina,
          timestamp: scheduledDate.getTime()
        },
        status: {
          isPast,
          diffMinutes,
          shouldHavePublished: isPast,
          timeUntil: isPast ? `Pasó hace ${Math.abs(diffMinutes)} minutos` : `Faltan ${diffMinutes} minutos`
        }
      };
    }

    // Intentar verificar upload-post (con manejo de errores)
    let uploadPostStatus = null;
    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    
    if (uploadPostApiKey) {
      try {
        const response = await fetch('https://api.upload-post.com/api/uploadposts/schedule', {
          method: 'GET',
          headers: {
            'Authorization': `Apikey ${uploadPostApiKey}`,
          }
          // Timeout no es soportado en fetch nativo
        });

        if (response.ok) {
          const data = await response.text(); // Obtener como texto primero
          console.log('📡 Respuesta raw de upload-post:', data);
          
          try {
            const jsonData = JSON.parse(data);
            uploadPostStatus = {
              success: true,
              rawResponse: data,
              parsedData: jsonData,
              type: Array.isArray(jsonData) ? 'array' : typeof jsonData,
              count: Array.isArray(jsonData) ? jsonData.length : 'No es array'
            };
          } catch (parseError) {
            uploadPostStatus = {
              success: false,
              error: 'No se pudo parsear JSON',
              rawResponse: data,
              parseError: parseError instanceof Error ? parseError.message : 'Error desconocido'
            };
          }
        } else {
          uploadPostStatus = {
            success: false,
            error: `HTTP ${response.status}`,
            statusText: response.statusText
          };
        }
      } catch (fetchError) {
        uploadPostStatus = {
          success: false,
          error: 'Error de conexión',
          details: fetchError instanceof Error ? fetchError.message : 'Error desconocido'
        };
      }
    } else {
      uploadPostStatus = {
        success: false,
        error: 'API key no configurada'
      };
    }

    // Recomendaciones basadas en el análisis
    const recommendations = [];
    
    if (schedulingAnalysis) {
      if (schedulingAnalysis.status.isPast) {
        recommendations.push('⏰ Ya pasó la hora programada');
        recommendations.push('👀 Verifica tu cuenta de Twitter (@DiariosLive)');
        recommendations.push('🔄 Upload-post puede tener un delay de 1-5 minutos');
      } else {
        recommendations.push(`⏰ Faltan ${schedulingAnalysis.status.diffMinutes} minutos`);
        recommendations.push('⏳ La publicación está programada correctamente');
      }
    }

    if (uploadPostStatus?.success) {
      recommendations.push('✅ Conexión con upload-post exitosa');
    } else {
      recommendations.push('⚠️ No se pudo verificar cola de upload-post');
      recommendations.push('💡 Esto no afecta la publicación programada');
    }

    recommendations.push('🎯 Job ID: ' + (jobId || 'No proporcionado'));

    return NextResponse.json({
      success: true,
      timeInfo,
      schedulingAnalysis,
      uploadPostStatus,
      jobId: jobId || null,
      recommendations,
      instructions: [
        '1. Verifica la hora actual vs programada',
        '2. Si ya pasó la hora, revisa Twitter',
        '3. Upload-post puede tener delay de 1-5 minutos',
        '4. Si no aparece después de 10 minutos, puede haber un problema',
        '5. El Job ID te permite rastrear la publicación'
      ],
      quickChecks: {
        timeHasPassed: schedulingAnalysis?.status.isPast || false,
        shouldCheckTwitter: schedulingAnalysis?.status.isPast || false,
        systemWorking: uploadPostStatus?.success || 'No verificado'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [SIMPLE STATUS] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error en verificación simple',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
