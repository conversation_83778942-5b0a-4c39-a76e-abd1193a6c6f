import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET /api/social-media/upload-post-profiles - Obtener perfiles disponibles de upload-post
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📋 [UPLOAD-POST PROFILES] Obteniendo perfiles disponibles...');

    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    if (!uploadPostApiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key de upload-post no configurada'
      }, { status: 500 });
    }

    try {
      // Obtener perfiles de upload-post
      const response = await fetch('https://api.upload-post.com/api/uploadposts/users', {
        method: 'GET',
        headers: {
          'Authorization': `Apikey ${uploadPostApiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        return NextResponse.json({
          success: false,
          error: 'Error obteniendo perfiles de upload-post',
          details: errorText
        }, { status: response.status });
      }

      const data = await response.json();
      console.log('📋 Datos obtenidos de upload-post:', JSON.stringify(data, null, 2));

      // La respuesta viene con profiles en lugar de users
      const profiles = data.profiles || [];
      console.log('👥 Perfiles encontrados:', profiles.length);
      console.log('👥 Estructura de perfiles:', profiles.map((p: any) => ({ username: p.username, social_accounts: Object.keys(p.social_accounts || {}) })));

      // Procesar perfiles para extraer información útil
      const processedProfiles = profiles.map((profile: any) => {
        // Extraer plataformas conectadas basado en la estructura de upload-post
        const connectedPlatforms = [];
        const socialAccounts = profile.social_accounts || {};

        if (socialAccounts.facebook && !socialAccounts.facebook.reauth_required) {
          connectedPlatforms.push('facebook');
        }
        if (socialAccounts.x && !socialAccounts.x.reauth_required) {
          connectedPlatforms.push('twitter');
        }
        if (socialAccounts.instagram && !socialAccounts.instagram.reauth_required) {
          connectedPlatforms.push('instagram');
        }
        if (socialAccounts.linkedin && !socialAccounts.linkedin.reauth_required) {
          connectedPlatforms.push('linkedin');
        }

        return {
          username: profile.username,
          displayName: profile.display_name || profile.username,
          connectedPlatforms,
          platformDetails: {
            facebook: socialAccounts.facebook && !socialAccounts.facebook.reauth_required ? {
              pageId: socialAccounts.facebook.page_id || 'default',
              pageName: socialAccounts.facebook.display_name || 'Facebook Page'
            } : null,
            twitter: socialAccounts.x && !socialAccounts.x.reauth_required ? {
              userId: socialAccounts.x.user_id || 'default',
              username: socialAccounts.x.display_name || profile.username
            } : null,
            instagram: socialAccounts.instagram && !socialAccounts.instagram.reauth_required ? {
              userId: socialAccounts.instagram.user_id || 'default',
              username: socialAccounts.instagram.display_name || profile.username
            } : null,
            linkedin: socialAccounts.linkedin && !socialAccounts.linkedin.reauth_required ? {
              pageId: socialAccounts.linkedin.page_id || 'default',
              pageName: socialAccounts.linkedin.display_name || 'LinkedIn Page'
            } : null
          },
          isActive: true,
          lastSync: new Date().toISOString()
        };
      });

      console.log('✅ Perfiles procesados:', processedProfiles.length);
      console.log('📋 Perfiles finales:', processedProfiles.map((p: any) => ({
        username: p.username,
        platforms: p.connectedPlatforms
      })));

      return NextResponse.json({
        success: true,
        profiles: processedProfiles,
        summary: {
          totalProfiles: processedProfiles.length,
          withFacebook: processedProfiles.filter((p: any) => p.connectedPlatforms.includes('facebook')).length,
          withTwitter: processedProfiles.filter((p: any) => p.connectedPlatforms.includes('twitter')).length,
          withInstagram: processedProfiles.filter((p: any) => p.connectedPlatforms.includes('instagram')).length,
          withLinkedin: processedProfiles.filter((p: any) => p.connectedPlatforms.includes('linkedin')).length
        },
        recommendations: [
          '📋 Estos son los perfiles disponibles en tu cuenta de upload-post',
          '🔗 Cada perfil puede tener múltiples plataformas conectadas',
          '⚙️ Asigna cada perfil a un diario específico',
          '✅ Verifica que las plataformas necesarias estén conectadas'
        ],
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error conectando con upload-post:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error conectando con upload-post',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [UPLOAD-POST PROFILES] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
