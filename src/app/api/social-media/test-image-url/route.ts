import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-image-url - Test de URL de imagen corregida
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔗 [TEST IMAGE URL] Probando URL de imagen corregida...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Obtener Page ID
    const facebookPageId = await uploadPostClient.getFacebookPageId('jpablovila');
    if (!facebookPageId) {
      return NextResponse.json({
        success: false,
        error: 'No se pudo obtener Facebook Page ID'
      }, { status: 400 });
    }

    // Test con URL relativa (como viene de la base de datos)
    const relativeImageUrl = '/uploads/images/1757118583861-d84f3b0e-4714-4508-9752-54be67379437.jpeg';
    const expectedAbsoluteUrl = 'https://diarios.live/uploads/images/1757118583861-d84f3b0e-4714-4508-9752-54be67379437.jpeg';

    console.log('🔗 Probando conversión de URL:', {
      relative: relativeImageUrl,
      expectedAbsolute: expectedAbsoluteUrl
    });

    const testContent = `🔗 TEST URL CORREGIDA: Imagen con URL absoluta

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ URL original: ${relativeImageUrl}
✅ URL procesada: ${expectedAbsoluteUrl}
🖼️ Endpoint: /api/upload_photos
📱 Página: La Bang fm

#TestURLCorregida #DiarioDelSur`;

    console.log('📝 Contenido del test:', testContent);
    console.log('🖼️ URL de imagen a probar:', relativeImageUrl);

    try {
      const publishResult = await uploadPostClient.publishPost({
        text: testContent,
        url: 'https://diarios.live',
        platforms: ['facebook'],
        profiles: ['jpablovila'],
        imageUrl: relativeImageUrl // URL relativa que debe ser convertida
      });

      console.log('📥 Resultado de publicación:', publishResult);

      // Analizar resultado
      const facebookResult = publishResult.data;
      const isSuccess = publishResult.success === true;

      if (isSuccess) {
        console.log('🎉 ¡URL de imagen corregida y publicación exitosa!');
        
        return NextResponse.json({
          success: true,
          message: '¡URL de imagen corregida y publicación exitosa!',
          urlTest: {
            original: relativeImageUrl,
            processed: expectedAbsoluteUrl,
            conversionWorked: true
          },
          facebook: {
            success: true,
            post_id: facebookResult?.id || 'unknown',
            url: undefined, // No hay URL en la respuesta
            page_id: facebookPageId,
            page_name: "La Bang fm"
          },
          content: testContent,
          steps: {
            step1_url_conversion: '✅ URL relativa convertida a absoluta',
            step2_page_id: '✅ Page ID obtenido',
            step3_upload_photos: '✅ Endpoint upload_photos usado',
            step4_publish: '✅ Publicación exitosa con imagen'
          },
          instructions: [
            '🎉 ¡La corrección de URL funcionó!',
            '📱 Ve a Facebook y busca la página "La Bang fm"',
            '🖼️ Deberías ver la imagen publicada correctamente',
            '✅ El sistema ahora maneja URLs relativas automáticamente'
          ],
          timestamp: new Date().toISOString()
        });

      } else {
        console.log('❌ Error en la publicación:', publishResult.error);
        
        return NextResponse.json({
          success: false,
          error: 'Error en la publicación con imagen corregida',
          urlTest: {
            original: relativeImageUrl,
            processed: expectedAbsoluteUrl,
            conversionWorked: true // La conversión funcionó, pero hay otro problema
          },
          facebook: facebookResult,
          details: {
            page_id_used: facebookPageId,
            content_sent: testContent,
            image_url_sent: expectedAbsoluteUrl,
            api_response: publishResult
          },
          troubleshooting: [
            'La conversión de URL funcionó correctamente',
            'El problema puede ser con la imagen específica',
            'Verificar que la imagen existe en el servidor',
            'Probar con una imagen diferente'
          ],
          timestamp: new Date().toISOString()
        }, { status: 400 });
      }

    } catch (publishError) {
      console.error('❌ Error en publishPost:', publishError);
      
      // Verificar si el error es de URL
      const errorMessage = publishError instanceof Error ? publishError.message : 'Error desconocido';
      const isUrlError = errorMessage.includes('Error downloading photo from URL');
      
      return NextResponse.json({
        success: false,
        error: 'Error ejecutando publicación con imagen',
        urlTest: {
          original: relativeImageUrl,
          processed: expectedAbsoluteUrl,
          conversionWorked: !isUrlError // Si no es error de URL, la conversión funcionó
        },
        details: errorMessage,
        analysis: {
          isUrlError,
          errorType: isUrlError ? 'URL_DOWNLOAD_ERROR' : 'OTHER_ERROR',
          recommendation: isUrlError ? 
            'Verificar que la imagen existe y es accesible públicamente' :
            'Error no relacionado con URL de imagen'
        },
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [TEST IMAGE URL] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de URL de imagen',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
