import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/sync-profiles - Sincronizar perfiles de upload-post
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔄 Sincronizando perfiles de upload-post...');

    // Verificar si tenemos API key
    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key de upload-post no configurada',
        note: 'Configura UPLOAD_POST_API_KEY en las variables de entorno'
      }, { status: 400 });
    }

    let uploadPostClient;
    let profiles = [];

    try {
      uploadPostClient = new UploadPostClient(apiKey);
      console.log('✅ Cliente de upload-post inicializado');
    } catch (error) {
      console.error('❌ Error inicializando cliente:', error);
      return NextResponse.json({
        success: false,
        error: 'Error inicializando cliente de upload-post',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, { status: 500 });
    }

    // Verificar conexión y obtener perfiles
    try {
      console.log('🔍 Verificando conexión con upload-post...');
      const isConnected = await uploadPostClient.testConnection();

      if (!isConnected) {
        return NextResponse.json({
          success: false,
          error: 'No se pudo conectar con upload-post.com',
          note: 'Verifica que la API key sea válida'
        }, { status: 503 });
      }

      console.log('✅ Conexión exitosa, obteniendo perfiles...');
      profiles = await uploadPostClient.getProfiles();
      console.log(`📱 ${profiles.length} perfiles obtenidos de upload-post`);
      console.log('🔍 Estructura de perfiles:', JSON.stringify(profiles, null, 2));

    } catch (error) {
      console.error('❌ Error conectando con upload-post:', error);
      return NextResponse.json({
        success: false,
        error: 'Error conectando con upload-post.com',
        details: error instanceof Error ? error.message : 'Error desconocido',
        note: 'Verifica tu API key y conexión a internet'
      }, { status: 503 });
    }

    // Validar estructura de perfiles
    if (!Array.isArray(profiles)) {
      console.error('❌ Perfiles no es un array:', typeof profiles);
      return NextResponse.json({
        success: false,
        error: 'Estructura de perfiles inválida',
        details: `Esperado array, recibido ${typeof profiles}`
      }, { status: 500 });
    }

    // Sincronizar con nuestra base de datos
    const syncResults = [];

    for (const profile of profiles) {
      // Validar estructura del perfil
      if (!profile || typeof profile !== 'object') {
        console.warn('⚠️ Perfil inválido saltado:', profile);
        continue;
      }

      if (!profile.id || !profile.platform || !profile.name) {
        console.warn('⚠️ Perfil con datos faltantes saltado:', {
          id: profile.id,
          platform: profile.platform,
          name: profile.name
        });
        continue;
      }
      try {
        console.log(`🔄 Procesando perfil: ${profile.platform} - ${profile.name} (${profile.id})`);

        // Buscar si ya existe
        const existingAccount = await prisma.socialMediaAccount.findFirst({
          where: {
            profileId: profile.id,
            platform: profile.platform
          }
        });

        if (existingAccount) {
          // Actualizar cuenta existente
          const updated = await prisma.socialMediaAccount.update({
            where: { id: existingAccount.id },
            data: {
              accountName: profile.name,
              isActive: profile.is_connected
            }
          });

          syncResults.push({
            action: 'updated',
            platform: profile.platform,
            accountName: profile.name,
            profileId: profile.id,
            id: updated.id
          });
        } else {
          // Crear nueva cuenta
          const created = await prisma.socialMediaAccount.create({
            data: {
              platform: profile.platform,
              accountName: profile.name,
              profileId: profile.id,
              isActive: profile.is_connected
            }
          });

          syncResults.push({
            action: 'created',
            platform: profile.platform,
            accountName: profile.name,
            profileId: profile.id,
            id: created.id
          });
        }
      } catch (error) {
        console.error(`❌ Error sincronizando perfil:`, {
          profileId: profile?.id,
          platform: profile?.platform,
          name: profile?.name,
          error: error instanceof Error ? error.message : error
        });

        syncResults.push({
          action: 'error',
          platform: profile?.platform || 'unknown',
          accountName: profile?.name || 'unknown',
          profileId: profile?.id || 'unknown',
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    // Desactivar cuentas que ya no existen en upload-post
    const uploadPostProfileIds = profiles.map(p => p.id);
    await prisma.socialMediaAccount.updateMany({
      where: {
        profileId: {
          notIn: uploadPostProfileIds
        },
        isActive: true
      },
      data: {
        isActive: false
      }
    });

    const summary = {
      total: profiles.length,
      created: syncResults.filter(r => r.action === 'created').length,
      updated: syncResults.filter(r => r.action === 'updated').length,
      errors: syncResults.filter(r => r.action === 'error').length
    };

    console.log('✅ Sincronización completada:', summary);

    return NextResponse.json({
      success: true,
      message: 'Perfiles sincronizados exitosamente',
      summary,
      results: syncResults,
      uploadPostProfiles: profiles.map(p => ({
        id: p.id,
        platform: p.platform,
        name: p.name,
        isConnected: p.is_connected
      }))
    });

  } catch (error) {
    console.error('❌ Error sincronizando perfiles:', error);
    
    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes('API key')) {
        statusCode = 401;
      } else if (error.message.includes('Upload-Post API Error')) {
        statusCode = 503;
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString()
    }, { status: statusCode });
  }
}

// GET /api/social-media/sync-profiles - Obtener estado de sincronización
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener cuentas actuales
    const accounts = await prisma.socialMediaAccount.findMany({
      orderBy: [
        { platform: 'asc' },
        { accountName: 'asc' }
      ]
    });

    // Verificar configuración
    const hasApiKey = !!process.env.UPLOAD_POST_API_KEY;
    let connectionStatus = 'not_tested';
    let connectionError = null;

    if (hasApiKey) {
      try {
        console.log('🔍 Probando conexión con upload-post...');
        const uploadPostClient = new UploadPostClient();
        const isConnected = await uploadPostClient.testConnection();
        connectionStatus = isConnected ? 'connected' : 'failed';
        console.log(`✅ Estado de conexión: ${connectionStatus}`);
      } catch (error) {
        console.error('❌ Error probando conexión:', error);
        connectionStatus = 'error';
        connectionError = error instanceof Error ? error.message : 'Error desconocido';
      }
    } else {
      console.log('⚠️ API key no configurada');
    }

    return NextResponse.json({
      success: true,
      configuration: {
        hasApiKey,
        connectionStatus,
        connectionError,
        apiKeyLength: hasApiKey ? process.env.UPLOAD_POST_API_KEY?.length : 0
      },
      accounts: accounts.map(account => ({
        id: account.id,
        platform: account.platform,
        accountName: account.accountName,
        profileId: account.profileId,
        isActive: account.isActive,
        createdAt: account.createdAt,
        updatedAt: account.updatedAt
      })),
      summary: {
        total: accounts.length,
        active: accounts.filter(a => a.isActive).length,
        byPlatform: {
          facebook: accounts.filter(a => a.platform === 'facebook').length,
          twitter: accounts.filter(a => a.platform === 'twitter').length,
          instagram: accounts.filter(a => a.platform === 'instagram').length
        }
      }
    });

  } catch (error) {
    console.error('❌ Error obteniendo estado de sincronización:', error);
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
