import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

const prisma = new PrismaClient();

// POST /api/social-media/publish-news - Publicar noticias inmediatamente
// Ruta alternativa para evitar problemas de routing
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      publicacionExternaId, 
      platforms, 
      accountIds, 
      customCaption 
    } = body;

    console.log('🚀 [PUBLISH-NEWS] Iniciando publicación inmediata en redes sociales:', {
      publicacionExternaId,
      platforms,
      accountIds: Array.isArray(accountIds) ? accountIds : [accountIds],
      hasCustomCaption: !!customCaption,
      user: session.user?.email
    });

    // Validaciones
    if (!publicacionExternaId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'publicacionExternaId es requerido' 
        },
        { status: 400 }
      );
    }

    if (!platforms || !Array.isArray(platforms) || platforms.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'platforms debe ser un array no vacío' 
        },
        { status: 400 }
      );
    }

    // Obtener datos de la publicación externa
    const publicacionExterna = await prisma.publicacionExterna.findUnique({
      where: { id: parseInt(publicacionExternaId) },
      include: {
        noticia: {
          select: {
            titulo: true,
            contenido: true,
            imagenUrl: true
          }
        }
      }
    });

    if (!publicacionExterna) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Publicación externa no encontrada' 
        },
        { status: 404 }
      );
    }

    // Verificar si tenemos API key de upload-post
    const uploadPostApiKey = process.env.UPLOAD_POST_API_KEY;
    let useAdvancedDemo = false;

    if (!uploadPostApiKey) {
      console.log('⚠️ API key no configurada, usando modo demo avanzado');
      useAdvancedDemo = true;
    } else {
      // Probar conexión rápida con upload-post usando endpoint correcto
      try {
        console.log('🔍 Probando conexión rápida con upload-post...');
        const testResponse = await fetch('https://api.upload-post.com/api/uploadposts/facebook/pages', {
          method: 'GET',
          headers: {
            'Authorization': `Apikey ${uploadPostApiKey}`,
          },
        });

        console.log('📡 Respuesta de upload-post:', {
          status: testResponse.status,
          contentType: testResponse.headers.get('content-type'),
          ok: testResponse.ok
        });

        const contentType = testResponse.headers.get('content-type');
        const isJson = contentType?.includes('application/json');

        if (!testResponse.ok) {
          console.log('⚠️ Upload-post responde con error, usando modo demo avanzado');
          useAdvancedDemo = true;
        } else if (!isJson) {
          console.log('⚠️ Upload-post devuelve HTML, usando modo demo avanzado');
          useAdvancedDemo = true;
        } else {
          console.log('✅ Upload-post disponible, usando API real');
        }
      } catch (error) {
        console.log('⚠️ Error conectando con upload-post, usando modo demo avanzado:', error);
        useAdvancedDemo = true;
      }
    }

    // Usar API real
    if (!useAdvancedDemo) {
      console.log('🚀 Ejecutando publicación con API real...');
      
      const uploadPostClient = new UploadPostClient(uploadPostApiKey!);
      
      // Generar caption
      const caption = customCaption || `📰 ${publicacionExterna.noticia.titulo}

¡Lee la noticia completa! 👆

${publicacionExterna.urlPublicacion}

#Noticias #Actualidad #DiarioDelSur`;

      try {
        const result = await uploadPostClient.publishPost({
          text: caption,
          url: publicacionExterna.urlPublicacion || undefined,
          platforms: platforms,
          profiles: ['jpablovila'],
          scheduledDate: body.scheduledFor // Agregar soporte para programación
        });

        console.log('✅ Publicación exitosa:', result);

        // Crear registros en la base de datos
        const results = [];
        for (const platform of platforms) {
          const platformData = result.data?.platforms?.find(p =>
            p.platform === (platform === 'twitter' ? 'x' : platform)
          );

          if (platformData && platformData.status === 'success') {
            const publication = await prisma.socialMediaPublication.create({
              data: {
                publicacionExternaId: parseInt(publicacionExternaId),
                socialMediaAccountId: 1, // Mock account ID
                platform: platform,
                caption,
                status: 'SUCCESS',
                publishedAt: new Date(),
                externalPostId: platformData.post_id || platformData.platform,
                createdBy: session.user?.email || 'system'
              }
            });

            results.push({
              platform: platform,
              accountId: 1,
              accountName: `Cuenta ${platform}`,
              success: true,
              publicationId: publication.id,
              externalPostId: platformData.post_id || platformData.platform,
              url: undefined, // No hay URL en la respuesta de upload-post
              message: `Publicado exitosamente en ${platform}`,
              publishedAt: new Date().toISOString()
            });
          } else {
            results.push({
              platform: platform,
              accountId: 1,
              accountName: `Cuenta ${platform}`,
              success: false,
              error: platformData?.error || 'Error desconocido',
              message: `Error publicando en ${platform}`
            });
          }
        }

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;

        return NextResponse.json({
          success: successCount > 0,
          results,
          summary: {
            total: totalCount,
            successful: successCount,
            failed: totalCount - successCount
          },
          message: successCount === totalCount
            ? `¡Publicado exitosamente en ${successCount} cuenta${successCount > 1 ? 's' : ''}!`
            : successCount > 0
              ? `Publicado en ${successCount}/${totalCount} cuentas. ${totalCount - successCount} fallaron.`
              : `Error: No se pudo publicar en ninguna cuenta.`,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('❌ Error en publicación con API real:', error);
        
        return NextResponse.json({
          success: false,
          error: 'Error en publicación con API real',
          details: error instanceof Error ? error.message : 'Error desconocido',
          timestamp: new Date().toISOString()
        }, { status: 500 });
      }
    } else {
      // Modo demo (fallback)
      console.log('🎭 Ejecutando modo demo...');
      
      return NextResponse.json({
        success: true,
        results: platforms.map(platform => ({
          platform,
          accountId: 1,
          accountName: `Cuenta ${platform}`,
          success: true,
          message: `Simulado exitosamente en ${platform} (modo demo)`,
          demoMode: true
        })),
        summary: {
          total: platforms.length,
          successful: platforms.length,
          failed: 0
        },
        message: `¡Simulado exitosamente en ${platforms.length} cuenta${platforms.length > 1 ? 's' : ''}! (modo demo)`,
        demoMode: true,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('❌ [PUBLISH-NEWS] Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
