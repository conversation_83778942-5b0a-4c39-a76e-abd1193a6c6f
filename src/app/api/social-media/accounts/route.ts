import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

const prisma = new PrismaClient();

// GET /api/social-media/accounts - Obtener cuentas de redes sociales
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📋 Obteniendo cuentas de redes sociales...');

    // Intentar obtener cuentas reales de la base de datos
    let accounts: any[] = [];
    try {
      accounts = await prisma.socialMediaAccount.findMany({
        where: {
          isActive: true
        },
        orderBy: [
          { platform: 'asc' },
          { accountName: 'asc' }
        ]
      });
    } catch (dbError) {
      console.warn('⚠️ Error accediendo a cuentas reales, usando cuentas demo:', dbError);
    }

    // Si no hay cuentas configuradas, crear cuentas demo
    if (accounts.length === 0) {
      console.log('📱 No hay cuentas configuradas, creando cuentas demo...');

      // Crear cuentas demo para las plataformas principales
      const demoPlatforms = ['facebook', 'twitter', 'instagram'];
      const demoAccounts = [];

      for (const platform of demoPlatforms) {
        try {
          const account = await prisma.socialMediaAccount.create({
            data: {
              platform: platform,
              accountName: `Demo ${platform.charAt(0).toUpperCase() + platform.slice(1)} Account`,
              profileId: `demo_${platform}_${Date.now()}`,
              isActive: true
            }
          });
          demoAccounts.push(account);
        } catch (error) {
          console.warn(`⚠️ Error creando cuenta demo para ${platform}:`, error);
        }
      }

      accounts = demoAccounts;
      console.log(`✅ ${demoAccounts.length} cuentas demo creadas`);
    }

    console.log(`✅ ${accounts.length} cuentas encontradas`);

    // Detectar si son cuentas demo
    const isDemoMode = accounts.some(a => a.profileId?.startsWith('demo_'));

    // Verificar configuración de upload-post
    const hasApiKey = !!process.env.UPLOAD_POST_API_KEY;
    let connectionStatus = 'not_tested';
    let connectionError = null;

    if (hasApiKey) {
      try {
        console.log('🔍 Probando conexión con upload-post...');
        const uploadPostClient = new UploadPostClient();
        const isConnected = await uploadPostClient.testConnection();
        connectionStatus = isConnected ? 'connected' : 'failed';
        console.log(`✅ Estado de conexión: ${connectionStatus}`);
      } catch (error) {
        console.error('❌ Error probando conexión:', error);
        connectionStatus = 'error';
        connectionError = error instanceof Error ? error.message : 'Error desconocido';
      }
    } else {
      console.log('⚠️ API key no configurada');
    }

    return NextResponse.json({
      success: true,
      accounts: accounts.map(account => ({
        id: account.id,
        platform: account.platform,
        username: account.accountName, // Para compatibilidad con la interfaz
        isActive: account.isActive,
        lastSync: account.updatedAt,
        isDemoAccount: account.profileId?.startsWith('demo_') || false
      })),
      syncStatus: {
        hasApiKey,
        connectionStatus,
        lastSync: null,
        profileCount: accounts.length
      },
      demoMode: isDemoMode,
      realAccounts: !isDemoMode,
      summary: {
        total: accounts.length,
        active: accounts.filter(a => a.isActive).length,
        demo: accounts.filter(a => a.profileId?.startsWith('demo_')).length,
        real: accounts.filter(a => !a.profileId?.startsWith('demo_')).length,
        byPlatform: {
          facebook: accounts.filter(a => a.platform === 'facebook').length,
          twitter: accounts.filter(a => a.platform === 'twitter').length,
          instagram: accounts.filter(a => a.platform === 'instagram').length
        }
      },
      note: isDemoMode
        ? 'Cuentas demo activas. Configura upload-post.com para publicación real.'
        : 'Cuentas reales sincronizadas desde upload-post.com'
    });
  } catch (error) {
    console.error('❌ Error obteniendo cuentas:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST /api/social-media/accounts - Crear nueva cuenta
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { platform, accountName, profileId, isActive = true } = body;

    // Validaciones
    if (!platform || !accountName || !profileId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Faltan campos requeridos: platform, accountName, profileId' 
        },
        { status: 400 }
      );
    }

    if (!['facebook', 'twitter', 'instagram'].includes(platform)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Plataforma no válida. Debe ser: facebook, twitter, instagram' 
        },
        { status: 400 }
      );
    }

    console.log('➕ Creando nueva cuenta de redes sociales:', {
      platform,
      accountName,
      profileId
    });

    // Verificar que el profileId existe en upload-post
    try {
      const uploadPostClient = new UploadPostClient();
      const profiles = await uploadPostClient.getProfiles();
      const profileExists = profiles.some(p => p.id === profileId && p.platform === platform);
      
      if (!profileExists) {
        return NextResponse.json(
          { 
            success: false, 
            error: `Perfil ${profileId} no encontrado en upload-post para la plataforma ${platform}` 
          },
          { status: 400 }
        );
      }
    } catch (error) {
      console.warn('⚠️ No se pudo verificar el perfil en upload-post:', error);
      // Continuar sin verificación si hay error de conexión
    }

    // Verificar que no exista ya una cuenta con el mismo profileId
    const existingAccount = await prisma.socialMediaAccount.findFirst({
      where: {
        platform,
        profileId
      }
    });

    if (existingAccount) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Ya existe una cuenta configurada para este perfil' 
        },
        { status: 409 }
      );
    }

    // Crear la cuenta
    const account = await prisma.socialMediaAccount.create({
      data: {
        platform,
        accountName,
        profileId,
        isActive
      }
    });

    console.log('✅ Cuenta creada exitosamente:', account.id);

    return NextResponse.json({
      success: true,
      account
    });
  } catch (error) {
    console.error('❌ Error creando cuenta:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}

// PUT /api/social-media/accounts - Actualizar cuenta existente
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { id, accountName, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de cuenta requerido' 
        },
        { status: 400 }
      );
    }

    console.log('✏️ Actualizando cuenta de redes sociales:', id);

    // Verificar que la cuenta existe
    const existingAccount = await prisma.socialMediaAccount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingAccount) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cuenta no encontrada' 
        },
        { status: 404 }
      );
    }

    // Actualizar solo los campos proporcionados
    const updateData: any = {};
    if (accountName !== undefined) updateData.accountName = accountName;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedAccount = await prisma.socialMediaAccount.update({
      where: { id: parseInt(id) },
      data: updateData
    });

    console.log('✅ Cuenta actualizada exitosamente');

    return NextResponse.json({
      success: true,
      account: updatedAccount
    });
  } catch (error) {
    console.error('❌ Error actualizando cuenta:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/social-media/accounts - Eliminar cuenta
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de cuenta requerido' 
        },
        { status: 400 }
      );
    }

    console.log('🗑️ Eliminando cuenta de redes sociales:', id);

    // Verificar que la cuenta existe
    const existingAccount = await prisma.socialMediaAccount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingAccount) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cuenta no encontrada' 
        },
        { status: 404 }
      );
    }

    // Verificar si hay publicaciones asociadas
    const publicationsCount = await prisma.socialMediaPublication.count({
      where: { socialMediaAccountId: parseInt(id) }
    });

    if (publicationsCount > 0) {
      // En lugar de eliminar, desactivar la cuenta
      await prisma.socialMediaAccount.update({
        where: { id: parseInt(id) },
        data: { isActive: false }
      });

      console.log('⚠️ Cuenta desactivada (tiene publicaciones asociadas)');

      return NextResponse.json({
        success: true,
        message: 'Cuenta desactivada (tiene publicaciones asociadas)',
        deactivated: true
      });
    } else {
      // Eliminar completamente si no hay publicaciones
      await prisma.socialMediaAccount.delete({
        where: { id: parseInt(id) }
      });

      console.log('✅ Cuenta eliminada exitosamente');

      return NextResponse.json({
        success: true,
        message: 'Cuenta eliminada exitosamente',
        deleted: true
      });
    }
  } catch (error) {
    console.error('❌ Error eliminando cuenta:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}
