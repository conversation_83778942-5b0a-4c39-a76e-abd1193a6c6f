import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// GET /api/social-media/profiles - Obtener perfiles disponibles de upload-post
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('👥 Obteniendo perfiles de upload-post...');

    const uploadPostClient = new UploadPostClient();
    
    // Verificar conexión primero
    const isConnected = await uploadPostClient.testConnection();
    if (!isConnected) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No se pudo conectar con upload-post. Verifica la API key.' 
        },
        { status: 503 }
      );
    }

    // Obtener perfiles
    const profiles = await uploadPostClient.getProfiles();

    // Agrupar por plataforma
    const profilesByPlatform = profiles.reduce((acc, profile) => {
      if (!acc[profile.platform]) {
        acc[profile.platform] = [];
      }
      acc[profile.platform].push(profile);
      return acc;
    }, {} as Record<string, any[]>);

    console.log(`✅ ${profiles.length} perfiles obtenidos:`, {
      facebook: profilesByPlatform.facebook?.length || 0,
      twitter: profilesByPlatform.twitter?.length || 0,
      instagram: profilesByPlatform.instagram?.length || 0
    });

    return NextResponse.json({
      success: true,
      profiles,
      profilesByPlatform,
      totalProfiles: profiles.length
    });
  } catch (error) {
    console.error('❌ Error obteniendo perfiles:', error);
    
    let errorMessage = 'Error interno del servidor';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'API key de upload-post inválida o expirada';
        statusCode = 401;
      } else if (error.message.includes('fetch')) {
        errorMessage = 'Error de conexión con upload-post';
        statusCode = 503;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage 
      },
      { status: statusCode }
    );
  }
}

// POST /api/social-media/profiles/test - Probar conexión con upload-post
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { apiKey } = body;

    console.log('🔍 Probando conexión con upload-post...');

    // Usar API key proporcionada o la del entorno
    const uploadPostClient = new UploadPostClient(apiKey);
    
    // Probar conexión
    const isConnected = await uploadPostClient.testConnection();
    
    if (isConnected) {
      // Obtener información adicional
      try {
        const [profiles, limits] = await Promise.all([
          uploadPostClient.getProfiles(),
          uploadPostClient.getAccountLimits().catch(() => null)
        ]);

        console.log('✅ Conexión exitosa con upload-post');

        return NextResponse.json({
          success: true,
          connected: true,
          profilesCount: profiles.length,
          limits,
          message: 'Conexión exitosa con upload-post'
        });
      } catch (error) {
        console.log('✅ Conexión básica exitosa, pero error obteniendo detalles');
        
        return NextResponse.json({
          success: true,
          connected: true,
          message: 'Conexión exitosa con upload-post (detalles limitados)'
        });
      }
    } else {
      console.log('❌ Conexión fallida con upload-post');
      
      return NextResponse.json({
        success: false,
        connected: false,
        error: 'No se pudo conectar con upload-post. Verifica la API key.'
      });
    }
  } catch (error) {
    console.error('❌ Error probando conexión:', error);
    
    let errorMessage = 'Error probando conexión';
    
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        errorMessage = 'API key inválida o expirada';
      } else if (error.message.includes('fetch') || error.message.includes('network')) {
        errorMessage = 'Error de red. Verifica tu conexión a internet.';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json({
      success: false,
      connected: false,
      error: errorMessage
    });
  }
}
