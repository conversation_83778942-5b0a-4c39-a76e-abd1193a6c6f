import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-final-system - Test final del sistema completo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🎉 [TEST FINAL SYSTEM] Probando sistema completo...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    const timestamp = new Date().toISOString();
    const results = [];

    // Test 1: Solo Facebook
    console.log('📘 Test 1: Solo Facebook...');
    try {
      const facebookContent = `📘 TEST FINAL FACEBOOK: Sistema completo funcionando

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Estado: Sistema completamente funcional
🏷️ Open Graph: Detección automática
🔗 Enlace: https://diarios.live
📱 Página: La Bang fm

#TestFinalFacebook #DiarioDelSur #SistemaCompleto`;

      const facebookResult = await uploadPostClient.publishPost({
        text: facebookContent,
        url: 'https://diarios.live',
        platforms: ['facebook'],
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Solo Facebook',
        platform: 'facebook',
        success: facebookResult.success === true,
        result: facebookResult.data,
        content: facebookContent
      });

      console.log('✅ Test Facebook completado');
    } catch (error) {
      results.push({
        test: 'Solo Facebook',
        platform: 'facebook',
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test Facebook:', error);
    }

    // Esperar entre tests
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 2: Solo Twitter
    console.log('🐦 Test 2: Solo Twitter...');
    try {
      const twitterContent = `🐦 TEST FINAL TWITTER: Sistema X funcionando

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Estado: Twitter/X completamente funcional
🔗 Enlace: https://diarios.live
📱 Detección automática de enlaces

#TestFinalTwitter #DiarioDelSur #XFuncionando`;

      const twitterResult = await uploadPostClient.publishPost({
        text: twitterContent,
        url: 'https://diarios.live',
        platforms: ['twitter'], // Se mapea a 'x' automáticamente
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Solo Twitter',
        platform: 'twitter',
        mappedTo: 'x',
        success: twitterResult.success === true,
        result: twitterResult.data,
        content: twitterContent
      });

      console.log('✅ Test Twitter completado');
    } catch (error) {
      results.push({
        test: 'Solo Twitter',
        platform: 'twitter',
        mappedTo: 'x',
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test Twitter:', error);
    }

    // Esperar entre tests
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test 3: Facebook y Twitter juntos
    console.log('🌐 Test 3: Facebook + Twitter juntos...');
    try {
      const multiContent = `🌐 TEST FINAL MULTI: Facebook + Twitter funcionando

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Estado: Sistema multi-plataforma completo
📘 Facebook: Open Graph automático
🐦 Twitter/X: Enlaces automáticos
🔗 Enlace: https://diarios.live

#TestFinalMulti #DiarioDelSur #FacebookTwitter #SistemaCompleto`;

      const multiResult = await uploadPostClient.publishPost({
        text: multiContent,
        url: 'https://diarios.live',
        platforms: ['facebook', 'twitter'],
        profiles: ['jpablovila']
      });

      results.push({
        test: 'Facebook + Twitter',
        platforms: ['facebook', 'twitter'],
        mappedPlatforms: ['facebook', 'x'],
        success: multiResult.success === true,
        facebookResult: multiResult.data,
        twitterResult: multiResult.data,
        content: multiContent,
        bothWorking: multiResult.success === true
      });

      console.log('✅ Test multi-plataforma completado');
    } catch (error) {
      results.push({
        test: 'Facebook + Twitter',
        platforms: ['facebook', 'twitter'],
        mappedPlatforms: ['facebook', 'x'],
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido'
      });
      console.log('❌ Error en test multi-plataforma:', error);
    }

    // Analizar resultados
    const successfulTests = results.filter(r => r.success);
    const failedTests = results.filter(r => !r.success);
    const facebookWorking = results.find(r => r.platform === 'facebook')?.success || false;
    const twitterWorking = results.find(r => r.platform === 'twitter')?.success || false;
    const multiWorking = results.find(r => r.platforms)?.success || false;

    return NextResponse.json({
      success: successfulTests.length > 0,
      summary: {
        total: results.length,
        successful: successfulTests.length,
        failed: failedTests.length,
        facebookWorking,
        twitterWorking,
        multiPlatformWorking: multiWorking,
        systemReady: facebookWorking && twitterWorking
      },
      results,
      systemStatus: {
        facebook: {
          working: facebookWorking,
          method: 'Open Graph automático',
          pageId: '227853537315084',
          pageName: 'La Bang fm'
        },
        twitter: {
          working: twitterWorking,
          method: 'Detección automática de enlaces',
          mapping: 'twitter → x',
          format: 'platform[]'
        },
        multiPlatform: {
          working: multiWorking,
          simultaneous: true,
          bothPlatforms: facebookWorking && twitterWorking
        }
      },
      finalStatus: facebookWorking && twitterWorking ? 
        '🎉 SISTEMA COMPLETAMENTE FUNCIONAL' :
        facebookWorking ? 
          '📘 SOLO FACEBOOK FUNCIONAL' :
          twitterWorking ?
            '🐦 SOLO TWITTER FUNCIONAL' :
            '❌ SISTEMA NECESITA CONFIGURACIÓN',
      recommendations: [
        facebookWorking && twitterWorking ? '🎉 Sistema listo para producción' : '⚠️ Sistema parcialmente funcional',
        facebookWorking ? '📘 Facebook: Listo para noticias' : '❌ Facebook: Necesita configuración',
        twitterWorking ? '🐦 Twitter: Listo para noticias' : '❌ Twitter: Necesita configuración',
        'Usar botón "Publicar en Redes" en /redes-sociales'
      ],
      instructions: [
        '📱 Ve a /redes-sociales para publicar noticias',
        '🔘 Haz clic en "Publicar en Redes" en cualquier noticia',
        '✅ Selecciona Facebook y/o Twitter según disponibilidad',
        '📝 Personaliza el caption si es necesario',
        '🚀 Publica y verifica en tus redes sociales',
        '🎉 ¡El sistema está listo para uso en producción!'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST FINAL SYSTEM] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test final del sistema',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
