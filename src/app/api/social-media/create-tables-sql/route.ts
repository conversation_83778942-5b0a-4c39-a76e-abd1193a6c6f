import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Pool } from 'pg';

// POST /api/social-media/create-tables-sql - Crear tablas usando SQL directo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 Creando tablas usando SQL directo...');

    const results = [];

    // Verificar DATABASE_URL
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL no está configurada');
    }

    if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
      throw new Error('DATABASE_URL debe ser una URL de PostgreSQL');
    }

    // Crear conexión directa a PostgreSQL
    const pool = new Pool({
      connectionString: databaseUrl,
    });

    try {
      // Paso 1: Crear tabla social_media_accounts
      try {
        console.log('📋 Creando tabla social_media_accounts...');
        await pool.query(`
          CREATE TABLE IF NOT EXISTS social_media_accounts (
            id SERIAL PRIMARY KEY,
            platform VARCHAR(50) NOT NULL,
            account_name VARCHAR(255) NOT NULL,
            profile_id VARCHAR(255) NOT NULL UNIQUE,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
        `);
        
        results.push({
          step: 'Create social_media_accounts',
          status: 'success',
          message: 'Tabla social_media_accounts creada exitosamente'
        });
      } catch (error) {
        results.push({
          step: 'Create social_media_accounts',
          status: 'error',
          message: error instanceof Error ? error.message : 'Error creando tabla'
        });
      }

      // Paso 2: Crear tabla social_media_publications
      try {
        console.log('📋 Creando tabla social_media_publications...');
        await pool.query(`
          CREATE TABLE IF NOT EXISTS social_media_publications (
            id SERIAL PRIMARY KEY,
            publicacion_externa_id INTEGER NOT NULL,
            social_media_account_id INTEGER NOT NULL,
            platform VARCHAR(50) NOT NULL,
            caption TEXT NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
            scheduled_for TIMESTAMP,
            published_at TIMESTAMP,
            external_post_id VARCHAR(255),
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by VARCHAR(255) NOT NULL,
            CONSTRAINT fk_social_media_account 
              FOREIGN KEY (social_media_account_id) 
              REFERENCES social_media_accounts(id) 
              ON DELETE CASCADE
          );
        `);
        
        results.push({
          step: 'Create social_media_publications',
          status: 'success',
          message: 'Tabla social_media_publications creada exitosamente'
        });
      } catch (error) {
        results.push({
          step: 'Create social_media_publications',
          status: 'error',
          message: error instanceof Error ? error.message : 'Error creando tabla'
        });
      }

      // Paso 3: Crear índices
      try {
        console.log('📋 Creando índices...');
        await pool.query(`
          CREATE INDEX IF NOT EXISTS idx_social_media_accounts_platform 
            ON social_media_accounts(platform);
          CREATE INDEX IF NOT EXISTS idx_social_media_accounts_profile_id 
            ON social_media_accounts(profile_id);
          CREATE INDEX IF NOT EXISTS idx_social_media_accounts_active 
            ON social_media_accounts(is_active);
          CREATE INDEX IF NOT EXISTS idx_social_media_publications_account_id 
            ON social_media_publications(social_media_account_id);
          CREATE INDEX IF NOT EXISTS idx_social_media_publications_status 
            ON social_media_publications(status);
          CREATE INDEX IF NOT EXISTS idx_social_media_publications_platform 
            ON social_media_publications(platform);
        `);
        
        results.push({
          step: 'Create Indexes',
          status: 'success',
          message: 'Índices creados exitosamente'
        });
      } catch (error) {
        results.push({
          step: 'Create Indexes',
          status: 'warning',
          message: 'Algunos índices ya existían o no se pudieron crear'
        });
      }

      // Paso 4: Verificar tablas
      try {
        console.log('🔍 Verificando tablas creadas...');
        const accountsResult = await pool.query('SELECT COUNT(*) FROM social_media_accounts');
        const publicationsResult = await pool.query('SELECT COUNT(*) FROM social_media_publications');
        
        results.push({
          step: 'Verify Tables',
          status: 'success',
          message: `Tablas verificadas: ${accountsResult.rows[0].count} cuentas, ${publicationsResult.rows[0].count} publicaciones`
        });
      } catch (error) {
        results.push({
          step: 'Verify Tables',
          status: 'error',
          message: error instanceof Error ? error.message : 'Error verificando tablas'
        });
      }

    } finally {
      await pool.end();
    }

    const hasErrors = results.some(r => r.status === 'error');
    const allSuccess = results.every(r => r.status === 'success');

    console.log('✅ Creación de tablas SQL completada');

    return NextResponse.json({
      success: !hasErrors,
      allSuccess,
      results,
      summary: {
        total: results.length,
        success: results.filter(r => r.status === 'success').length,
        errors: results.filter(r => r.status === 'error').length,
        warnings: results.filter(r => r.status === 'warning').length
      },
      message: hasErrors 
        ? 'Creación completada con algunos errores'
        : 'Tablas creadas exitosamente usando SQL directo',
      instructions: allSuccess ? [
        'Las tablas han sido creadas directamente en PostgreSQL',
        'Ahora regenera el cliente Prisma con: npx prisma generate',
        'Reinicia el servidor para cargar la nueva configuración',
        'Usa "Check DB" para verificar que todo funcione'
      ] : [
        'Revisa los errores reportados',
        'Verifica que PostgreSQL esté ejecutándose',
        'Confirma que DATABASE_URL sea correcta',
        'Intenta nuevamente después de corregir los problemas'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error creando tablas SQL:', error);
    return NextResponse.json({
      success: false,
      error: 'Error creando tablas con SQL directo',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
