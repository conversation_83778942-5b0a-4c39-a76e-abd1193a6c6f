import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/social-media/diario-profiles - Obtener configuración de perfiles por diario
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📋 [DIARIO PROFILES] Obteniendo configuración de perfiles...');

    // Obtener todos los diarios
    const diarios = await prisma.diario.findMany({
      select: {
        id: true,
        nombre: true,
        descripcion: true,
        isActive: true
      },
      orderBy: {
        nombre: 'asc'
      }
    });

    // Obtener configuraciones existentes de redes sociales (usando SQL directo)
    const configuraciones = await prisma.$queryRaw`
      SELECT
        dsc.id,
        dsc."diarioId",
        dsc."uploadPostProfile",
        dsc."facebookEnabled",
        dsc."twitterEnabled",
        dsc."instagramEnabled",
        dsc."linkedinEnabled",
        dsc."facebookPageId",
        dsc.activo,
        dsc."updatedAt",
        d.nombre as diario_nombre
      FROM diario_social_configs dsc
      JOIN diarios d ON dsc."diarioId" = d.id
    ` as any[];

    // Mapear configuraciones por diario
    const perfilesPorDiario = diarios.map(diario => {
      const config = configuraciones.find(c => c.diarioId === diario.id);
      
      return {
        diario: {
          id: diario.id,
          nombre: diario.nombre,
          descripcion: diario.descripcion,
          activo: diario.isActive
        },
        configuracion: config ? {
          id: config.id,
          uploadPostProfile: config.uploadPostProfile,
          facebookEnabled: config.facebookEnabled,
          twitterEnabled: config.twitterEnabled,
          instagramEnabled: config.instagramEnabled,
          linkedinEnabled: config.linkedinEnabled,
          facebookPageId: config.facebookPageId,
          configuradoEn: config.updatedAt,
          activo: config.activo
        } : null,
        estadoConexion: config ? 'configurado' : 'pendiente'
      };
    });

    return NextResponse.json({
      success: true,
      perfiles: perfilesPorDiario,
      resumen: {
        totalDiarios: diarios.length,
        configurados: configuraciones.filter(c => c.activo).length,
        pendientes: diarios.length - configuraciones.filter(c => c.activo).length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [DIARIO PROFILES] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error obteniendo configuración de perfiles',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// POST /api/social-media/diario-profiles - Configurar perfil de diario
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const {
      diarioId,
      uploadPostProfile,
      facebookEnabled = false,
      twitterEnabled = false,
      instagramEnabled = false,
      linkedinEnabled = false,
      facebookPageId = null
    } = await request.json();

    console.log('⚙️ [DIARIO PROFILES] Configurando perfil:', {
      diarioId,
      uploadPostProfile,
      plataformas: {
        facebook: facebookEnabled,
        twitter: twitterEnabled,
        instagram: instagramEnabled,
        linkedin: linkedinEnabled
      }
    });

    // Validar que el diario existe
    const diario = await prisma.diario.findUnique({
      where: { id: diarioId }
    });

    if (!diario) {
      return NextResponse.json({
        success: false,
        error: 'Diario no encontrado'
      }, { status: 404 });
    }

    // Crear o actualizar configuración usando SQL directo
    const existingConfig = await prisma.$queryRaw`
      SELECT id FROM diario_social_configs WHERE "diarioId" = ${diarioId}
    ` as any[];

    let configuracion;
    if (existingConfig.length > 0) {
      // Actualizar configuración existente
      await prisma.$executeRaw`
        UPDATE diario_social_configs
        SET
          "uploadPostProfile" = ${uploadPostProfile},
          "facebookEnabled" = ${facebookEnabled},
          "twitterEnabled" = ${twitterEnabled},
          "instagramEnabled" = ${instagramEnabled},
          "linkedinEnabled" = ${linkedinEnabled},
          "facebookPageId" = ${facebookPageId},
          "activo" = true,
          "updatedAt" = NOW()
        WHERE "diarioId" = ${diarioId}
      `;
      configuracion = { id: existingConfig[0].id };
    } else {
      // Crear nueva configuración
      const result = await prisma.$queryRaw`
        INSERT INTO diario_social_configs (
          "diarioId", "uploadPostProfile", "facebookEnabled", "twitterEnabled",
          "instagramEnabled", "linkedinEnabled", "facebookPageId", "activo"
        ) VALUES (
          ${diarioId}, ${uploadPostProfile}, ${facebookEnabled}, ${twitterEnabled},
          ${instagramEnabled}, ${linkedinEnabled}, ${facebookPageId}, true
        ) RETURNING id
      ` as any[];
      configuracion = { id: result[0].id };
    }

    // Obtener información del diario
    const diarioInfo = await prisma.diario.findUnique({
      where: { id: diarioId },
      select: { nombre: true, descripcion: true }
    });

    console.log('✅ [DIARIO PROFILES] Configuración guardada:', configuracion);

    return NextResponse.json({
      success: true,
      configuracion: {
        id: configuracion.id,
        diario: diarioInfo,
        uploadPostProfile: uploadPostProfile,
        plataformas: {
          facebook: facebookEnabled,
          twitter: twitterEnabled,
          instagram: instagramEnabled,
          linkedin: linkedinEnabled
        },
        facebookPageId: facebookPageId,
        activo: true
      },
      message: `Configuración de redes sociales para ${diarioInfo?.nombre} guardada exitosamente`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [DIARIO PROFILES] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error configurando perfil de diario',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
