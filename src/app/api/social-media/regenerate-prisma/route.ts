import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

// POST /api/social-media/regenerate-prisma - Regenerar cliente Prisma para PostgreSQL
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 Regenerando cliente Prisma para PostgreSQL...');

    const results = [];

    // Paso 0: Limpiar caché de Next.js
    try {
      console.log('🧹 Limpiando caché de Next.js...');
      const nextCachePath = path.join(process.cwd(), '.next');
      if (fs.existsSync(nextCachePath)) {
        fs.rmSync(nextCachePath, { recursive: true, force: true });
        results.push({
          step: 'Clear Next.js Cache',
          status: 'success',
          message: 'Caché de Next.js eliminada'
        });
      } else {
        results.push({
          step: 'Clear Next.js Cache',
          status: 'success',
          message: 'No había caché para limpiar'
        });
      }
    } catch (error) {
      results.push({
        step: 'Clear Next.js Cache',
        status: 'warning',
        message: 'No se pudo limpiar el caché automáticamente'
      });
    }

    // Paso 1: Generar cliente Prisma
    try {
      console.log('📦 Generando cliente Prisma...');
      const { stdout, stderr } = await execAsync('npx prisma generate', {
        cwd: process.cwd(),
        timeout: 60000 // 60 segundos timeout
      });
      
      results.push({
        step: 'Generate Prisma Client',
        status: 'success',
        message: 'Cliente Prisma regenerado exitosamente',
        output: stdout
      });
      
      if (stderr) {
        console.warn('⚠️ Warnings durante generación:', stderr);
      }
    } catch (error) {
      results.push({
        step: 'Generate Prisma Client',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error generando cliente',
        error: error
      });
    }

    // Paso 2: Verificar configuración de base de datos
    try {
      console.log('🔍 Verificando configuración de base de datos...');
      
      // Verificar que DATABASE_URL esté configurada para PostgreSQL
      const databaseUrl = process.env.DATABASE_URL;
      if (!databaseUrl) {
        throw new Error('DATABASE_URL no está configurada');
      }
      
      if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
        throw new Error('DATABASE_URL debe ser una URL de PostgreSQL (postgresql:// o postgres://)');
      }
      
      results.push({
        step: 'Database URL Validation',
        status: 'success',
        message: 'DATABASE_URL configurada correctamente para PostgreSQL'
      });
    } catch (error) {
      results.push({
        step: 'Database URL Validation',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error validando DATABASE_URL'
      });
    }

    // Paso 3: Aplicar cambios a la base de datos
    try {
      console.log('🔄 Aplicando cambios a la base de datos...');
      const { stdout, stderr } = await execAsync('npx prisma db push', {
        cwd: process.cwd(),
        timeout: 120000 // 2 minutos timeout
      });
      
      results.push({
        step: 'Database Push',
        status: 'success',
        message: 'Esquema aplicado a PostgreSQL exitosamente',
        output: stdout
      });
      
      if (stderr) {
        console.warn('⚠️ Warnings durante db push:', stderr);
      }
    } catch (error) {
      results.push({
        step: 'Database Push',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error aplicando esquema',
        error: error
      });
    }

    const hasErrors = results.some(r => r.status === 'error');
    const allSuccess = results.every(r => r.status === 'success');

    console.log('✅ Regeneración de Prisma completada');

    return NextResponse.json({
      success: !hasErrors,
      allSuccess,
      results,
      summary: {
        total: results.length,
        success: results.filter(r => r.status === 'success').length,
        errors: results.filter(r => r.status === 'error').length
      },
      message: hasErrors 
        ? 'Regeneración completada con algunos errores'
        : 'Cliente Prisma regenerado exitosamente para PostgreSQL',
      instructions: allSuccess ? [
        'El cliente Prisma ha sido regenerado para PostgreSQL',
        'Las tablas han sido creadas/actualizadas en la base de datos',
        'Ahora puedes usar "Check DB" para verificar el estado',
        'Después puedes sincronizar perfiles de upload-post'
      ] : [
        'Revisa los errores reportados',
        'Verifica que PostgreSQL esté ejecutándose',
        'Confirma que DATABASE_URL sea correcta',
        'Intenta nuevamente después de corregir los problemas'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error regenerando Prisma:', error);
    return NextResponse.json({
      success: false,
      error: 'Error regenerando cliente Prisma',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
