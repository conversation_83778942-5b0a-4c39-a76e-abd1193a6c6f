import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-facebook-only - Test solo Facebook
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📘 [TEST FACEBOOK ONLY] Probando solo Facebook...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Test solo Facebook (que sabemos que funciona)
    console.log('📘 Test: Solo Facebook...');
    try {
      const facebookContent = `📘 TEST FACEBOOK FUNCIONAL: Confirmando que Facebook funciona

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Plataforma: Solo Facebook
🏷️ Open Graph: Detección automática
🔗 Enlace: https://diarios.live
📱 Página: La Bang fm

#TestFacebookFuncional #DiarioDelSur`;

      const facebookResult = await uploadPostClient.publishPost({
        text: facebookContent,
        url: 'https://diarios.live',
        platforms: ['facebook'], // Solo Facebook
        profiles: ['jpablovila']
      });

      console.log('📘 Resultado Facebook:', facebookResult);

      const facebookSuccess = facebookResult.success === true;

      return NextResponse.json({
        success: facebookSuccess,
        facebook: {
          working: facebookSuccess,
          result: facebookResult.data,
          postId: facebookResult.data?.id,
          url: undefined // No hay URL en la respuesta
        },
        twitter: {
          working: false,
          issue: 'API Key validation error',
          recommendation: 'Usar solo Facebook por ahora'
        },
        systemStatus: {
          facebookReady: facebookSuccess,
          twitterBlocked: true,
          canPublishNews: facebookSuccess,
          recommendation: facebookSuccess ? 
            'Sistema listo para publicar noticias solo en Facebook' :
            'Sistema necesita configuración'
        },
        nextSteps: facebookSuccess ? [
          '✅ Facebook está funcionando perfectamente',
          '📱 Puedes usar el botón "Publicar en Redes" seleccionando solo Facebook',
          '🐦 Twitter necesita resolución de API key con upload-post.com',
          '📞 Contactar soporte de upload-post para resolver Twitter',
          '🎯 Mientras tanto, usar solo Facebook para publicaciones'
        ] : [
          '❌ Facebook también tiene problemas',
          '🔧 Revisar configuración completa de upload-post',
          '📞 Contactar soporte técnico'
        ],
        content: facebookContent,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error en test Facebook:', error);
      
      return NextResponse.json({
        success: false,
        error: 'Error en test de Facebook',
        details: error instanceof Error ? error.message : 'Error desconocido',
        systemStatus: {
          facebookReady: false,
          twitterBlocked: true,
          canPublishNews: false,
          recommendation: 'Sistema necesita configuración completa'
        },
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [TEST FACEBOOK ONLY] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de Facebook únicamente',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
