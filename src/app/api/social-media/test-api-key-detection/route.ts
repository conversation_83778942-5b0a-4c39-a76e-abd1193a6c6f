import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/test-api-key-detection - Test de detección de API Key
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [API KEY TEST] Probando detección de API Key...');

    // Verificar variables de entorno
    const apiKey = process.env.UPLOAD_POST_API_KEY;
    const hasApiKey = !!apiKey;

    console.log('🔍 Variables de entorno:', {
      hasApiKey,
      apiKeyLength: apiKey?.length || 0,
      apiKeyStart: apiKey?.substring(0, 10) + '...' || 'No definida',
      nodeEnv: process.env.NODE_ENV
    });

    // Verificar todas las variables relacionadas
    const envVars = {
      UPLOAD_POST_API_KEY: {
        exists: !!process.env.UPLOAD_POST_API_KEY,
        length: process.env.UPLOAD_POST_API_KEY?.length || 0,
        preview: process.env.UPLOAD_POST_API_KEY?.substring(0, 10) + '...' || 'No definida'
      },
      NODE_ENV: process.env.NODE_ENV,
      NEXTAUTH_SECRET: {
        exists: !!process.env.NEXTAUTH_SECRET,
        length: process.env.NEXTAUTH_SECRET?.length || 0
      },
      DATABASE_URL: {
        exists: !!process.env.DATABASE_URL,
        length: process.env.DATABASE_URL?.length || 0
      }
    };

    // Test de conexión si hay API Key
    let connectionTest = null;
    if (hasApiKey) {
      try {
        console.log('🔍 Probando conexión con upload-post...');
        
        const response = await fetch('https://api.upload-post.com/api/uploadposts/profiles', {
          method: 'GET',
          headers: {
            'Authorization': `Apikey ${apiKey}`,
          },
        });

        connectionTest = {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          contentType: response.headers.get('content-type'),
          responsePreview: response.ok ? 'Conexión exitosa' : await response.text().then(t => t.substring(0, 200))
        };

        console.log('📡 Resultado de conexión:', connectionTest);

      } catch (error) {
        connectionTest = {
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido'
        };
        console.error('❌ Error en test de conexión:', error);
      }
    }

    // Simular lo que hace el endpoint accounts
    const accountsEndpointSimulation = {
      hasApiKey,
      connectionStatus: connectionTest?.success ? 'connected' : (connectionTest ? 'failed' : 'not_tested'),
      syncStatus: {
        hasApiKey,
        connectionStatus: connectionTest?.success ? 'connected' : (connectionTest ? 'failed' : 'not_tested'),
        lastSync: null,
        profileCount: 0
      }
    };

    return NextResponse.json({
      success: true,
      apiKeyDetection: {
        hasApiKey,
        apiKeyLength: apiKey?.length || 0,
        apiKeyPreview: apiKey?.substring(0, 10) + '...' || 'No definida'
      },
      environmentVariables: envVars,
      connectionTest,
      accountsEndpointSimulation,
      recommendations: [
        hasApiKey ? '✅ API Key detectada correctamente' : '❌ API Key no encontrada',
        connectionTest?.success ? '✅ Conexión con upload-post exitosa' : '⚠️ Problema de conexión con upload-post',
        '🔄 Recarga la página /admin/social-media para ver cambios',
        '🔍 Verifica que el endpoint /api/social-media/accounts devuelva syncStatus'
      ],
      debugInfo: {
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform
      }
    });

  } catch (error) {
    console.error('❌ [API KEY TEST] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de API Key',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
