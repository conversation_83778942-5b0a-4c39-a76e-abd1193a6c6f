import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/test-simple-twitter - Test simple de Twitter
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🐦 [TEST SIMPLE TWITTER] Probando Twitter directamente...');

    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    console.log('🔑 API Key disponible:', apiKey ? 'Sí' : 'No');

    // Test directo a upload-post sin nuestro cliente
    const testContent = `🐦 TEST DIRECTO TWITTER: Probando X directamente

📅 ${new Date().toLocaleString('es-ES', { timeZone: 'America/Argentina/Buenos_Aires' })}

✅ Método: Llamada directa a upload-post
🔗 Enlace: https://diarios.live
📱 Plataforma: x (Twitter)

#TestDirectoTwitter #DiarioDelSur`;

    console.log('📝 Contenido del test:', testContent);

    // Probar diferentes formatos de parámetros
    const testFormats = [
      {
        name: 'Formato 1: platform[0]',
        data: {
          user: 'jpablovila',
          'platform[0]': 'x',
          title: testContent
        }
      },
      {
        name: 'Formato 2: platform[]',
        data: {
          user: 'jpablovila',
          'platform[]': 'x',
          title: testContent
        }
      }
    ];

    const results = [];

    for (const format of testFormats) {
      try {
        console.log(`🧪 Probando: ${format.name}`);
        console.log('📤 Datos:', format.data);

        // Filtrar propiedades undefined
        const cleanData = Object.fromEntries(
          Object.entries(format.data).filter(([_, value]) => value !== undefined)
        );
        const formData = new URLSearchParams(cleanData);
        
        const response = await fetch('https://api.upload-post.com/api/upload_text', {
          method: 'POST',
          headers: {
            'Authorization': `Apikey ${apiKey}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData.toString()
        });

        const responseText = await response.text();
        console.log(`📥 Respuesta ${format.name}:`, responseText);

        let result;
        try {
          result = JSON.parse(responseText);
        } catch {
          result = { rawResponse: responseText };
        }
        
        const isSuccess = response.ok && result.success === true;
        const xResult = result.results?.x;
        
        results.push({
          format: format.name,
          success: isSuccess,
          httpStatus: response.status,
          xSuccess: xResult?.success,
          xError: xResult?.error,
          fullResponse: result,
          data: format.data
        });

        console.log(`${isSuccess ? '✅' : '❌'} ${format.name}: ${isSuccess ? 'ÉXITO' : 'Error'}`);
        
        if (isSuccess) {
          console.log(`🎉 ¡Formato exitoso encontrado: ${format.name}!`);
          break; // Si encontramos uno que funciona, podemos parar
        }
        
      } catch (error) {
        results.push({
          format: format.name,
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido',
          data: format.data
        });
        
        console.log(`❌ ${format.name}: Error - ${error instanceof Error ? error.message : 'Error desconocido'}`);
      }

      // Esperar entre tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    const successfulFormat = results.find(r => r.success);
    const hasSuccess = !!successfulFormat;

    return NextResponse.json({
      success: hasSuccess,
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      },
      apiKeyStatus: {
        available: !!apiKey,
        length: apiKey ? apiKey.length : 0,
        preview: apiKey ? `${apiKey.substring(0, 8)}...` : 'No disponible'
      },
      recommendation: hasSuccess 
        ? `Usar formato: ${successfulFormat.format}`
        : 'Ningún formato funcionó - revisar configuración',
      workingFormat: successfulFormat || null,
      troubleshooting: [
        !apiKey ? '❌ API Key no configurada' : '✅ API Key disponible',
        'Verificar que la cuenta de Twitter esté conectada en upload-post.com',
        'Confirmar que el usuario "jpablovila" tenga acceso a Twitter/X',
        'Revisar límites de la cuenta en upload-post.com'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST SIMPLE TWITTER] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test simple de Twitter',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
