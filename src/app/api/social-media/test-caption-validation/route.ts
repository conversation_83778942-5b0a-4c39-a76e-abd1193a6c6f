import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// POST /api/social-media/test-caption-validation - Test de validación de caption
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [CAPTION VALIDATION] Probando validación de caption...');

    // Simular diferentes casos de datos de publicación
    const testCases = [
      {
        name: 'Datos completos',
        publicacion: {
          noticia: {
            titulo: 'Luna de sangre: cinco rituales para aprovechar la energía del eclipse',
            url: 'https://www.delsurdiario.com/64785-luna-de-sangre'
          },
          titulo: 'Título alternativo',
          url: 'https://alternativo.com'
        }
      },
      {
        name: 'Solo noticia.titulo',
        publicacion: {
          noticia: {
            titulo: 'Noticia con solo título en noticia'
          }
        }
      },
      {
        name: 'Solo titulo principal',
        publicacion: {
          titulo: 'Título principal sin noticia'
        }
      },
      {
        name: 'Datos undefined',
        publicacion: {
          noticia: {
            titulo: undefined,
            url: undefined
          },
          titulo: undefined,
          url: undefined
        }
      },
      {
        name: 'Datos null',
        publicacion: {
          noticia: {
            titulo: null,
            url: null
          },
          titulo: null,
          url: null
        }
      },
      {
        name: 'Sin noticia object',
        publicacion: {
          titulo: 'Solo título principal'
        }
      },
      {
        name: 'Completamente vacío',
        publicacion: {}
      }
    ];

    const results = testCases.map(testCase => {
      const publicacion = testCase.publicacion as any;
      
      // Simular la lógica de validación del frontend
      const titulo = publicacion.noticia?.titulo || publicacion.titulo || 'Sin título';
      const url = publicacion.noticia?.url || publicacion.url || '';
      
      const baseCaption = `📰 ${titulo}`;
      const urlSection = url ? `\n\n${url}` : '';
      const hashtags = '\n\n#Noticias #Actualidad #DiarioDelSur';
      
      const caption = baseCaption + urlSection + hashtags;
      
      // Verificar si contiene "undefined" o "null"
      const hasUndefined = caption.includes('undefined');
      const hasNull = caption.includes('null');
      const isValid = !hasUndefined && !hasNull;
      
      return {
        testCase: testCase.name,
        input: publicacion,
        output: {
          titulo,
          url,
          caption,
          captionLength: caption.length
        },
        validation: {
          hasUndefined,
          hasNull,
          isValid,
          issues: [
            ...(hasUndefined ? ['Contiene "undefined"'] : []),
            ...(hasNull ? ['Contiene "null"'] : [])
          ]
        }
      };
    });

    // Estadísticas
    const validResults = results.filter(r => r.validation.isValid);
    const invalidResults = results.filter(r => !r.validation.isValid);

    return NextResponse.json({
      success: true,
      summary: {
        totalTests: results.length,
        validCaptions: validResults.length,
        invalidCaptions: invalidResults.length,
        allValid: invalidResults.length === 0
      },
      results,
      recommendations: [
        invalidResults.length === 0 ? '✅ Todas las validaciones pasaron' : '❌ Algunas validaciones fallaron',
        '🔍 Verificar que no aparezca "undefined" en los captions',
        '📝 Usar valores por defecto para campos faltantes',
        '✅ Validación implementada correctamente'
      ],
      validationLogic: {
        titulo: 'publicacion.noticia?.titulo || publicacion.titulo || "Sin título"',
        url: 'publicacion.noticia?.url || publicacion.url || ""',
        caption: 'baseCaption + urlSection + hashtags'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [CAPTION VALIDATION] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de validación de caption',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
