import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClient } from '@/lib/social-media/upload-post-client';

// POST /api/social-media/test-real-publish - Test de publicación real en upload-post
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🧪 [TEST REAL PUBLISH] Probando publicación real en upload-post...');

    const results = [];

    // Verificar API key
    const apiKey = process.env.UPLOAD_POST_API_KEY;
    if (!apiKey) {
      throw new Error('UPLOAD_POST_API_KEY no configurada');
    }

    const uploadPostClient = new UploadPostClient(apiKey);

    // Test 1: Conexión básica
    try {
      console.log('🔍 Test 1: Verificando conexión básica...');
      const isConnected = await uploadPostClient.testConnection();
      
      results.push({
        test: 'Basic Connection',
        status: isConnected ? 'success' : 'error',
        message: isConnected ? 'Conexión básica exitosa' : 'Fallo en conexión básica'
      });
    } catch (error) {
      results.push({
        test: 'Basic Connection',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error en conexión básica'
      });
    }

    // Test 2: Obtener perfiles (sabemos que funciona)
    try {
      console.log('🔍 Test 2: Obteniendo perfiles...');
      const profiles = await uploadPostClient.getProfiles();
      
      results.push({
        test: 'Get Profiles',
        status: 'success',
        message: `${profiles.length} perfiles obtenidos`,
        data: profiles.map(p => ({ platform: p.platform, name: p.name }))
      });
    } catch (error) {
      results.push({
        test: 'Get Profiles',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error obteniendo perfiles'
      });
    }

    // Test 3: Probar endpoints correctos según documentación
    const publishEndpoints = [
      '/api/upload_text',  // Endpoint correcto para texto
      '/api/upload_photos', // Endpoint correcto para fotos
      '/api/upload',       // Endpoint correcto para videos
      '/api/posts',        // Endpoint anterior (para comparar)
      '/api/uploadposts'   // Endpoint anterior (para comparar)
    ];

    let publishSuccess = false;
    const publishResults = [];

    // Obtener Page ID real antes de probar endpoints
    let realPageId = null;
    try {
      console.log('🔍 Obteniendo Facebook Page ID real...');
      realPageId = await uploadPostClient.getFacebookPageId('jpablovila');
      console.log('📘 Page ID obtenido:', realPageId);
    } catch (error) {
      console.warn('⚠️ No se pudo obtener Page ID:', error);
    }

    for (const endpoint of publishEndpoints) {
      try {
        console.log(`🔍 Test 3.${publishEndpoints.indexOf(endpoint) + 1}: Probando endpoint ${endpoint}...`);

        // Datos de prueba según documentación oficial
        const testText = "🧪 TEST: Publicación de prueba desde el panel - " + new Date().toISOString();

        // Probar diferentes formatos de Page ID para Facebook
        let formData;

        if (endpoint === '/api/upload_text' && realPageId) {
          // Probar múltiples formatos del Page ID
          const pageIdFormats = [
            { page_id: realPageId },
            { facebook_page_id: realPageId },
            { 'page_id[]': realPageId },
            { 'facebook[page_id]': realPageId }
          ];

          // Usar el primer formato por ahora
          formData = new URLSearchParams({
            user: "jpablovila",
            'platform[]': "facebook",
            title: testText,
            url: "https://diarios.live",
            facebook_page_id: realPageId  // Usar facebook_page_id según documentación
          });

          console.log(`📤 Probando con Page ID: ${realPageId}`);
        } else {
          formData = new URLSearchParams({
            user: "jpablovila",
            'platform[]': "facebook",
            title: testText,
            ...(endpoint === '/api/upload_text' && { url: "https://diarios.live" })
          });
        }

        console.log(`📤 Enviando a ${endpoint}:`, Object.fromEntries(formData));

        // Hacer request con formato correcto
        const response = await fetch(`https://api.upload-post.com${endpoint}`, {
          method: 'POST',
          headers: {
            'Authorization': `Apikey ${apiKey}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData.toString()
        });

        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');

        if (response.ok && isJson) {
          const result = await response.json();
          publishResults.push({
            endpoint,
            status: 'success',
            message: `Endpoint ${endpoint} funciona correctamente`,
            data: result
          });
          publishSuccess = true;
          console.log(`✅ Endpoint ${endpoint} exitoso:`, result);
          break; // Salir del loop si encontramos uno que funciona
        } else {
          const errorText = await response.text();
          publishResults.push({
            endpoint,
            status: 'error',
            message: `${response.status} - ${isJson ? 'JSON' : 'HTML'} response`,
            error: errorText.substring(0, 200) + '...'
          });
        }

      } catch (error) {
        publishResults.push({
          endpoint,
          status: 'error',
          message: error instanceof Error ? error.message : 'Error de conexión',
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    results.push({
      test: 'Publish Endpoints Test',
      status: publishSuccess ? 'success' : 'error',
      message: publishSuccess
        ? `Encontrado endpoint funcional: ${publishResults.find(r => r.status === 'success')?.endpoint}`
        : 'Ningún endpoint de publicación funciona',
      data: publishResults
    });

    // Test 4: Verificar formato de datos requerido
    try {
      console.log('🔍 Test 4: Verificando formato de datos...');

      // Si encontramos un endpoint que funciona, verificar el formato correcto
      const workingEndpoint = publishResults.find(r => r.status === 'success');

      if (workingEndpoint) {
        results.push({
          test: 'Data Format Verification',
          status: 'success',
          message: `Formato correcto identificado para ${workingEndpoint.endpoint}`,
          data: {
            endpoint: workingEndpoint.endpoint,
            requiredFields: ['text', 'usernames', 'platforms'],
            optionalFields: ['url', 'media']
          }
        });
      } else {
        results.push({
          test: 'Data Format Verification',
          status: 'warning',
          message: 'No se pudo verificar formato - ningún endpoint funciona',
          data: {
            testedEndpoints: publishEndpoints,
            suggestion: 'Revisar documentación de upload-post o contactar soporte'
          }
        });
      }

    } catch (error) {
      results.push({
        test: 'Data Format Verification',
        status: 'error',
        message: error instanceof Error ? error.message : 'Error verificando formato'
      });
    }

    const hasErrors = results.some(r => r.status === 'error');
    const hasSuccess = results.some(r => r.status === 'success');

    return NextResponse.json({
      success: hasSuccess,
      hasErrors,
      results,
      summary: {
        total: results.length,
        success: results.filter(r => r.status === 'success').length,
        errors: results.filter(r => r.status === 'error').length,
        info: results.filter(r => r.status === 'info').length
      },
      analysis: {
        canConnect: results.find(r => r.test === 'Basic Connection')?.status === 'success',
        canGetProfiles: results.find(r => r.test === 'Get Profiles')?.status === 'success',
        canPublish: results.find(r => r.test === 'Publish Endpoints Test')?.status === 'success',
        workingEndpoint: Array.isArray(results.find(r => r.test === 'Publish Endpoints Test')?.data)
          ? (results.find(r => r.test === 'Publish Endpoints Test')?.data as any[])?.find((d: any) => d.status === 'success')?.endpoint
          : undefined,
        recommendation: results.find(r => r.test === 'Publish Endpoints Test')?.status === 'success'
          ? 'Endpoint de publicación encontrado - actualizar cliente para usar endpoint correcto'
          : 'Ningún endpoint de publicación funciona - continuar con modo demo o contactar soporte de upload-post'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST REAL PUBLISH] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error en test de publicación real',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
