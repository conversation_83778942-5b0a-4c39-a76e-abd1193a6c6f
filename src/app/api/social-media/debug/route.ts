import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { UploadPostClientDebug } from '@/lib/social-media/upload-post-client-debug';

// GET /api/social-media/debug - Debug de conexión con upload-post
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 [DEBUG] Iniciando debug de upload-post...');

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasApiKey: !!process.env.UPLOAD_POST_API_KEY,
        apiKeyLength: process.env.UPLOAD_POST_API_KEY?.length || 0,
        apiKeyPrefix: process.env.UPLOAD_POST_API_KEY?.substring(0, 20) + '...' || 'No configurada'
      },
      tests: [] as any[]
    };

    // Test 1: Verificar API key
    console.log('🧪 [DEBUG] Test 1: Verificar API key...');
    const test1 = {
      name: 'API Key Configuration',
      status: 'unknown',
      details: {},
      error: null as string | null
    };

    if (!process.env.UPLOAD_POST_API_KEY) {
      test1.status = 'failed';
      test1.error = 'UPLOAD_POST_API_KEY no está configurada en las variables de entorno';
    } else {
      test1.status = 'passed';
      test1.details = {
        length: process.env.UPLOAD_POST_API_KEY.length,
        prefix: process.env.UPLOAD_POST_API_KEY.substring(0, 20) + '...',
        isJWT: process.env.UPLOAD_POST_API_KEY.startsWith('eyJ')
      };
    }
    debugInfo.tests.push(test1);

    // Test 2: Inicializar cliente
    console.log('🧪 [DEBUG] Test 2: Inicializar cliente...');
    const test2 = {
      name: 'Client Initialization',
      status: 'unknown',
      details: {},
      error: null as string | null
    };

    let client = null;
    try {
      client = new UploadPostClientDebug();
      test2.status = 'passed';
      test2.details = { message: 'Cliente inicializado correctamente' };
    } catch (error) {
      test2.status = 'failed';
      test2.error = error instanceof Error ? error.message : 'Error desconocido';
    }
    debugInfo.tests.push(test2);

    // Test 3: Probar conexión
    console.log('🧪 [DEBUG] Test 3: Probar conexión...');
    const test3 = {
      name: 'Connection Test',
      status: 'unknown',
      details: {},
      error: null as string | null
    };

    if (client) {
      try {
        const isConnected = await client.testConnection();
        test3.status = isConnected ? 'passed' : 'failed';
        test3.details = { connected: isConnected };
        if (!isConnected) {
          test3.error = 'La conexión falló - revisar logs del servidor';
        }
      } catch (error) {
        test3.status = 'failed';
        test3.error = error instanceof Error ? error.message : 'Error desconocido';
      }
    } else {
      test3.status = 'skipped';
      test3.error = 'Cliente no inicializado';
    }
    debugInfo.tests.push(test3);

    // Test 4: Obtener perfiles
    console.log('🧪 [DEBUG] Test 4: Obtener perfiles...');
    const test4 = {
      name: 'Get Profiles',
      status: 'unknown',
      details: {},
      error: null as string | null
    };

    if (client && test3.status === 'passed') {
      try {
        const profiles = await client.getProfiles();
        test4.status = 'passed';
        test4.details = {
          profileCount: profiles.length,
          profiles: profiles.map(p => ({
            id: p.id,
            platform: p.platform,
            name: p.name,
            isConnected: p.is_connected
          }))
        };
      } catch (error) {
        test4.status = 'failed';
        test4.error = error instanceof Error ? error.message : 'Error desconocido';
      }
    } else {
      test4.status = 'skipped';
      test4.error = 'Conexión no establecida';
    }
    debugInfo.tests.push(test4);

    // Test 5: Probar múltiples endpoints
    console.log('🧪 [DEBUG] Test 5: Probar múltiples endpoints...');
    const test5 = {
      name: 'Multiple Endpoints Test',
      status: 'unknown',
      details: {},
      error: null as string | null
    };

    if (client) {
      try {
        const endpointResults = await client.testMultipleEndpoints();
        test5.status = 'passed';
        test5.details = { endpointResults };
        console.log('🔍 [DEBUG] Resultados de endpoints:', endpointResults);
      } catch (error) {
        test5.status = 'failed';
        test5.error = error instanceof Error ? error.message : 'Error desconocido';
      }
    } else {
      test5.status = 'skipped';
      test5.error = 'Cliente no inicializado';
    }
    debugInfo.tests.push(test5);

    // Resumen
    const passedTests = debugInfo.tests.filter(t => t.status === 'passed').length;
    const failedTests = debugInfo.tests.filter(t => t.status === 'failed').length;
    const skippedTests = debugInfo.tests.filter(t => t.status === 'skipped').length;

    const summary = {
      total: debugInfo.tests.length,
      passed: passedTests,
      failed: failedTests,
      skipped: skippedTests,
      overallStatus: failedTests === 0 ? 'success' : 'failed'
    };

    console.log('✅ [DEBUG] Debug completado:', summary);

    return NextResponse.json({
      success: true,
      message: 'Debug de upload-post completado',
      summary,
      debugInfo,
      recommendations: generateRecommendations(debugInfo.tests)
    });

  } catch (error) {
    console.error('❌ [DEBUG] Error en debug:', error);
    return NextResponse.json({
      success: false,
      error: 'Error ejecutando debug',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

function generateRecommendations(tests: any[]): string[] {
  const recommendations = [];

  const apiKeyTest = tests.find(t => t.name === 'API Key Configuration');
  if (apiKeyTest?.status === 'failed') {
    recommendations.push('Configura UPLOAD_POST_API_KEY en tu archivo .env.local');
    recommendations.push('Obtén tu API key desde https://app.upload-post.com');
  }

  const connectionTest = tests.find(t => t.name === 'Connection Test');
  if (connectionTest?.status === 'failed') {
    if (connectionTest.error?.includes('HTML')) {
      recommendations.push('🚨 PROBLEMA: La API devuelve HTML en lugar de JSON');
      recommendations.push('Esto indica un problema de autenticación o URL incorrecta');
      recommendations.push('Verifica que tu API key sea válida en upload-post.com');
      recommendations.push('Verifica que tu cuenta esté activa y autorizada');
    } else {
      recommendations.push('Verifica tu conexión a internet');
      recommendations.push('Verifica que la API key sea válida');
      recommendations.push('Verifica que tu cuenta de upload-post esté activa');
    }
  }

  const profilesTest = tests.find(t => t.name === 'Get Profiles');
  if (profilesTest?.status === 'failed') {
    recommendations.push('Conecta tus cuentas de Facebook/Twitter en upload-post.com');
    recommendations.push('Verifica que las cuentas estén activas y autorizadas');
  }

  if (recommendations.length === 0) {
    recommendations.push('¡Todo está funcionando correctamente!');
    recommendations.push('Puedes proceder a sincronizar tus perfiles');
  }

  return recommendations;
}
