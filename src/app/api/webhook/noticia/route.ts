import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { NotificationService } from '@/lib/notification-service';
import crypto from 'crypto';

// Interfaz para los datos esperados del webhook
interface WebhookNoticiaData {
  titulo: string;
  contenido: string;
  periodista: string;
  volanta?: string;
  subtitulo?: string;
  resumen?: string;
  autor?: string;
  fuente?: string;
  urlFuente?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  categoria?: string; // Nombre de la categoría
  destacada?: boolean;
  webhookToken?: string; // Token de autenticación
}

// Token de autenticación para el webhook (REQUERIDO en variables de entorno)
const WEBHOOK_TOKEN = process.env.WEBHOOK_TOKEN;

// Validar que el token de webhook esté configurado
if (!WEBHOOK_TOKEN) {
  throw new Error(
    'WEBHOOK_TOKEN environment variable is required for webhook security. ' +
    'Generate a secure random token and set it in your environment variables.'
  );
}

// POST /api/webhook/noticia - Recibir noticia desde WhatsApp
export async function POST(request: NextRequest) {
  try {
    console.log('📱 Webhook recibido - Nueva noticia desde WhatsApp');

    // Obtener datos del body
    const body: WebhookNoticiaData = await request.json();
    console.log('📝 Datos recibidos:', {
      titulo: body.titulo,
      periodista: body.periodista,
      categoria: body.categoria,
      tieneContenido: !!body.contenido,
      tieneResumen: !!body.resumen
    });

    // Validar token de autenticación
    const authHeader = request.headers.get('authorization');
    const tokenFromHeader = authHeader?.replace('Bearer ', '');
    const tokenFromBody = body.webhookToken;

    if (!tokenFromHeader && !tokenFromBody) {
      console.log('❌ Token de autenticación faltante');
      return NextResponse.json(
        { error: 'Token de autenticación requerido' },
        { status: 401 }
      );
    }

    const providedToken = tokenFromHeader || tokenFromBody;

    // Usar comparación segura para evitar timing attacks
    if (!providedToken || !WEBHOOK_TOKEN || !secureCompare(providedToken, WEBHOOK_TOKEN)) {
      console.log('❌ Token de autenticación inválido');

      // Agregar delay para prevenir ataques de fuerza bruta
      await new Promise(resolve => setTimeout(resolve, 1000));

      return NextResponse.json(
        { error: 'Token de autenticación inválido' },
        { status: 401 }
      );
    }

    // Validar campos requeridos (solo titulo y periodista son obligatorios)
    if (!body.titulo || !body.periodista) {
      console.log('❌ Campos requeridos faltantes');
      return NextResponse.json(
        {
          error: 'Campos requeridos faltantes',
          required: ['titulo', 'periodista']
        },
        { status: 400 }
      );
    }

    // Generar contenido automático si no se proporciona
    const contenidoFinal = body.contenido || `Esta noticia fue recibida desde WhatsApp con el título: "${body.titulo}". Reportada por ${body.periodista}. Se requiere completar el contenido de la noticia.`;

    // Generar resumen automático si no se proporciona
    const resumenFinal = body.resumen || `Noticia reportada por ${body.periodista}${body.categoria ? ` en la categoría ${body.categoria}` : ''}. Recibida desde WhatsApp, pendiente de desarrollo completo.`;

    // Buscar o crear usuario del sistema para asignar la noticia
    let webhookUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!webhookUser) {
      console.log('📝 Creando usuario webhook del sistema...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('webhook-system-2025', 10);

      webhookUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Sistema WhatsApp',
          password: hashedPassword,
          role: 'EDITOR',
          isActive: true,
        }
      });
    }

    // Buscar categoría por nombre o usar categoría por defecto
    let categoriaId: number | null = null;

    if (body.categoria) {
      const categoria = await prisma.categoria.findFirst({
        where: {
          nombre: {
            contains: body.categoria
          },
          isActive: true
        }
      });
      categoriaId = categoria?.id || null;
    }

    // Si no se encontró categoría, usar la primera activa como defecto
    if (!categoriaId) {
      const categoriaDefecto = await prisma.categoria.findFirst({
        where: { isActive: true },
        orderBy: { orden: 'asc' }
      });
      categoriaId = categoriaDefecto?.id || null;
    }

    // Preparar datos para crear la noticia con nuevos campos
    const webhookMetadata = {
      receivedAt: new Date().toISOString(),
      source: 'WhatsApp',
      originalData: body
    };

    const noticiaData = {
      titulo: body.titulo,
      subtitulo: body.subtitulo || null,
      volanta: body.volanta || null,
      contenido: contenidoFinal,
      resumen: resumenFinal,
      imagenUrl: body.imagenUrl || null,
      imagenAlt: body.imagenAlt || null,
      autor: body.autor || body.periodista,
      fuente: body.fuente || 'WhatsApp',
      urlFuente: body.urlFuente || null,
      estado: 'EN_REVISION' as const, // Estado automático para revisión
      destacada: body.destacada || false,
      publicada: false,

      // Campos específicos del webhook
      periodista: body.periodista,
      origen: 'WEBHOOK' as const,
      webhookData: webhookMetadata as any, // Usar any para evitar problemas de tipo

      // Relaciones
      categoriaId: categoriaId,
      userId: webhookUser.id,
    };

    console.log('💾 Creando noticia en base de datos...');

    // Crear la noticia
    const noticia = await prisma.noticia.create({
      data: noticiaData,
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log(`✅ Noticia creada exitosamente: ID ${noticia.id}`);

    // Crear notificaciones para usuarios ADMIN y EDITOR
    await NotificationService.createWebhookNotification(
      noticia.id,
      body.periodista,
      noticia.titulo
    );

    console.log(`📢 Notificaciones creadas para la noticia ID ${noticia.id}`);

    // Respuesta exitosa
    return NextResponse.json({
      success: true,
      message: 'Noticia recibida y procesada exitosamente desde WhatsApp',
      data: {
        id: noticia.id,
        titulo: noticia.titulo,
        periodista: body.periodista,
        estado: noticia.estado,
        categoria: noticia.categoria?.nombre || 'Sin categoría',
        createdAt: noticia.createdAt,
        webhookSource: 'WhatsApp'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('❌ Error al procesar webhook de noticia:', error);
    
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        message: 'No se pudo procesar la noticia recibida',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// GET /api/webhook/noticia - Información del webhook (para testing)
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/webhook/noticia',
    method: 'POST',
    description: 'Endpoint para recibir noticias desde WhatsApp',
    authentication: 'Bearer token o webhookToken en body',
    requiredFields: ['titulo', 'periodista'],
    optionalFields: [
      'volanta', 'subtitulo', 'resumen', 'autor', 'fuente',
      'urlFuente', 'imagenUrl', 'imagenAlt', 'categoria', 'destacada'
    ],
    example: {
      titulo: 'Título de la noticia',
      contenido: 'Contenido completo de la noticia... (opcional, se genera automáticamente si no se proporciona)',
      resumen: 'Resumen de la noticia... (opcional, se genera automáticamente si no se proporciona)',
      periodista: 'Juan Pérez',
      volanta: 'Volanta opcional',
      categoria: 'Política',
      webhookToken: 'your-secure-webhook-token'
    }
  });
}

/**
 * Comparación segura de tokens para prevenir timing attacks
 */
function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  return crypto.timingSafeEqual(
    Buffer.from(a, 'utf8'),
    Buffer.from(b, 'utf8')
  );
}
