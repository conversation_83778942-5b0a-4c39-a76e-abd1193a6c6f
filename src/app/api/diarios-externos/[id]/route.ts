import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { DiarioExternoInput } from '@/types/external-publication';

// GET /api/diarios-externos/[id] - Obtener diario específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const diario = await prisma.diarioExterno.findUnique({
      where: { id },
      include: {
        categoriaMapeos: {
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          },
          orderBy: {
            categoriaLocal: {
              nombre: 'asc'
            }
          }
        },
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: diario
    });
  } catch (error) {
    console.error('Error fetching diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/diarios-externos/[id] - Actualizar diario específico
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body: DiarioExternoInput = await request.json();

    // Validaciones
    if (!body.nombre || !body.urlBase || !body.bearerToken || !body.categoriaImagenId) {
      return NextResponse.json(
        { error: 'Faltan campos obligatorios' },
        { status: 400 }
      );
    }

    // Validar URL
    try {
      new URL(body.urlBase);
    } catch {
      return NextResponse.json(
        { error: 'URL base inválida' },
        { status: 400 }
      );
    }

    // Verificar que el diario existe
    const diarioExistente = await prisma.diarioExterno.findUnique({
      where: { id }
    });

    if (!diarioExistente) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Verificar que no exista otro diario con el mismo nombre
    const diarioConMismoNombre = await prisma.diarioExterno.findFirst({
      where: { 
        nombre: body.nombre,
        id: { not: id }
      }
    });

    if (diarioConMismoNombre) {
      return NextResponse.json(
        { error: 'Ya existe otro diario con ese nombre' },
        { status: 409 }
      );
    }

    const diarioActualizado = await prisma.diarioExterno.update({
      where: { id },
      data: {
        nombre: body.nombre,
        urlBase: body.urlBase.replace(/\/$/, ''), // Remover slash final
        bearerToken: body.bearerToken,
        categoriaImagenId: body.categoriaImagenId,
        activo: body.activo ?? true,
        updatedAt: new Date(),
      },
      include: {
        categoriaMapeos: {
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          }
        },
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: diarioActualizado,
      message: 'Diario externo actualizado exitosamente'
    });
  } catch (error) {
    console.error('Error updating diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/diarios-externos/[id] - Eliminar diario específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            publicacionesExternas: true
          }
        }
      }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Verificar que no tenga publicaciones asociadas
    if (diario._count.publicacionesExternas > 0) {
      return NextResponse.json({
        error: 'No se puede eliminar un diario con publicaciones asociadas',
        details: `El diario tiene ${diario._count.publicacionesExternas} publicación(es) asociada(s)`
      }, { status: 409 });
    }

    await prisma.diarioExterno.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Diario externo eliminado exitosamente'
    });
  } catch (error) {
    console.error('Error deleting diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
