import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createExternalPublicationService } from '@/lib/external-publication-service';

// GET /api/diarios-externos/[id]/categorias - Obtener categorías del diario externo
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get('parent_id');

    // Obtener el diario externo
    const diario = await prisma.diarioExterno.findUnique({
      where: { id }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Crear servicio y obtener categorías
    const service = createExternalPublicationService({
      ...diario,
      descripcion: diario.descripcion || undefined,
      configuracion: diario.configuracion || undefined
    });
    const result = await service.fetchCategories(parentId ? parseInt(parentId) : undefined);

    if (!result.success) {
      return NextResponse.json({
        error: 'Error al obtener categorías del diario externo',
        details: result.error
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: 'Categorías obtenidas exitosamente'
    });
  } catch (error) {
    console.error('Error fetching external categories:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// POST /api/diarios-externos/[id]/categorias/import - Importar categorías masivamente
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const diarioExternoId = parseInt(idParam);

    if (isNaN(diarioExternoId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const { mapeos }: { mapeos: Array<{ categoriaLocalId: number; categoriaExternaId: number }> } = await request.json();

    if (!Array.isArray(mapeos)) {
      return NextResponse.json(
        { error: 'Formato de datos inválido' },
        { status: 400 }
      );
    }

    // Obtener el diario externo
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Obtener categorías externas para validar IDs
    const service = createExternalPublicationService({
      ...diario,
      descripcion: diario.descripcion || undefined,
      configuracion: diario.configuracion || undefined
    });
    const categoriasResult = await service.fetchCategories();

    if (!categoriasResult.success) {
      return NextResponse.json({
        error: 'Error al validar categorías externas',
        details: categoriasResult.error
      }, { status: 500 });
    }

    const categoriasExternas = categoriasResult.data || [];
    const idsExternasValidas = new Set(categoriasExternas.map(c => c.id));

    // Validar que todos los IDs externos existen
    const idsInvalidas = mapeos
      .map(m => m.categoriaExternaId)
      .filter(id => !idsExternasValidas.has(id));

    if (idsInvalidas.length > 0) {
      return NextResponse.json({
        error: 'IDs de categorías externas inválidas',
        details: `Los siguientes IDs no existen: ${idsInvalidas.join(', ')}`
      }, { status: 400 });
    }

    // Obtener categorías locales para validar
    const categoriasLocales = await prisma.categoria.findMany({
      where: {
        id: { in: mapeos.map(m => m.categoriaLocalId) }
      }
    });

    if (categoriasLocales.length !== mapeos.length) {
      return NextResponse.json({
        error: 'Algunas categorías locales no existen'
      }, { status: 400 });
    }

    // Usar transacción para crear mapeos
    const resultado = await prisma.$transaction(async (tx) => {
      // Eliminar mapeos existentes para evitar duplicados
      await tx.categoriaMapeo.deleteMany({
        where: {
          diarioExternoId,
          categoriaLocalId: { in: mapeos.map(m => m.categoriaLocalId) }
        }
      });

      // Crear nuevos mapeos
      const nuevosMapeos = await Promise.all(
        mapeos.map(mapeo =>
          tx.categoriaMapeo.create({
            data: {
              diarioExternoId,
              categoriaLocalId: mapeo.categoriaLocalId,
              categoriaExternaId: mapeo.categoriaExternaId,
            },
            include: {
              categoriaLocal: {
                select: {
                  id: true,
                  nombre: true,
                  color: true,
                }
              }
            }
          })
        )
      );

      return nuevosMapeos;
    });

    return NextResponse.json({
      success: true,
      data: {
        imported: resultado.length,
        mapeos: resultado
      },
      message: `${resultado.length} mapeo(s) importado(s) exitosamente`
    });
  } catch (error) {
    console.error('Error importing categories:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
