import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createExternalPublicationService } from '@/lib/external-publication-service';

// POST /api/diarios-externos/[id]/test-connection - Probar conexión con diario externo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Obtener el diario externo
    const diario = await prisma.diarioExterno.findUnique({
      where: { id }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Crear servicio y probar conexión
    const service = createExternalPublicationService({
      ...diario,
      descripcion: diario.descripcion || undefined,
      configuracion: diario.configuracion || undefined
    });
    const testResult = await service.testConnection();

    // Actualizar el estado del diario basado en el resultado
    await prisma.diarioExterno.update({
      where: { id },
      data: {
        activo: testResult.success,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: testResult,
      message: testResult.success 
        ? 'Conexión exitosa' 
        : 'Error en la conexión'
    });
  } catch (error) {
    console.error('Error testing connection:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// GET /api/diarios-externos/[id]/test-connection - Obtener estado de conexión
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Obtener el diario externo
    const diario = await prisma.diarioExterno.findUnique({
      where: { id },
      select: {
        id: true,
        nombre: true,
        activo: true,
        updatedAt: true,
        _count: {
          select: {
            publicacionesExternas: {
              where: {
                estado: 'EXITOSO',
                createdAt: {
                  gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
                }
              }
            }
          }
        }
      }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        id: diario.id,
        nombre: diario.nombre,
        activo: diario.activo,
        ultimaVerificacion: diario.updatedAt,
        publicacionesRecientes: diario._count.publicacionesExternas,
        estado: diario.activo ? 'conectado' : 'desconectado'
      }
    });
  } catch (error) {
    console.error('Error getting connection status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
