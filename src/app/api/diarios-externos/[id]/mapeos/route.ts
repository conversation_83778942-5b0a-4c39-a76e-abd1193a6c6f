import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { CategoriaMapeoInput } from '@/types/external-publication';

// GET /api/diarios-externos/[id]/mapeos - Obtener mapeos de categorías
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const diarioExternoId = parseInt(idParam);

    if (isNaN(diarioExternoId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    const mapeos = await prisma.categoriaMapeo.findMany({
      where: { diarioExternoId },
      include: {
        categoriaLocal: {
          select: {
            id: true,
            nombre: true,
            color: true,
          }
        }
      },
      orderBy: {
        categoriaLocal: {
          nombre: 'asc'
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: mapeos
    });
  } catch (error) {
    console.error('Error fetching mapeos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/diarios-externos/[id]/mapeos - Crear nuevo mapeo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const diarioExternoId = parseInt(idParam);

    if (isNaN(diarioExternoId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body: Omit<CategoriaMapeoInput, 'diarioExternoId'> = await request.json();

    // Validaciones
    if (!body.categoriaLocalId || !body.categoriaExternaId) {
      return NextResponse.json(
        { error: 'Faltan campos obligatorios' },
        { status: 400 }
      );
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Verificar que la categoría local existe
    const categoriaLocal = await prisma.categoria.findUnique({
      where: { id: body.categoriaLocalId }
    });

    if (!categoriaLocal) {
      return NextResponse.json({ error: 'Categoría local no encontrada' }, { status: 404 });
    }

    // Verificar que no exista ya un mapeo para esta combinación
    const mapeoExistente = await prisma.categoriaMapeo.findFirst({
      where: {
        diarioExternoId,
        categoriaLocalId: body.categoriaLocalId
      }
    });

    if (mapeoExistente) {
      return NextResponse.json(
        { error: 'Ya existe un mapeo para esta categoría local en este diario' },
        { status: 409 }
      );
    }

    const nuevoMapeo = await prisma.categoriaMapeo.create({
      data: {
        diarioExternoId,
        categoriaLocalId: body.categoriaLocalId,
        categoriaExternaId: body.categoriaExternaId,
      },
      include: {
        categoriaLocal: {
          select: {
            id: true,
            nombre: true,
            color: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: nuevoMapeo,
      message: 'Mapeo creado exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating mapeo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/diarios-externos/[id]/mapeos - Actualizar múltiples mapeos
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const diarioExternoId = parseInt(idParam);

    if (isNaN(diarioExternoId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const { mapeos }: { mapeos: Array<{ categoriaLocalId: number; categoriaExternaId: number }> } = await request.json();

    if (!Array.isArray(mapeos)) {
      return NextResponse.json(
        { error: 'Formato de datos inválido' },
        { status: 400 }
      );
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Usar transacción para actualizar todos los mapeos
    const resultado = await prisma.$transaction(async (tx) => {
      // Eliminar mapeos existentes
      await tx.categoriaMapeo.deleteMany({
        where: { diarioExternoId }
      });

      // Crear nuevos mapeos
      const nuevosMapeos = await Promise.all(
        mapeos.map(mapeo =>
          tx.categoriaMapeo.create({
            data: {
              diarioExternoId,
              categoriaLocalId: mapeo.categoriaLocalId,
              categoriaExternaId: mapeo.categoriaExternaId,
            },
            include: {
              categoriaLocal: {
                select: {
                  id: true,
                  nombre: true,
                  color: true,
                }
              }
            }
          })
        )
      );

      return nuevosMapeos;
    });

    return NextResponse.json({
      success: true,
      data: resultado,
      message: `${resultado.length} mapeo(s) actualizado(s) exitosamente`
    });
  } catch (error) {
    console.error('Error updating mapeos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/diarios-externos/[id]/mapeos - Eliminar todos los mapeos
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { id: idParam } = await params;
    const diarioExternoId = parseInt(idParam);

    if (isNaN(diarioExternoId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const { count } = await prisma.categoriaMapeo.deleteMany({
      where: { diarioExternoId }
    });

    return NextResponse.json({
      success: true,
      message: `${count} mapeo(s) eliminado(s) exitosamente`
    });
  } catch (error) {
    console.error('Error deleting mapeos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
