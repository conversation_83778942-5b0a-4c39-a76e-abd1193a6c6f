import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { DiarioExternoInput } from '@/types/external-publication';

// GET /api/diarios-externos - Obtener todos los diarios externos
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const activo = searchParams.get('activo');
    const includeMapeos = searchParams.get('includeMapeos') === 'true';

    const whereClause = activo !== null ? { activo: activo === 'true' } : {};

    const diarios = await prisma.diarioExterno.findMany({
      where: whereClause,
      select: {
        id: true,
        nombre: true,
        urlBase: true,
        descripcion: true,
        activo: true,
        createdAt: true,
        updatedAt: true,
        // EXCLUIR bearer_token por seguridad
        categoriaMapeos: includeMapeos ? {
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          }
        } : false,
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      },
      orderBy: {
        nombre: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      data: diarios
    });
  } catch (error) {
    console.error('Error fetching diarios externos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/diarios-externos - Crear nuevo diario externo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const body: DiarioExternoInput = await request.json();

    // Validaciones
    if (!body.nombre || !body.urlBase || !body.bearerToken || !body.categoriaImagenId) {
      return NextResponse.json(
        { error: 'Faltan campos obligatorios' },
        { status: 400 }
      );
    }

    // Validar URL
    try {
      new URL(body.urlBase);
    } catch {
      return NextResponse.json(
        { error: 'URL base inválida' },
        { status: 400 }
      );
    }

    // Verificar que no exista un diario con el mismo nombre
    const existingDiario = await prisma.diarioExterno.findFirst({
      where: { nombre: body.nombre }
    });

    if (existingDiario) {
      return NextResponse.json(
        { error: 'Ya existe un diario con ese nombre' },
        { status: 409 }
      );
    }

    const nuevoDiario = await prisma.diarioExterno.create({
      data: {
        nombre: body.nombre,
        urlBase: body.urlBase.replace(/\/$/, ''), // Remover slash final
        bearerToken: body.bearerToken,
        categoriaImagenId: body.categoriaImagenId,
        activo: body.activo ?? true,
      },
      select: {
        id: true,
        nombre: true,
        urlBase: true,
        descripcion: true,
        activo: true,
        createdAt: true,
        updatedAt: true,
        // EXCLUIR bearer_token por seguridad
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: nuevoDiario,
      message: 'Diario externo creado exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/diarios-externos - Actualizar múltiples diarios (para activar/desactivar)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { ids, activo } = await request.json();

    if (!Array.isArray(ids) || typeof activo !== 'boolean') {
      return NextResponse.json(
        { error: 'Datos inválidos' },
        { status: 400 }
      );
    }

    await prisma.diarioExterno.updateMany({
      where: {
        id: { in: ids }
      },
      data: {
        activo: activo,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: `${ids.length} diario(s) ${activo ? 'activado(s)' : 'desactivado(s)'} exitosamente`
    });
  } catch (error) {
    console.error('Error updating diarios externos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/diarios-externos - Eliminar múltiples diarios
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar permisos de administrador
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { role: true }
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    const { ids } = await request.json();

    if (!Array.isArray(ids)) {
      return NextResponse.json(
        { error: 'IDs inválidos' },
        { status: 400 }
      );
    }

    // Verificar que no tengan publicaciones asociadas
    const diariosConPublicaciones = await prisma.diarioExterno.findMany({
      where: {
        id: { in: ids },
        publicacionesExternas: {
          some: {}
        }
      },
      select: { id: true, nombre: true }
    });

    if (diariosConPublicaciones.length > 0) {
      return NextResponse.json({
        error: 'No se pueden eliminar diarios con publicaciones asociadas',
        details: diariosConPublicaciones.map(d => d.nombre)
      }, { status: 409 });
    }

    await prisma.diarioExterno.deleteMany({
      where: {
        id: { in: ids }
      }
    });

    return NextResponse.json({
      success: true,
      message: `${ids.length} diario(s) eliminado(s) exitosamente`
    });
  } catch (error) {
    console.error('Error deleting diarios externos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
