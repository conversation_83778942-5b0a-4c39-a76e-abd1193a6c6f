import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcrypt';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    console.log('🔐 Test Auth - Email:', email);
    
    if (!email || !password) {
      return NextResponse.json({ 
        success: false, 
        error: 'Email y contraseña requeridos' 
      }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      console.log('❌ Usuario no encontrado:', email);
      return NextResponse.json({ 
        success: false, 
        error: 'Usuario no encontrado' 
      }, { status: 404 });
    }

    console.log('✅ Usuario encontrado:', user.email, 'Activo:', user.isActive);

    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('🔑 Contraseña válida:', isPasswordValid);

    if (!isPasswordValid) {
      return NextResponse.json({ 
        success: false, 
        error: 'Contraseña incorrecta' 
      }, { status: 401 });
    }

    if (!user.isActive) {
      return NextResponse.json({ 
        success: false, 
        error: 'Usuario inactivo' 
      }, { status: 401 });
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive
      }
    });

  } catch (error) {
    console.error('❌ Error en test auth:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}
