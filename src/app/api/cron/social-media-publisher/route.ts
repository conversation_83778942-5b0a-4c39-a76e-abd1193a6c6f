import { NextRequest, NextResponse } from 'next/server';
import { SocialMediaService } from '@/lib/social-media/social-media-service';

// POST /api/cron/social-media-publisher - Procesar publicaciones programadas
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Verificar que la llamada viene de Vercel Cron o es local
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.warn('⚠️ Intento de acceso no autorizado al cron job');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('⚡ Iniciando procesamiento de publicaciones programadas...');

    const socialMediaService = new SocialMediaService();
    await socialMediaService.processScheduledPublications();

    const executionTime = Date.now() - startTime;
    console.log(`✅ Cron job completado en ${executionTime}ms`);

    return NextResponse.json({
      success: true,
      message: 'Publicaciones programadas procesadas exitosamente',
      executionTime,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error('❌ Error en cron job de publicaciones:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
      executionTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET /api/cron/social-media-publisher - Estado del cron job (para debugging)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      // Obtener estadísticas del sistema
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      const [
        scheduledCount,
        pendingCount,
        todayPublished,
        recentErrors
      ] = await Promise.all([
        prisma.socialMediaPublication.count({
          where: { status: 'SCHEDULED' }
        }),
        prisma.socialMediaPublication.count({
          where: { status: 'PENDING' }
        }),
        prisma.socialMediaPublication.count({
          where: {
            status: 'SUCCESS',
            publishedAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        }),
        prisma.socialMediaPublication.findMany({
          where: {
            status: 'ERROR',
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24 horas
            }
          },
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            platform: true,
            errorMessage: true,
            retryCount: true,
            createdAt: true
          }
        })
      ]);

      return NextResponse.json({
        success: true,
        status: {
          scheduled: scheduledCount,
          pending: pendingCount,
          publishedToday: todayPublished,
          recentErrors: recentErrors.length,
          errors: recentErrors
        },
        timestamp: new Date().toISOString(),
        nextExecution: 'Cada minuto'
      });
    }

    if (action === 'test') {
      // Ejecutar una prueba del procesamiento
      console.log('🧪 Ejecutando prueba del cron job...');
      
      const socialMediaService = new SocialMediaService();
      await socialMediaService.processScheduledPublications();

      return NextResponse.json({
        success: true,
        message: 'Prueba del cron job ejecutada exitosamente',
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Cron job de publicaciones en redes sociales',
      endpoints: {
        status: '?action=status',
        test: '?action=test'
      },
      schedule: 'Cada minuto',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error obteniendo estado del cron job:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
