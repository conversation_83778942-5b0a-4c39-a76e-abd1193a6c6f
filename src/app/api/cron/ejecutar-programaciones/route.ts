import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { executeExternalPublication } from '@/lib/external-publication-cron';

// POST - Endpoint de cron job para ejecutar programaciones pendientes automáticamente
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 CRON: Iniciando verificación de programaciones pendientes...');

    // Verificar si es una llamada de cron job válida
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'dev-secret-key-2025';

    if (authHeader !== `Bearer ${cronSecret}`) {
      console.log('🚫 CRON: Acceso no autorizado');
      console.log('🔍 CRON: Expected:', `Bearer ${cronSecret}`);
      console.log('🔍 CRON: Received:', authHeader);
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener programaciones pendientes que ya deberían ejecutarse
    const ahora = new Date();
    console.log(`🕐 CRON: Verificando programaciones hasta: ${ahora.toISOString()}`);

    const programacionesPendientes = await prisma.programacionPublicacion.findMany({
      where: {
        estado: 'PENDIENTE',
        fechaPublicacion: {
          lte: ahora
        }
      },
      include: {
        noticia: true,
        version: {
          include: {
            diario: true
          }
        },
        diarioExterno: {
          include: {
            categoriaMapeos: true
          }
        }
      },
      orderBy: {
        fechaPublicacion: 'asc'
      }
    });

    console.log(`📋 CRON: Encontradas ${programacionesPendientes.length} programaciones pendientes`);

    if (programacionesPendientes.length === 0) {
      return NextResponse.json({
        message: 'No hay programaciones pendientes',
        ejecutadas: 0,
        timestamp: ahora.toISOString()
      });
    }

    const resultados = [];

    // Ejecutar cada programación
    for (const programacion of programacionesPendientes) {
      try {
        console.log(`🚀 CRON: Ejecutando programación ${programacion.id} - ${programacion.tipo}`);

        // Marcar como ejecutando
        await prisma.programacionPublicacion.update({
          where: { id: programacion.id },
          data: { estado: 'EJECUTANDO' }
        });

        if (programacion.tipo === 'ORIGINAL') {
          // Publicar noticia original
          await prisma.noticia.update({
            where: { id: programacion.noticiaId },
            data: {
              publicada: true,
              fechaPublicacion: new Date()
            }
          });

          // Si hay un diario externo configurado, también publicar externamente
          if (programacion.diarioExternoId && programacion.diarioExterno) {
            console.log(`🌐 CRON: Publicando noticia original ${programacion.noticiaId} a diario externo ${programacion.diarioExterno.nombre}`);

            const resultadoPublicacion = await ejecutarPublicacionExternaDirecta(
              programacion.noticiaId,
              programacion.diarioExterno.id,
              null // Sin versionId para noticia original
            );

            if (resultadoPublicacion.success) {
              // Marcar como completada con metadatos de publicación externa
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'COMPLETADA',
                  ejecutadoEn: new Date(),
                  metadatos: JSON.stringify({
                    publicadoEn: 'externo',
                    urlPublicacion: resultadoPublicacion.urlPublicacion
                  })
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'ORIGINAL_EXTERNA',
                titulo: programacion.noticia.titulo,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'COMPLETADA',
                urlPublicacion: resultadoPublicacion.urlPublicacion
              });

              console.log(`✅ CRON: Programación ${programacion.id} (ORIGINAL_EXTERNA) ejecutada exitosamente`);
            } else {
              // Marcar como error
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'ERROR',
                  ejecutadoEn: new Date(),
                  errorMensaje: resultadoPublicacion.error
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'ORIGINAL_EXTERNA',
                titulo: programacion.noticia.titulo,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'ERROR',
                error: resultadoPublicacion.error
              });

              console.log(`❌ CRON: Programación ${programacion.id} (ORIGINAL_EXTERNA) falló: ${resultadoPublicacion.error}`);
            }
          } else {
            // Solo publicación local (sin diario externo)
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'COMPLETADA',
                ejecutadoEn: new Date()
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'ORIGINAL',
              titulo: programacion.noticia.titulo,
              estado: 'COMPLETADA'
            });

            console.log(`✅ CRON: Programación ${programacion.id} (ORIGINAL) ejecutada exitosamente`);
          }

        } else if (programacion.tipo === 'VERSION' && programacion.version) {
          // Para versiones, necesitamos determinar si se publica localmente o externamente
          // Si hay un diario externo configurado, publicar externamente
          // Si no, publicar localmente

          if (programacion.diarioExternoId && programacion.diarioExterno) {
            // Publicar versión a diario externo
            console.log(`🌐 CRON: Publicando versión ${programacion.versionId} a diario externo ${programacion.diarioExterno.nombre}`);

            const resultadoPublicacion = await ejecutarPublicacionExternaDirecta(
              programacion.noticiaId,
              programacion.diarioExterno.id,
              programacion.versionId
            );

            if (resultadoPublicacion.success) {
              // Marcar versión como publicada externamente
              await prisma.versionNoticia.update({
                where: { id: programacion.versionId! },
                data: {
                  estadoPublicacion: 'PUBLICADA',
                  urlPublicacion: resultadoPublicacion.urlPublicacion || `${process.env.NEXT_PUBLIC_BASE_URL}/versiones/${programacion.versionId}`
                }
              });

              // Marcar programación como completada
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'COMPLETADA',
                  ejecutadoEn: new Date(),
                  metadatos: JSON.stringify({
                    ...(programacion.metadatos ? JSON.parse(programacion.metadatos as string) : {}),
                    urlPublicacion: resultadoPublicacion.urlPublicacion,
                    publicadoEn: 'externo'
                  })
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'VERSION_EXTERNA',
                titulo: programacion.noticia.titulo,
                diario: programacion.version.diario.nombre,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'COMPLETADA',
                urlPublicacion: resultadoPublicacion.urlPublicacion
              });

              console.log(`✅ CRON: Programación ${programacion.id} (VERSION_EXTERNA) ejecutada exitosamente`);
            } else {
              // Marcar como fallida
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'ERROR',
                  ejecutadoEn: new Date(),
                  errorMensaje: resultadoPublicacion.error || 'Error en publicación externa'
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'VERSION_EXTERNA',
                titulo: programacion.noticia.titulo,
                diario: programacion.version.diario.nombre,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'ERROR',
                error: resultadoPublicacion.error
              });

              console.log(`❌ CRON: Programación ${programacion.id} (VERSION_EXTERNA) falló: ${resultadoPublicacion.error}`);
            }
          } else {
            // Publicar versión localmente (comportamiento original)
            console.log(`🏠 CRON: Publicando versión ${programacion.versionId} localmente`);

            await prisma.versionNoticia.update({
              where: { id: programacion.versionId! },
              data: {
                estadoPublicacion: 'PUBLICADA',
                urlPublicacion: `${process.env.NEXT_PUBLIC_BASE_URL}/versiones/${programacion.versionId}`
              }
            });

            // Marcar como completada
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'COMPLETADA',
                ejecutadoEn: new Date(),
                metadatos: JSON.stringify({
                  ...(programacion.metadatos ? JSON.parse(programacion.metadatos as string) : {}),
                  publicadoEn: 'local'
                })
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'VERSION_LOCAL',
              titulo: programacion.noticia.titulo,
              diario: programacion.version.diario.nombre,
              estado: 'COMPLETADA'
            });

            console.log(`✅ CRON: Programación ${programacion.id} (VERSION_LOCAL) ejecutada exitosamente`);
          }

        } else if (programacion.tipo === 'EXTERNA' && programacion.diarioExterno) {
          // Publicar a diario externo usando la misma API que la publicación manual
          const resultadoPublicacion = await ejecutarPublicacionExternaDirecta(
            programacion.noticiaId,
            programacion.diarioExterno.id,
            programacion.versionId
          );

          if (resultadoPublicacion.success) {
            // Marcar como completada
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'COMPLETADA',
                ejecutadoEn: new Date(),
                metadatos: JSON.stringify({
                  ...(programacion.metadatos ? JSON.parse(programacion.metadatos as string) : {}),
                  urlPublicacion: resultadoPublicacion.urlPublicacion
                })
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'EXTERNA',
              titulo: programacion.noticia.titulo,
              diario: programacion.diarioExterno.nombre,
              estado: 'COMPLETADA',
              urlPublicacion: resultadoPublicacion.urlPublicacion
            });

            console.log(`✅ CRON: Programación ${programacion.id} (EXTERNA) ejecutada exitosamente`);
          } else {
            // Marcar como fallida
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'ERROR',
                ejecutadoEn: new Date(),
                errorMensaje: resultadoPublicacion.error || 'Error desconocido'
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'EXTERNA',
              titulo: programacion.noticia.titulo,
              diario: programacion.diarioExterno.nombre,
              estado: 'ERROR',
              error: resultadoPublicacion.error
            });

            console.log(`❌ CRON: Programación ${programacion.id} (EXTERNA) falló: ${resultadoPublicacion.error}`);
          }
        }

      } catch (error) {
        console.error(`❌ CRON: Error ejecutando programación ${programacion.id}:`, error);

        // Marcar como fallida
        await prisma.programacionPublicacion.update({
          where: { id: programacion.id },
          data: {
            estado: 'ERROR',
            ejecutadoEn: new Date(),
            errorMensaje: error instanceof Error ? error.message : 'Error desconocido'
          }
        });

        resultados.push({
          id: programacion.id,
          tipo: programacion.tipo,
          titulo: programacion.noticia.titulo,
          estado: 'ERROR',
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    console.log(`🎉 CRON: Proceso completado. ${resultados.length} programaciones procesadas`);

    return NextResponse.json({
      message: `Ejecutadas ${resultados.length} programaciones`,
      ejecutadas: resultados.length,
      resultados,
      timestamp: ahora.toISOString()
    });

  } catch (error) {
    console.error('❌ CRON: Error general:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// GET - Verificar estado del cron job
export async function GET(request: NextRequest) {
  try {
    const ahora = new Date();
    const programacionesPendientes = await prisma.programacionPublicacion.findMany({
      where: {
        estado: 'PENDIENTE',
        fechaPublicacion: {
          lte: ahora
        }
      },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true
          }
        },
        version: {
          include: {
            diario: {
              select: {
                id: true,
                nombre: true
              }
            }
          }
        },
        diarioExterno: {
          select: {
            id: true,
            nombre: true
          }
        }
      },
      orderBy: {
        fechaPublicacion: 'asc'
      }
    });

    return NextResponse.json({
      pendientes: programacionesPendientes.length,
      programaciones: programacionesPendientes,
      timestamp: ahora.toISOString()
    });

  } catch (error) {
    console.error('❌ CRON: Error verificando estado:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// Función auxiliar para ejecutar publicación externa directamente
async function ejecutarPublicacionExternaDirecta(
  noticiaId: number,
  diarioExternoId: number,
  versionId?: number | null
) {
  try {
    console.log(`🤖 CRON: Ejecutando publicación externa para noticia ${noticiaId} en diario ${diarioExternoId}${versionId ? ` con versión ${versionId}` : ''}`);

    // Preparar el payload
    const payload: any = {
      diarioExternoId: diarioExternoId
    };

    // Solo agregar versionId si está presente
    if (versionId) {
      payload.versionId = versionId;
    }

    console.log(`🤖 CRON: Payload para publicación:`, payload);

    // Llamar directamente a la función de publicación externa
    const result = await executeExternalPublication(noticiaId, payload);

    console.log(`🤖 CRON: Resultado de publicación:`, {
      success: result.success,
      error: result.error,
      urlPublicacion: result.urlPublicacion
    });

    return {
      success: result.success,
      urlPublicacion: result.urlPublicacion,
      error: result.error
    };

  } catch (error) {
    console.error(`❌ CRON: Error en publicación externa:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error de conexión'
    };
  }
}
