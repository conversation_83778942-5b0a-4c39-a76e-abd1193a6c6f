import { NextRequest, NextResponse } from 'next/server';
import { refreshModelCacheBackground } from '@/lib/ai-model-cache';

// POST - Background refresh of AI models (for cron jobs)
export async function POST(request: NextRequest) {
  try {
    // Verify cron secret to prevent unauthorized access
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || process.env.WEBHOOK_TOKEN;
    
    if (!cronSecret) {
      console.error('CRON_SECRET not configured');
      return NextResponse.json(
        { error: 'Configuración de cron no válida' },
        { status: 500 }
      );
    }

    // Check authorization
    if (!authHeader || !authHeader.includes(cronSecret)) {
      console.error('Unauthorized cron request');
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('Starting scheduled AI models refresh...');
    
    // Run background refresh
    await refreshModelCacheBackground();
    
    console.log('Scheduled AI models refresh completed successfully');

    return NextResponse.json({
      success: true,
      message: 'AI models cache refreshed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in scheduled AI models refresh:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Error en la actualización programada',
        details: error instanceof Error ? error.message : 'Error desconocido',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// GET - Health check for the cron endpoint
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const secret = searchParams.get('secret');
    const cronSecret = process.env.CRON_SECRET || process.env.WEBHOOK_TOKEN;
    
    if (!cronSecret || secret !== cronSecret) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'AI models refresh cron endpoint is healthy',
      timestamp: new Date().toISOString(),
      nextScheduledRun: 'Every 24 hours'
    });

  } catch (error) {
    console.error('Error in cron health check:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Error en el health check',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
