import { NextRequest, NextResponse } from 'next/server';

// GET - Endpoint para obtener el secret del cron de forma segura
export async function GET(request: NextRequest) {
  try {
    // Solo permitir acceso desde el mismo dominio
    const origin = request.headers.get('origin');
    const host = request.headers.get('host');
    
    // Verificar que la llamada viene del mismo servidor
    if (origin && !origin.includes(host || '')) {
      return NextResponse.json({ error: 'Acceso denegado' }, { status: 403 });
    }

    const cronSecret = process.env.CRON_SECRET || 'dev-secret-key-2025';
    
    return NextResponse.json({
      cronSecret: cronSecret
    });

  } catch (error) {
    console.error('❌ Error obteniendo secret del cron:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
