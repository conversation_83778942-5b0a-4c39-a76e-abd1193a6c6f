import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ExternalPublicationService } from '@/lib/external-publication-service';

// POST - Publicar noticia original
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const noticiaId = parseInt(id);
    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { tipo, diarioExternoId } = body;

    // Validar que se proporcione diario externo para publicación directa
    if (!diarioExternoId) {
      return NextResponse.json(
        { error: 'Se requiere especificar un diario externo para la publicación directa' },
        { status: 400 }
      );
    }

    // Verificar que la noticia existe y está aprobada
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      include: {
        categoria: true,
        user: true
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    if (noticia.estado !== 'APROBADA') {
      return NextResponse.json(
        { error: 'La noticia debe estar aprobada para publicarla' },
        { status: 400 }
      );
    }

    if (noticia.publicada) {
      return NextResponse.json(
        { error: 'La noticia ya está publicada' },
        { status: 400 }
      );
    }

    console.log(`📰 Iniciando publicación directa de noticia ${noticiaId}`);

    // Actualizar estado de la noticia
    const noticiaActualizada = await prisma.noticia.update({
      where: { id: noticiaId },
      data: {
        estado: 'PUBLICADA', // Cambiar estado a PUBLICADA
        publicada: true,
        fechaPublicacion: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`✅ Noticia ${noticiaId} marcada como PUBLICADA`);

    // 🚀 CREAR PUBLICACIÓN EXTERNA PARA QUE APAREZCA EN REDES SOCIALES
    let publicacionExterna = null;
    let urlPublicacion = null;
      try {
        console.log(`🌐 Creando publicación externa en diario ${diarioExternoId}`);

        // Obtener diario externo
        const diarioExterno = await prisma.diarioExterno.findUnique({
          where: { id: parseInt(diarioExternoId) }
        });

        if (!diarioExterno) {
          console.warn(`⚠️ Diario externo ${diarioExternoId} no encontrado`);
        } else {
          // Crear servicio de publicación externa
          const diarioExternoAdaptado = {
            ...diarioExterno,
            configuracion: diarioExterno.configuracion || undefined,
            descripcion: diarioExterno.descripcion || undefined
          };
          const publicationService = new ExternalPublicationService(diarioExternoAdaptado);

          // Preparar datos para publicación
          const datosPublicacion = {
            titulo: noticia.titulo,
            subtitulo: noticia.subtitulo || '',
            volanta: noticia.volanta || '',
            contenido: noticia.contenido,
            autor: noticia.user?.name || 'Redacción',
            categoria: noticia.categoria?.nombre || 'General',
            imagenUrl: noticia.imagenUrl
          };

          console.log(`📝 Publicando artículo: "${datosPublicacion.titulo}"`);

          // Subir imagen si existe
          let imagenResult = null;
          if (noticia.imagenUrl) {
            // Descargar imagen desde URL
            const imageDownload = await publicationService.downloadImage(noticia.imagenUrl);
            if (imageDownload.success) {
              // Preparar datos de imagen
              const imageRequest = {
                title: noticia.titulo,
                summary: noticia.titulo,
                date: new Date().toISOString().split('T')[0], // YYYY-MM-DD
                category_id: diarioExternoAdaptado.categoriaImagenId,
                file: imageDownload.data!
              };

              imagenResult = await publicationService.uploadImage(imageRequest);
              console.log(`📸 Imagen subida:`, imagenResult.success ? '✅' : '❌');
            } else {
              console.log(`⚠️ Error descargando imagen: ${imageDownload.error}`);
            }
          }

          // Publicar artículo
          const articleRequest = {
            header: datosPublicacion.titulo,
            created_at: new Date().toISOString().replace('T', ' ').split('.')[0], // YYYY-MM-DD HH:MM:SS
            deferred_publication: false,
            publish: true,
            title: datosPublicacion.titulo,
            summary: datosPublicacion.subtitulo || datosPublicacion.titulo,
            content: datosPublicacion.contenido,
            images: imagenResult?.data?.image_id ? [imagenResult.data.image_id] : [],
            categories: [1] // Categoría por defecto
          };

          const articuloResult = await publicationService.publishArticle(articleRequest);
          console.log(`📄 Artículo publicado:`, articuloResult.success ? '✅' : '❌');

          if (articuloResult.success) {
            urlPublicacion = articuloResult.data?.url;

            // Crear registro de publicación externa
            publicacionExterna = await prisma.publicacionExterna.create({
              data: {
                noticiaId: noticiaId,
                diarioExternoId: parseInt(diarioExternoId),
                estado: 'EXITOSO',
                urlPublicacion: urlPublicacion,
                metadatos: JSON.stringify({
                  iniciadoPor: session.user.email,
                  fechaInicio: new Date().toISOString(),
                  fechaCompletado: new Date().toISOString(),
                  tipoPublicacion: 'publicacion_directa',
                  imagenId: imagenResult?.data?.image_id,
                  articuloId: articuloResult.data?.id,
                  // 🚀 GUARDAR TÍTULO PARA REDES SOCIALES
                  tituloOriginal: datosPublicacion.titulo,
                  tituloPublicado: datosPublicacion.titulo,
                  esVersion: false,
                  datosPublicacion: datosPublicacion
                })
              }
            });

            console.log(`🎉 Publicación externa creada: ID ${publicacionExterna.id}, URL: ${urlPublicacion}`);
          } else {
            console.error(`❌ Error en publicación externa:`, articuloResult.error);

            // Crear registro de error
            publicacionExterna = await prisma.publicacionExterna.create({
              data: {
                noticiaId: noticiaId,
                diarioExternoId: parseInt(diarioExternoId),
                estado: 'ERROR_ARTICULO',
                errorMensaje: articuloResult.error || 'Error desconocido en publicación',
                metadatos: JSON.stringify({
                  iniciadoPor: session.user.email,
                  fechaInicio: new Date().toISOString(),
                  tipoPublicacion: 'publicacion_directa',
                  error: articuloResult.error
                })
              }
            });
          }
        }
      } catch (error) {
        console.error(`❌ Error en publicación externa:`, error);

        // Crear registro de error
        try {
          publicacionExterna = await prisma.publicacionExterna.create({
            data: {
              noticiaId: noticiaId,
              diarioExternoId: parseInt(diarioExternoId),
              estado: 'ERROR_ARTICULO',
              errorMensaje: error instanceof Error ? error.message : 'Error desconocido',
              metadatos: JSON.stringify({
                iniciadoPor: session.user.email,
                fechaInicio: new Date().toISOString(),
                tipoPublicacion: 'publicacion_directa',
                error: error instanceof Error ? error.message : 'Error desconocido'
              })
            }
          });
        } catch (dbError) {
          console.error(`❌ Error guardando registro de error:`, dbError);
        }
      }

    // Crear registro de actividad (comentado porque el modelo no existe)
    // await prisma.actividadNoticia.create({
    //   data: {
    //     noticiaId: noticiaId,
    //     tipo: 'PUBLICACION',
    //     descripcion: `Noticia publicada ${tipo === 'inmediata' ? 'inmediatamente' : 'programada'}`,
    //     userId: session.user.email,
    //     metadatos: {
    //       tipoPublicacion: tipo,
    //       fechaPublicacion: new Date().toISOString()
    //     }
    //   }
    // });

    return NextResponse.json({
      message: 'Noticia publicada exitosamente',
      noticia: noticiaActualizada,
      publicacionExterna: publicacionExterna ? {
        id: publicacionExterna.id,
        estado: publicacionExterna.estado,
        urlPublicacion: urlPublicacion,
        diarioExternoId: publicacionExterna.diarioExternoId
      } : null,
      urlPublicacion: urlPublicacion,
      aparecerEnRedesSociales: !!publicacionExterna && publicacionExterna.estado === 'EXITOSO'
    });

  } catch (error) {
    console.error('Error al publicar noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
