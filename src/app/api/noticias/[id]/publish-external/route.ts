import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createExternalPublicationService, ExternalPublicationService } from '@/lib/external-publication-service';
import { EstadoPublicacion } from '@prisma/client';
import { cleanContentFromImageCredits, processContentForExternalPublication } from '@/lib/content-utils';

// POST /api/noticias/[id]/publish-external - Publicar noticia externamente
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  console.log('🚀🚀🚀 INICIO PUBLISH EXTERNAL 🚀🚀🚀');
  try {
    // Log de datos recibidos muy temprano
    const rawBody = await request.text();
    console.log('Raw body recibido:', rawBody);

    // Parsear el JSON
    let body;
    try {
      body = JSON.parse(rawBody);
      console.log('Body parseado:', body);
    } catch (parseError) {
      console.error('Error parseando JSON:', parseError);
      return NextResponse.json({ error: 'JSON inválido' }, { status: 400 });
    }

    // Verificar si es una llamada del cron
    const cronHeader = request.headers.get('x-cron-execution');
    const isCronExecution = cronHeader === 'true';

    let session = null;
    if (!isCronExecution) {
      session = await getServerSession(authOptions);
      if (!session?.user) {
        console.log('Error: No autorizado');
        return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
      }
    } else {
      console.log('🤖 Ejecución desde CRON - bypass de autenticación');
    }

    const { id: idParam } = await params;
    const noticiaId = parseInt(idParam);

    console.log('Parámetros URL:', { idParam, noticiaId });

    if (isNaN(noticiaId)) {
      console.log('Error: ID de noticia inválido');
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    const { diarioExternoId, versionId } = body;

    console.log('Datos recibidos:', { noticiaId, diarioExternoId, versionId });

    if (!diarioExternoId) {
      return NextResponse.json({ error: 'ID de diario externo requerido' }, { status: 400 });
    }

    // Obtener la noticia
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      include: {
        categoria: true,
        user: true
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Si se proporciona versionId, obtener los datos de la versión específica
    let datosPublicacion = {
      titulo: noticia.titulo,
      subtitulo: noticia.subtitulo,
      volanta: noticia.volanta,
      contenido: noticia.contenido,
      resumen: noticia.resumen,
      imagenUrl: noticia.imagenUrl,
      categoria: noticia.categoria?.nombre,
      autor: noticia.user?.name || noticia.autor
    };

    console.log('📊 DEBUG - Datos noticia original:', {
      titulo: noticia.titulo,
      contenidoLength: noticia.contenido?.length || 0,
      contenidoPreview: noticia.contenido?.substring(0, 100) || 'VACÍO'
    });

    let version = null;
    if (versionId) {
      console.log('Buscando versión:', { versionId: parseInt(versionId), noticiaId });

      version = await prisma.versionNoticia.findFirst({
        where: {
          id: parseInt(versionId),
          noticiaId: noticiaId
        },
        include: {
          diario: true
        }
      });

      console.log('Versión encontrada:', version ? { id: version.id, titulo: version.titulo } : 'null');

      if (!version) {
        console.log('ERROR: Versión no encontrada - devolviendo 404');
        return NextResponse.json({ error: 'Versión no encontrada' }, { status: 404 });
      }

      console.log('Versión válida encontrada, continuando...');

      // Usar los datos de la versión específica
      datosPublicacion = {
        titulo: version.titulo,
        subtitulo: version.subtitulo,
        volanta: version.volanta,
        contenido: version.contenido,
        resumen: version.resumen,
        imagenUrl: version.imagenUrl || noticia.imagenUrl, // Fallback a imagen de noticia
        categoria: noticia.categoria?.nombre,
        autor: noticia.user?.name || noticia.autor
      };

      console.log('📊 DEBUG - Datos versión:', {
        titulo: version.titulo,
        contenidoLength: version.contenido?.length || 0,
        contenidoPreview: version.contenido?.substring(0, 100) || 'VACÍO'
      });
    }

    // Validar que tenga imagen
    if (!datosPublicacion.imagenUrl) {
      return NextResponse.json({
        error: 'La noticia/versión debe tener una imagen para ser publicada externamente'
      }, { status: 400 });
    }

    // Obtener el diario externo
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId },
      include: {
        categoriaMapeos: {
          where: {
            categoriaLocalId: noticia.categoriaId || 0
          }
        }
      }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario externo no encontrado' }, { status: 404 });
    }

    if (!diario.activo) {
      return NextResponse.json({ error: 'El diario externo está inactivo' }, { status: 400 });
    }

    // Verificar que exista mapeo de categoría
    if (noticia.categoriaId && diario.categoriaMapeos.length === 0) {
      return NextResponse.json({ 
        error: 'No existe mapeo de categoría para esta noticia en el diario seleccionado' 
      }, { status: 400 });
    }

    // Verificar si ya existe una publicación exitosa
    const publicacionExistente = await prisma.publicacionExterna.findFirst({
      where: {
        noticiaId,
        diarioExternoId,
        estado: EstadoPublicacion.EXITOSO
      }
    });

    if (publicacionExistente) {
      // Verificar si es la misma versión
      const metadatos = publicacionExistente.metadatos ? JSON.parse(publicacionExistente.metadatos) : {};
      const publicacionVersionId = metadatos.versionId;

      // Si es una versión específica y ya fue publicada esa versión
      if (versionId && publicacionVersionId && parseInt(versionId) === publicacionVersionId) {
        return NextResponse.json({
          error: '✅ Esta versión ya fue publicada exitosamente en este diario',
          data: {
            urlPublicacion: publicacionExistente.urlPublicacion,
            fechaPublicacion: publicacionExistente.createdAt,
            alreadyPublished: true
          }
        }, { status: 409 });
      }

      // Si es la noticia original y ya fue publicada
      if (!versionId && !publicacionVersionId) {
        return NextResponse.json({
          error: '✅ Esta noticia ya fue publicada exitosamente en este diario',
          data: {
            urlPublicacion: publicacionExistente.urlPublicacion,
            fechaPublicacion: publicacionExistente.createdAt,
            alreadyPublished: true
          }
        }, { status: 409 });
      }
    }

    // Crear registro de publicación
    const publicacion = await prisma.publicacionExterna.create({
      data: {
        noticiaId,
        diarioExternoId,
        estado: EstadoPublicacion.PENDIENTE,
        metadatos: JSON.stringify({
          iniciadoPor: session?.user?.email || 'cron',
          fechaInicio: new Date().toISOString()
        })
      }
    });

    // Crear servicio de publicación
    const service = createExternalPublicationService({
      ...diario,
      descripcion: diario.descripcion || undefined,
      configuracion: diario.configuracion || undefined
    });

    // Si es una versión, marcar como publicando
    if (versionId && version) {
      await prisma.versionNoticia.update({
        where: { id: parseInt(versionId) },
        data: { estadoPublicacion: 'PUBLICANDO' }
      });
    }

    try {
      // PASO 1: Subir imagen
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: { estado: EstadoPublicacion.SUBIENDO_IMAGEN }
      });

      const imagenResult = await uploadImageToExternal(service, datosPublicacion, diario.categoriaImagenId);
      
      if (!imagenResult.success) {
        await prisma.publicacionExterna.update({
          where: { id: publicacion.id },
          data: {
            estado: EstadoPublicacion.ERROR_IMAGEN,
            errorMensaje: imagenResult.error
          }
        });

        // Si es una versión, marcar como error
        if (versionId && version) {
          await prisma.versionNoticia.update({
            where: { id: parseInt(versionId) },
            data: { estadoPublicacion: 'ERROR' }
          });
        }

        return NextResponse.json({
          error: 'Error al subir imagen',
          details: imagenResult.error
        }, { status: 500 });
      }

      // Actualizar con ID de imagen
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: { 
          estado: EstadoPublicacion.IMAGEN_SUBIDA,
          imagenExternaId: imagenResult.data?.id
        }
      });

      // PASO 2: Publicar artículo
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: { estado: EstadoPublicacion.PUBLICANDO_ARTICULO }
      });

      const categoriasMapeadas = diario.categoriaMapeos.map(m => m.categoriaExternaId);
      const articuloResult = await publishArticleToExternal(
        service,
        datosPublicacion,
        imagenResult.data!.image_id,
        categoriasMapeadas
      );

      if (!articuloResult.success) {
        await prisma.publicacionExterna.update({
          where: { id: publicacion.id },
          data: {
            estado: EstadoPublicacion.ERROR_ARTICULO,
            errorMensaje: articuloResult.error
          }
        });

        // Si es una versión, marcar como error
        if (versionId && version) {
          await prisma.versionNoticia.update({
            where: { id: parseInt(versionId) },
            data: { estadoPublicacion: 'ERROR' }
          });
        }

        return NextResponse.json({
          error: 'Error al publicar artículo',
          details: articuloResult.error
        }, { status: 500 });
      }

      // PASO 3: Marcar como exitoso
      const urlPublicacion = (() => {
        // Si el artículo ya tiene una URL, usarla
        if (articuloResult.data?.url) {
          return articuloResult.data.url;
        }

        // Construir URL usando la configuración del diario
        const config = diario.configuracion ? JSON.parse(diario.configuracion) : null;
        const articleId = (articuloResult.data as any)?.article_id || (articuloResult.data as any)?.id;

        if (config?.urlSitioWeb && articleId) {
          // Para Del Sur Diario: https://www.delsurdiario.com/60024-titulo-slug
          const slug = datosPublicacion.titulo
            .toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remover acentos
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-|-$/g, '');
          return `${config.urlSitioWeb}/${articleId}-${slug}`;
        }

        // Fallback a la URL base
        return `${diario.urlBase.replace('/api', '')}/nota/${articleId}`;
      })();

      const publicacionFinal = await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: {
          estado: EstadoPublicacion.EXITOSO,
          articuloExternoId: parseInt((articuloResult.data as any)?.article_id || (articuloResult.data as any)?.id || '0'),
          urlPublicacion: urlPublicacion,
          metadatos: JSON.stringify({
            iniciadoPor: session?.user?.email || 'cron',
            fechaInicio: new Date().toISOString(),
            fechaCompletado: new Date().toISOString(),
            imagenId: imagenResult.data?.image_id,
            articuloId: (articuloResult.data as any)?.article_id || (articuloResult.data as any)?.id,
            versionId: versionId ? parseInt(versionId) : null,
            tipoPublicacion: versionId ? 'version' : 'noticia_original',
            // 🚀 GUARDAR TÍTULO PUBLICADO PARA MOSTRAR EN REDES SOCIALES
            tituloOriginal: datosPublicacion.titulo, // Título que se publicó (versión o original)
            tituloPublicado: datosPublicacion.titulo,
            esVersion: !!versionId,
            versionDiario: version?.diario?.nombre || null,
            datosPublicacion: {
              titulo: datosPublicacion.titulo,
              subtitulo: datosPublicacion.subtitulo,
              volanta: datosPublicacion.volanta,
              autor: datosPublicacion.autor
            }
          })
        }
      });

      // Si se publicó una versión específica, actualizar su estado de publicación
      if (versionId && version) {
        await prisma.versionNoticia.update({
          where: { id: parseInt(versionId) },
          data: {
            estadoPublicacion: 'PUBLICADA',
            urlPublicacion: urlPublicacion
          }
        });
      }

      // 🚀 ACTUALIZAR ESTADO DE LA NOTICIA CUANDO SE PUBLICA EXITOSAMENTE
      console.log(`📰 Actualizando estado de noticia ${noticiaId} a PUBLICADA`);

      // Solo actualizar si la noticia está en BORRADOR, EN_REVISION o APROBADA
      const estadosPermitidos = ['BORRADOR', 'EN_REVISION', 'APROBADA'];
      if (estadosPermitidos.includes(noticia.estado)) {
        await prisma.noticia.update({
          where: { id: noticiaId },
          data: {
            estado: 'PUBLICADA',
            fechaPublicacion: new Date(),
            publicada: true
          }
        });
        console.log(`✅ Estado de noticia ${noticiaId} actualizado a PUBLICADA`);
      } else {
        console.log(`⚠️ Noticia ${noticiaId} tiene estado ${noticia.estado}, no se actualiza automáticamente`);
      }

      return NextResponse.json({
        success: true,
        data: {
          publicacionId: publicacionFinal.id,
          imagenExternaId: publicacionFinal.imagenExternaId,
          articuloExternoId: publicacionFinal.articuloExternoId,
          urlPublicacion: publicacionFinal.urlPublicacion,
          estado: publicacionFinal.estado
        },
        message: versionId ? 'Versión publicada exitosamente' : 'Noticia publicada exitosamente'
      });

    } catch (error) {
      // Error general
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: {
          estado: EstadoPublicacion.ERROR_CONEXION,
          errorMensaje: error instanceof Error ? error.message : 'Error desconocido'
        }
      });

      // Si es una versión, marcar como error
      if (versionId && version) {
        await prisma.versionNoticia.update({
          where: { id: parseInt(versionId) },
          data: { estadoPublicacion: 'ERROR' }
        });
      }

      throw error;
    }

  } catch (error) {
    console.error('=== ERROR CATCH GENERAL ===');
    console.error('Error publishing externally:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack');
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// Función auxiliar para subir imagen
async function uploadImageToExternal(
  service: ExternalPublicationService,
  datosPublicacion: any,
  categoriaImagenId: number
) {
  // Descargar imagen
  const imageDownload = await service.downloadImage(datosPublicacion.imagenUrl);
  if (!imageDownload.success) {
    return { success: false, error: `Error descargando imagen: ${imageDownload.error}` };
  }

  // Preparar datos de imagen
  const imageRequest = {
    title: datosPublicacion.titulo,
    summary: datosPublicacion.titulo, // Usar título como summary por defecto
    date: ExternalPublicationService.formatDateForAPI(new Date()),
    category_id: categoriaImagenId,
    file: imageDownload.data!
  };

  return await service.uploadImage(imageRequest);
}

// Función auxiliar para publicar artículo
async function publishArticleToExternal(
  service: ExternalPublicationService,
  datosPublicacion: any,
  imagenId: number,
  categorias: number[]
) {
  // Procesar el contenido manteniendo HTML pero removiendo créditos
  const contenidoLimpio = processContentForExternalPublication(datosPublicacion.contenido || '');

  console.log('📝 DEBUG - Contenido original length:', datosPublicacion.contenido?.length || 0);
  console.log('📝 DEBUG - Contenido limpio length:', contenidoLimpio.length);
  console.log('📝 DEBUG - Contenido original preview:', datosPublicacion.contenido?.substring(0, 200) || 'VACÍO');
  console.log('📝 DEBUG - Contenido limpio preview:', contenidoLimpio.substring(0, 200));

  const articleRequest = {
    header: datosPublicacion.volanta || '',
    created_at: ExternalPublicationService.formatDateForAPI(new Date()),
    deferred_publication: false,
    publish: true,
    title: datosPublicacion.titulo,
    summary: datosPublicacion.subtitulo || datosPublicacion.resumen || '',
    content: contenidoLimpio,
    images: [imagenId],
    categories: categorias
  };

  console.log('📤 DEBUG - Article request content length:', articleRequest.content.length);

  return await service.publishArticle(articleRequest);
}
