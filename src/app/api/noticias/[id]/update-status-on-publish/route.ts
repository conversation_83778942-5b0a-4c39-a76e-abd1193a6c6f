import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/noticias/[id]/update-status-on-publish - Actualizar estado de noticia cuando se publica externamente
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const noticiaId = parseInt(idParam);

    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { force = false } = body; // Parámetro para forzar actualización

    console.log(`📰 Solicitud de actualización de estado para noticia ${noticiaId}`);

    // Obtener la noticia actual
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      select: {
        id: true,
        titulo: true,
        estado: true,
        publicada: true,
        fechaPublicacion: true
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    console.log(`📊 Estado actual de la noticia: ${noticia.estado}`);

    // Verificar si hay publicaciones externas exitosas
    const publicacionesExitosas = await prisma.publicacionExterna.findMany({
      where: {
        noticiaId: noticiaId,
        estado: 'EXITOSO'
      },
      include: {
        diarioExterno: {
          select: { nombre: true }
        }
      }
    });

    console.log(`📊 Publicaciones exitosas encontradas: ${publicacionesExitosas.length}`);

    if (publicacionesExitosas.length === 0) {
      return NextResponse.json({
        error: 'No hay publicaciones externas exitosas para esta noticia',
        data: {
          noticiaId,
          estadoActual: noticia.estado,
          publicacionesExitosas: 0
        }
      }, { status: 400 });
    }

    // Verificar si ya está publicada
    if (noticia.estado === 'PUBLICADA' && noticia.publicada && !force) {
      return NextResponse.json({
        message: 'La noticia ya está marcada como PUBLICADA',
        data: {
          noticiaId,
          estadoActual: noticia.estado,
          fechaPublicacion: noticia.fechaPublicacion,
          publicacionesExitosas: publicacionesExitosas.length,
          diarios: publicacionesExitosas.map(p => p.diarioExterno.nombre)
        }
      });
    }

    // Estados que permiten actualización automática
    const estadosPermitidos = ['EN_REVISION', 'APROBADA', 'BORRADOR'];
    
    if (!estadosPermitidos.includes(noticia.estado) && !force) {
      return NextResponse.json({
        error: `No se puede actualizar automáticamente desde el estado ${noticia.estado}. Use force=true para forzar la actualización.`,
        data: {
          noticiaId,
          estadoActual: noticia.estado,
          estadosPermitidos,
          publicacionesExitosas: publicacionesExitosas.length
        }
      }, { status: 400 });
    }

    // Actualizar el estado de la noticia
    const noticiaActualizada = await prisma.noticia.update({
      where: { id: noticiaId },
      data: {
        estado: 'PUBLICADA',
        fechaPublicacion: noticia.fechaPublicacion || new Date(), // Mantener fecha existente o usar actual
        publicada: true
      },
      select: {
        id: true,
        titulo: true,
        estado: true,
        publicada: true,
        fechaPublicacion: true
      }
    });

    console.log(`✅ Estado de noticia ${noticiaId} actualizado a PUBLICADA`);

    // Obtener información adicional para la respuesta
    const publicacionesInfo = publicacionesExitosas.map(pub => ({
      id: pub.id,
      diario: pub.diarioExterno.nombre,
      urlPublicacion: pub.urlPublicacion,
      fechaCreacion: pub.createdAt
    }));

    return NextResponse.json({
      success: true,
      message: 'Estado de noticia actualizado exitosamente',
      data: {
        noticia: {
          id: noticiaActualizada.id,
          titulo: noticiaActualizada.titulo,
          estadoAnterior: noticia.estado,
          estadoNuevo: noticiaActualizada.estado,
          publicada: noticiaActualizada.publicada,
          fechaPublicacion: noticiaActualizada.fechaPublicacion
        },
        publicacionesExternas: {
          total: publicacionesExitosas.length,
          publicaciones: publicacionesInfo
        },
        actualizadoPor: session.user.email,
        fechaActualizacion: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error al actualizar estado de noticia:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// GET /api/noticias/[id]/update-status-on-publish - Verificar estado de publicaciones externas
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const noticiaId = parseInt(idParam);

    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    // Obtener la noticia
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      select: {
        id: true,
        titulo: true,
        estado: true,
        publicada: true,
        fechaPublicacion: true
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Obtener todas las publicaciones externas
    const publicaciones = await prisma.publicacionExterna.findMany({
      where: { noticiaId },
      include: {
        diarioExterno: {
          select: { nombre: true, urlBase: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const publicacionesExitosas = publicaciones.filter(p => p.estado === 'EXITOSO');
    const publicacionesError = publicaciones.filter(p => p.estado.includes('ERROR'));
    const publicacionesPendientes = publicaciones.filter(p => 
      ['PENDIENTE', 'SUBIENDO_IMAGEN', 'PUBLICANDO_ARTICULO'].includes(p.estado)
    );

    // Determinar si se puede actualizar automáticamente
    const estadosPermitidos = ['EN_REVISION', 'APROBADA', 'BORRADOR'];
    const puedeActualizarAutomaticamente = estadosPermitidos.includes(noticia.estado);
    const deberiaEstarPublicada = publicacionesExitosas.length > 0 && noticia.estado !== 'PUBLICADA';

    return NextResponse.json({
      success: true,
      data: {
        noticia: {
          id: noticia.id,
          titulo: noticia.titulo,
          estado: noticia.estado,
          publicada: noticia.publicada,
          fechaPublicacion: noticia.fechaPublicacion
        },
        publicaciones: {
          total: publicaciones.length,
          exitosas: publicacionesExitosas.length,
          error: publicacionesError.length,
          pendientes: publicacionesPendientes.length
        },
        analisis: {
          puedeActualizarAutomaticamente,
          deberiaEstarPublicada,
          razon: deberiaEstarPublicada 
            ? `Hay ${publicacionesExitosas.length} publicación(es) exitosa(s) pero la noticia está en estado ${noticia.estado}`
            : publicacionesExitosas.length === 0 
              ? 'No hay publicaciones externas exitosas'
              : 'La noticia ya está correctamente marcada como PUBLICADA'
        },
        publicacionesDetalle: publicaciones.map(pub => ({
          id: pub.id,
          diario: pub.diarioExterno.nombre,
          estado: pub.estado,
          urlPublicacion: pub.urlPublicacion,
          fechaCreacion: pub.createdAt,
          fechaActualizacion: pub.updatedAt,
          error: pub.errorMensaje
        }))
      }
    });

  } catch (error) {
    console.error('❌ Error al verificar estado de publicaciones:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
