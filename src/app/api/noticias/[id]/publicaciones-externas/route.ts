import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const noticiaId = parseInt(resolvedParams.id);
    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Obtener todas las publicaciones externas de esta noticia
    const publicaciones = await prisma.publicacionExterna.findMany({
      where: {
        noticiaId: noticiaId
      },
      include: {
        diarioExterno: {
          select: {
            id: true,
            nombre: true,
            urlBase: true,
            descripcion: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Formatear las publicaciones para el frontend
    const publicacionesFormateadas = publicaciones.map(pub => ({
      id: pub.id,
      estado: pub.estado,
      urlPublicacion: pub.urlPublicacion,
      imagenExternaId: pub.imagenExternaId,
      articuloExternoId: pub.articuloExternoId,
      errorMensaje: pub.errorMensaje,
      metadatos: pub.metadatos,
      fechaCreacion: pub.createdAt,
      fechaActualizacion: pub.updatedAt,
      diario: {
        id: pub.diarioExterno.id,
        nombre: pub.diarioExterno.nombre,
        urlBase: pub.diarioExterno.urlBase,
        descripcion: pub.diarioExterno.descripcion
      }
    }));

    return NextResponse.json({
      success: true,
      publicaciones: publicacionesFormateadas
    });

  } catch (error) {
    console.error('Error al obtener publicaciones externas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
