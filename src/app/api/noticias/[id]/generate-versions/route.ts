import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { rewriteNoticia, rewriteNoticiaWithFallback } from '@/lib/ai-service';

// POST /api/noticias/[id]/generate-versions - Generar versiones con IA
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Obtener datos del request
    const body = await request.json();
    console.log('Body recibido:', body);
    const { diarioIds } = body; // Array de IDs de diarios seleccionados
    console.log('diarioIds extraídos:', diarioIds);

    if (!diarioIds || !Array.isArray(diarioIds) || diarioIds.length === 0) {
      return NextResponse.json(
        { error: 'Debe seleccionar al menos un diario' },
        { status: 400 }
      );
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id },
      include: {
        categoria: true,
        user: true,
      },
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos (solo el autor o admin/editor pueden generar versiones)
    const userRole = session.user.role;
    const isAuthor = noticia.userId === parseInt(session.user.id);
    const canGenerate = isAuthor || userRole === 'ADMIN' || userRole === 'EDITOR';

    if (!canGenerate) {
      return NextResponse.json(
        { error: 'No tienes permisos para generar versiones de esta noticia' },
        { status: 403 }
      );
    }

    // Obtener los diarios seleccionados con toda su información
    const diarios = await prisma.diario.findMany({
      where: {
        id: { in: diarioIds },
        isActive: true,
      },
    });

    if (diarios.length === 0) {
      return NextResponse.json(
        { error: 'No se encontraron diarios válidos' },
        { status: 400 }
      );
    }

    console.log(`📊 Generando versiones para ${diarios.length} diarios:`, diarios.map(d => d.nombre));

    const generatedVersions = [];
    const errors = [];

    // Generar versión para cada diario SECUENCIALMENTE con delays y retry
    for (let i = 0; i < diarios.length; i++) {
      const diario = diarios[i];
      const posicion = i + 1;
      const esUltima = posicion === diarios.length;

      try {
        console.log(`\n🔄 Generando versión ${posicion}/${diarios.length} para ${diario.nombre}...`);
        console.log(`📝 Prompt del diario (${diario.nombre}):`, diario.prompt.substring(0, 100) + '...');

        // Agregar delay progresivo para evitar rate limiting
        if (i > 0) {
          const delay = 2000 + (i * 1000); // 2s, 3s, 4s...
          console.log(`⏳ Esperando ${delay}ms para evitar rate limiting...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        let rewriteResponse = await rewriteNoticiaWithFallback({
          titulo: noticia.titulo,
          subtitulo: noticia.subtitulo || undefined,
          volanta: noticia.volanta || undefined,
          contenido: noticia.contenido,
          resumen: noticia.resumen || undefined,
          prompt: diario.prompt,
          diarioNombre: diario.nombre,
        }, diario.id);

        let intentos = 1;
        const maxIntentos = esUltima ? 3 : 2; // Más intentos para la última versión
        const minNegritasRequeridas = esUltima ? 8 : 3; // Más estricto para la última versión (Delsurdiario)

        // Función mejorada para contar negritas completas
        function contarNegritas(contenido: string): number {
          const matches = contenido.match(/<strong[^>]*>.*?<\/strong>/gi);
          return matches ? matches.length : 0;
        }

        // Verificar si tiene negritas SUFICIENTES y reintentar si es necesario
        let tieneNegritas = rewriteResponse.contenido.includes('<strong>');
        let cantidadNegritas = contarNegritas(rewriteResponse.contenido);
        let suficientesNegritas = cantidadNegritas >= minNegritasRequeridas;

        console.log(`📊 Verificación intento ${intentos}: ${tieneNegritas ? '✅' : '❌'} ${cantidadNegritas} negritas ${suficientesNegritas ? '✅' : `❌ (mín: ${minNegritasRequeridas})`}`);

        // Retry loop para versiones sin negritas O con pocas negritas
        while ((!tieneNegritas || !suficientesNegritas) && intentos < maxIntentos) {
          intentos++;
          const razonReintento = !tieneNegritas ? 'Sin negritas' : `Pocas negritas (${cantidadNegritas}/${minNegritasRequeridas})`;
          console.log(`⚠️ ${razonReintento}, reintentando... (${intentos}/${maxIntentos})`);

          // Delay adicional antes del retry
          await new Promise(resolve => setTimeout(resolve, 3000));

          rewriteResponse = await rewriteNoticiaWithFallback({
            titulo: noticia.titulo,
            subtitulo: noticia.subtitulo || undefined,
            volanta: noticia.volanta || undefined,
            contenido: noticia.contenido,
            resumen: noticia.resumen || undefined,
            prompt: diario.prompt,
            diarioNombre: diario.nombre,
          }, diario.id);

          tieneNegritas = rewriteResponse.contenido.includes('<strong>');
          cantidadNegritas = contarNegritas(rewriteResponse.contenido);
          suficientesNegritas = cantidadNegritas >= minNegritasRequeridas;

          console.log(`📊 Verificación intento ${intentos}: ${tieneNegritas ? '✅' : '❌'} ${cantidadNegritas} negritas ${suficientesNegritas ? '✅' : `❌ (mín: ${minNegritasRequeridas})`}`);
        }

        if ((!tieneNegritas || !suficientesNegritas) && esUltima) {
          console.log(`⚠️ ALERTA: Última versión con problemas de negritas después de ${maxIntentos} intentos (${cantidadNegritas}/${minNegritasRequeridas})`);
        }

        // Guardar la versión generada en la base de datos
        const versionGuardada = await prisma.versionNoticia.create({
          data: {
            titulo: rewriteResponse.titulo,
            subtitulo: noticia.subtitulo, // Mantener el subtítulo original
            volanta: rewriteResponse.volanta,
            contenido: rewriteResponse.contenido,
            resumen: rewriteResponse.resumen,
            imagenUrl: noticia.imagenUrl, // Copiar la imagen de la noticia original
            promptUsado: diario.prompt,
            metadatos: JSON.stringify(rewriteResponse.metadatos),
            noticiaId: noticia.id,
            diarioId: diario.id,
            generadaPor: parseInt(session.user.id),
            estado: 'GENERADA',
          },
          include: {
            diario: true,
            usuario: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        generatedVersions.push(versionGuardada);

        console.log(`✅ Versión generada exitosamente para ${diario.nombre}`);
        console.log(`📊 Negritas: ${tieneNegritas ? '✅' : '❌'} (${cantidadNegritas} total)`);
        console.log(`📊 Metadatos:`, rewriteResponse.metadatos);

        if (esUltima && !tieneNegritas) {
          console.log(`🚨 PROBLEMA DETECTADO: La última versión (${diario.nombre}) no tiene negritas`);
        }

      } catch (error) {
        console.error(`❌ Error generando versión ${posicion}/${diarios.length} para ${diario.nombre}:`, error);
        errors.push({
          diario: diario.nombre,
          posicion: posicion,
          esUltima: esUltima,
          error: error instanceof Error ? error.message : 'Error desconocido',
        });
      }
    }

    // Cambiar el estado de la noticia a EN_REVISION si se generaron versiones
    if (generatedVersions.length > 0 && noticia.estado === 'BORRADOR') {
      await prisma.noticia.update({
        where: { id },
        data: { estado: 'EN_REVISION' },
      });
    }

    // Análisis final de negritas
    const analisisNegritas = generatedVersions.map((version, index) => {
      const tieneNegritas = version.contenido.includes('<strong>');
      const cantidadNegritas = (version.contenido.match(/<strong>/g) || []).length;
      return {
        diario: version.diario.nombre,
        posicion: index + 1,
        esUltima: index === generatedVersions.length - 1,
        tieneNegritas,
        cantidadNegritas
      };
    });

    const versionesSinNegritas = analisisNegritas.filter(a => !a.tieneNegritas);
    const problemasEnUltima = versionesSinNegritas.filter(a => a.esUltima);

    if (problemasEnUltima.length > 0) {
      console.log(`🚨 PROBLEMA DETECTADO: La última versión no tiene negritas`);
      console.log(`📊 Análisis completo:`, analisisNegritas);
    }

    return NextResponse.json({
      success: true,
      message: `Se generaron ${generatedVersions.length} versiones exitosamente`,
      versiones: generatedVersions,
      errores: errors,
      analisisNegritas: analisisNegritas,
      advertencias: problemasEnUltima.length > 0 ? [`La última versión (${problemasEnUltima[0].diario}) no tiene negritas`] : []
    });

  } catch (error) {
    console.error('Error en generate-versions:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}