import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/noticias/[id]/versions - Obtener versiones generadas
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id },
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Obtener todas las versiones de la noticia
    const versiones = await prisma.versionNoticia.findMany({
      where: { noticiaId: id },
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Obtener publicaciones externas para esta noticia
    const publicacionesExternas = await prisma.publicacionExterna.findMany({
      where: {
        noticiaId: id,
        estado: 'EXITOSO'
      },
      include: {
        diarioExterno: {
          select: {
            id: true,
            nombre: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Parsear metadatos JSON y agregar información de publicación externa
    const versionesConMetadatos = versiones.map(version => {
      // Buscar publicación externa para esta versión específica
      const publicacionExterna = publicacionesExternas.find(pub => {
        if (!pub.metadatos) return false;
        try {
          const metadatos = JSON.parse(pub.metadatos);
          return metadatos.versionId === version.id;
        } catch {
          return false;
        }
      });

      console.log(`🔍 Versión ${version.id} (${version.diario.nombre}):`, {
        tienePublicacion: !!publicacionExterna,
        urlPublicacion: publicacionExterna?.urlPublicacion,
        diarioExterno: publicacionExterna?.diarioExterno?.nombre
      });

      return {
        ...version,
        metadatos: version.metadatos ? JSON.parse(version.metadatos) : null,
        // Agregar información de publicación externa
        estadoPublicacion: publicacionExterna ? 'PUBLICADA' : (version.estadoPublicacion || 'PENDIENTE'),
        urlPublicacion: publicacionExterna?.urlPublicacion || version.urlPublicacion,
        publicacionExterna: publicacionExterna ? {
          id: publicacionExterna.id,
          urlPublicacion: publicacionExterna.urlPublicacion,
          diarioExterno: publicacionExterna.diarioExterno,
          fechaPublicacion: publicacionExterna.createdAt
        } : null
      };
    });

    return NextResponse.json({
      noticia: {
        id: noticia.id,
        titulo: noticia.titulo,
        estado: noticia.estado,
      },
      versiones: versionesConMetadatos,
    });

  } catch (error) {
    console.error('Error al obtener versiones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/noticias/[id]/versions - Actualizar estado de versiones
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { versionId, estado, titulo, subtitulo, volanta, contenido, resumen } = body;

    if (!versionId) {
      return NextResponse.json(
        { error: 'versionId es requerido' },
        { status: 400 }
      );
    }

    // Determinar si es una operación de cambio de estado o edición de contenido
    const isStateChange = !!estado;
    const isContentEdit = !!(titulo || subtitulo || volanta || contenido || resumen);

    // Validar operación
    if (!isStateChange && !isContentEdit) {
      return NextResponse.json(
        { error: 'Debe especificar estado o contenido para actualizar' },
        { status: 400 }
      );
    }

    // Verificar que el estado es válido (si se está cambiando)
    if (isStateChange) {
      const estadosValidos = ['GENERADA', 'APROBADA', 'RECHAZADA', 'EN_REVISION'];
      if (!estadosValidos.includes(estado)) {
        return NextResponse.json(
          { error: 'Estado inválido' },
          { status: 400 }
        );
      }
    }

    // Verificar que la noticia existe y obtener información del autor
    const noticia = await prisma.noticia.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true }
        }
      }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos
    const userRole = session.user.role;
    const isAdmin = userRole === 'ADMIN';
    const isEditor = userRole === 'EDITOR';
    const isAuthor = parseInt(session.user.id) === noticia.user.id;

    // Verificar permisos según el tipo de operación
    if (!isAdmin && !isEditor && !isAuthor) {
      return NextResponse.json(
        { error: 'No tienes permisos para modificar versiones' },
        { status: 403 }
      );
    }

    // Para cambios de estado: Los autores solo pueden poner en revisión
    if (isStateChange && isAuthor && !isAdmin && !isEditor && estado !== 'EN_REVISION') {
      return NextResponse.json(
        { error: 'Solo puedes enviar versiones a revisión. Los administradores pueden aprobar o rechazar.' },
        { status: 403 }
      );
    }

    // Para edición de contenido: Los autores pueden editar sus propias versiones
    // Los administradores y editores pueden editar cualquier versión

    // Preparar datos para actualizar
    const updateData: any = {};

    if (isStateChange) {
      updateData.estado = estado;
    }

    if (isContentEdit) {
      if (titulo !== undefined) updateData.titulo = titulo;
      if (subtitulo !== undefined) updateData.subtitulo = subtitulo;
      if (volanta !== undefined) updateData.volanta = volanta;
      if (contenido !== undefined) updateData.contenido = contenido;
      if (resumen !== undefined) updateData.resumen = resumen;
    }

    // Actualizar la versión
    const versionActualizada = await prisma.versionNoticia.update({
      where: { id: versionId },
      data: updateData,
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      version: {
        ...versionActualizada,
        metadatos: versionActualizada.metadatos ? JSON.parse(versionActualizada.metadatos) : null,
      },
    });

  } catch (error) {
    console.error('Error al actualizar versión:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}