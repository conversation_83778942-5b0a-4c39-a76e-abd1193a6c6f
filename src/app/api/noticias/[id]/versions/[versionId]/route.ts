import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { rewriteNoticia, rewriteNoticiaWithFallback } from '@/lib/ai-service';

// PUT - Update AI-generated version
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; versionId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const id = parseInt(resolvedParams.id);
    const versionId = parseInt(resolvedParams.versionId);

    if (isNaN(id) || isNaN(versionId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { titulo, volanta, resumen, contenido, imagenUrl } = body;

    // Verificar que la versión existe y pertenece a la noticia
    const existingVersion = await prisma.versionNoticia.findFirst({
      where: {
        id: versionId,
        noticiaId: id,
      },
    });

    if (!existingVersion) {
      return NextResponse.json({ error: 'Versión no encontrada' }, { status: 404 });
    }

    // Actualizar la versión
    const updatedVersion = await prisma.versionNoticia.update({
      where: { id: versionId },
      data: {
        titulo: titulo || existingVersion.titulo,
        volanta: volanta !== undefined ? volanta : existingVersion.volanta,
        resumen: resumen !== undefined ? resumen : existingVersion.resumen,
        contenido: contenido || existingVersion.contenido,
        imagenUrl: imagenUrl !== undefined ? imagenUrl : existingVersion.imagenUrl,
        updatedAt: new Date(),
      },
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(updatedVersion);

  } catch (error) {
    console.error('Error updating version:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Regenerate AI version
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; versionId: string }> }
) {
  try {
    const resolvedParams = await params;
    console.log('🔄 Iniciando regeneración de versión:', { id: resolvedParams.id, versionId: resolvedParams.versionId });

    const session = await getServerSession(authOptions);

    if (!session?.user) {
      console.log('❌ Usuario no autorizado');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const id = parseInt(resolvedParams.id);
    const versionId = parseInt(resolvedParams.versionId);

    if (isNaN(id) || isNaN(versionId)) {
      console.log('❌ IDs inválidos:', { id, versionId });
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    console.log('📖 Obteniendo noticia y versión...');

    // Obtener la noticia original y la versión existente
    const [noticia, existingVersion] = await Promise.all([
      prisma.noticia.findUnique({
        where: { id },
        include: { categoria: true },
      }),
      prisma.versionNoticia.findFirst({
        where: {
          id: versionId,
          noticiaId: id,
        },
        include: { diario: true },
      }),
    ]);

    if (!noticia || !existingVersion) {
      console.log('❌ Noticia o versión no encontrada:', { noticia: !!noticia, existingVersion: !!existingVersion });
      return NextResponse.json({ error: 'Noticia o versión no encontrada' }, { status: 404 });
    }

    console.log('🤖 Llamando a OpenAI para regenerar...');
    console.log('Prompt del diario:', existingVersion.diario.prompt);

    // Regenerar usando el prompt del diario con fallback automático
    const rewriteResponse = await rewriteNoticiaWithFallback({
      titulo: noticia.titulo,
      subtitulo: noticia.subtitulo || undefined,
      volanta: noticia.volanta || undefined,
      contenido: noticia.contenido,
      resumen: noticia.resumen || undefined,
      prompt: existingVersion.diario.prompt,
      diarioNombre: existingVersion.diario.nombre,
    }, existingVersion.diario.id);

    console.log('✅ Respuesta de OpenAI recibida');

    // Actualizar la versión con el contenido regenerado
    const updatedVersion = await prisma.versionNoticia.update({
      where: { id: versionId },
      data: {
        titulo: rewriteResponse.titulo,
        volanta: rewriteResponse.volanta,
        resumen: rewriteResponse.resumen,
        contenido: rewriteResponse.contenido,
        // Mantener la imagen existente o usar la de la noticia original si no tiene
        imagenUrl: existingVersion.imagenUrl || noticia.imagenUrl,
        metadatos: JSON.stringify(rewriteResponse.metadatos),
        promptUsado: existingVersion.diario.prompt,
        updatedAt: new Date(),
      },
      include: {
        diario: true,
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('✅ Versión actualizada en base de datos');
    return NextResponse.json(updatedVersion);

  } catch (error) {
    console.error('❌ Error regenerating version:', error);
    return NextResponse.json(
      { error: `Error al regenerar la versión: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    );
  }
}
