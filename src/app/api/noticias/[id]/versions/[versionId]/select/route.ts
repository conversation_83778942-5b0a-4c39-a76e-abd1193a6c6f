import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; versionId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const noticiaId = parseInt(resolvedParams.id);
    const versionId = parseInt(resolvedParams.versionId);

    if (isNaN(noticiaId) || isNaN(versionId)) {
      return NextResponse.json({ error: 'IDs inválidos' }, { status: 400 });
    }

    // Verificar que la noticia existe y el usuario tiene permisos
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId },
      include: { user: true }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar permisos (admin, editor, o propietario)
    const isAdmin = session.user?.role === 'ADMIN';
    const isEditor = session.user?.role === 'EDITOR';
    const isOwner = noticia.userId === parseInt(session.user?.id || '0');

    if (!isAdmin && !isEditor && !isOwner) {
      return NextResponse.json({ error: 'Sin permisos para seleccionar versiones' }, { status: 403 });
    }

    // Verificar que la versión existe y pertenece a esta noticia
    const version = await prisma.versionNoticia.findFirst({
      where: {
        id: versionId,
        noticiaId: noticiaId
      }
    });

    if (!version) {
      return NextResponse.json({ error: 'Versión no encontrada' }, { status: 404 });
    }

    // Usar transacción para actualizar la noticia y las versiones
    await prisma.$transaction(async (tx) => {
      // TODO: Implementar campo isSelected en el esquema de VersionNoticia
      // Por ahora, solo actualizamos la noticia con el contenido de la versión

      // Desmarcar todas las versiones como no seleccionadas
      // await tx.versionNoticia.updateMany({
      //   where: { noticiaId: noticiaId },
      //   data: { isSelected: false }
      // });

      // Marcar la versión seleccionada
      // await tx.versionNoticia.update({
      //   where: { id: versionId },
      //   data: { isSelected: true }
      // });

      // Actualizar la noticia con el contenido de la versión seleccionada
      await tx.noticia.update({
        where: { id: noticiaId },
        data: {
          titulo: version.titulo,
          subtitulo: version.subtitulo || null,
          contenido: version.contenido,
          resumen: version.resumen || null,
          volanta: version.volanta || null,
          updatedAt: new Date()
        }
      });
    });

    return NextResponse.json({
      message: 'Versión seleccionada exitosamente',
      versionId: versionId
    });

  } catch (error) {
    console.error('Error al seleccionar versión:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
