import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { EstadoPublicacion } from '@prisma/client';

// GET /api/noticias/[id]/publication-status - Obtener estado de publicaciones
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const noticiaId = parseInt(idParam);

    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    // Obtener todas las publicaciones exitosas de esta noticia
    const publicaciones = await prisma.publicacionExterna.findMany({
      where: {
        noticiaId,
        estado: EstadoPublicacion.EXITOSO
      },
      include: {
        diarioExterno: {
          select: {
            id: true,
            nombre: true,
            urlBase: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Procesar metadatos para obtener información de versiones
    const publicacionesConInfo = publicaciones.map(pub => {
      const metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
      return {
        id: pub.id,
        diarioExternoId: pub.diarioExternoId,
        diarioNombre: pub.diarioExterno.nombre,
        urlPublicacion: pub.urlPublicacion,
        fechaPublicacion: pub.createdAt,
        versionId: metadatos.versionId || null,
        tipoPublicacion: metadatos.tipoPublicacion || 'noticia_original'
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        noticiaId,
        publicaciones: publicacionesConInfo
      }
    });

  } catch (error) {
    console.error('Error obteniendo estado de publicaciones:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
