import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// POST - Publicar versiones de IA
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const noticiaId = parseInt(id);
    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { tipo, versionesIds } = body;

    if (!versionesIds || !Array.isArray(versionesIds) || versionesIds.length === 0) {
      return NextResponse.json(
        { error: 'IDs de versiones requeridos' },
        { status: 400 }
      );
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId }
    });

    if (!noticia) {
      return NextResponse.json({ error: 'Noticia no encontrada' }, { status: 404 });
    }

    // Verificar que todas las versiones existen y pertenecen a la noticia
    const versiones = await prisma.versionNoticia.findMany({
      where: {
        id: { in: versionesIds },
        noticiaId: noticiaId
      },
      include: {
        diario: {
          select: {
            id: true,
            nombre: true
          }
        }
      }
    });

    if (versiones.length !== versionesIds.length) {
      return NextResponse.json(
        { error: 'Algunas versiones no fueron encontradas' },
        { status: 404 }
      );
    }

    // Verificar que las versiones están en estado válido para publicar
    const versionesInvalidas = versiones.filter(v => 
      !['APROBADA', 'GENERADA'].includes(v.estado)
    );

    if (versionesInvalidas.length > 0) {
      return NextResponse.json(
        { 
          error: 'Algunas versiones no están en estado válido para publicar',
          versionesInvalidas: versionesInvalidas.map(v => ({
            id: v.id,
            diario: v.diario.nombre,
            estado: v.estado
          }))
        },
        { status: 400 }
      );
    }

    // Publicar las versiones
    const versionesActualizadas = await Promise.all(
      versiones.map(async (version) => {
        const versionActualizada = await prisma.versionNoticia.update({
          where: { id: version.id },
          data: {
            estadoPublicacion: 'PUBLICADA',
            urlPublicacion: `${process.env.NEXT_PUBLIC_BASE_URL}/versiones/${version.id}`,
            updatedAt: new Date()
          },
          include: {
            diario: {
              select: {
                id: true,
                nombre: true
              }
            }
          }
        });

        // Log de la publicación (sin crear registro de actividad por ahora)
        console.log(`Versión ${version.id} publicada para ${version.diario.nombre} ${tipo === 'inmediata' ? 'inmediatamente' : 'programada'}`);

        return versionActualizada;
      })
    );

    return NextResponse.json({
      message: `${versionesActualizadas.length} versiones publicadas exitosamente`,
      versiones: versionesActualizadas
    });

  } catch (error) {
    console.error('Error al publicar versiones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
