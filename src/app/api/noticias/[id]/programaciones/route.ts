import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Obtener programaciones de una noticia
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const noticiaId = parseInt(id);

    if (isNaN(noticiaId)) {
      return NextResponse.json({ error: 'ID de noticia inválido' }, { status: 400 });
    }

    // Obtener programaciones de la noticia
    const programaciones = await prisma.programacionPublicacion.findMany({
      where: { noticiaId },
      include: {
        noticia: {
          select: { titulo: true }
        },
        version: {
          select: {
            titulo: true,
            diario: { select: { nombre: true } }
          }
        },
        diarioExterno: {
          select: { nombre: true }
        }
      },
      orderBy: { fechaPublicacion: 'desc' }
    });

    return NextResponse.json(programaciones);
  } catch (error) {
    console.error('Error al obtener programaciones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
