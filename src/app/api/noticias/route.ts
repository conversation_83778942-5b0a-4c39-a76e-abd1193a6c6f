import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { ImageUploadService } from '@/lib/image-upload-service';

// GET /api/noticias - Obtener todas las noticias
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const categoria = searchParams.get('categoria');
    const estado = searchParams.get('estado');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Construir filtros
    const where: any = {};
    
    if (categoria) {
      where.categoriaId = parseInt(categoria);
    }
    
    if (estado) {
      where.estado = estado;
    }
    
    if (search) {
      where.OR = [
        { titulo: { contains: search, mode: 'insensitive' } },
        { volanta: { contains: search, mode: 'insensitive' } },
        { contenido: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Obtener noticias con relaciones incluyendo versiones de IA
    const noticias = await prisma.noticia.findMany({
      where,
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        versiones: {
          include: {
            diario: {
              select: {
                id: true,
                nombre: true,
                descripcion: true,
              },
            },
            usuario: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Contar total para paginación
    const total = await prisma.noticia.count({ where });

    return NextResponse.json({
      noticias,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error al obtener noticias:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función helper para timeout
function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`Timeout después de ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

// POST /api/noticias - Crear nueva noticia
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('🚀 [INICIO] Creación de noticia iniciada');

    const session = await getServerSession(authOptions);
    console.log(`⏱️ [${Date.now() - startTime}ms] Sesión obtenida`);

    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('Session user ID:', session.user.id, 'Type:', typeof session.user.id);

    // Verificar si es FormData o JSON
    const contentType = request.headers.get('content-type');
    console.log(`⏱️ [${Date.now() - startTime}ms] Content-Type: ${contentType}`);

    let formData: any = {};
    let uploadResult: any = null;

    if (contentType?.includes('multipart/form-data')) {
      // Manejar FormData
      console.log(`⏱️ [${Date.now() - startTime}ms] Iniciando procesamiento de FormData`);
      const formDataObj = await withTimeout(
        request.formData(),
        15000 // 15 segundos timeout para FormData
      );
      console.log(`⏱️ [${Date.now() - startTime}ms] FormData obtenido`);

      // Procesar archivo de imagen si existe
      const imageFile = formDataObj.get('imagen') as File;
      if (imageFile && imageFile.size > 0) {
        console.log(`⏱️ [${Date.now() - startTime}ms] 📤 Procesando archivo de imagen:`, {
          name: imageFile.name,
          size: imageFile.size,
          type: imageFile.type
        });

        console.log(`⏱️ [${Date.now() - startTime}ms] Iniciando subida de imagen...`);

        // Optimización: Reducir timeout y procesar más rápido
        uploadResult = await withTimeout(
          ImageUploadService.uploadImage(imageFile),
          10000 // 10 segundos timeout para subida de imagen
        );
        console.log(`⏱️ [${Date.now() - startTime}ms] Subida de imagen completada`);

        if (!uploadResult.success) {
          return NextResponse.json(
            { error: `Error al subir imagen: ${uploadResult.error}` },
            { status: 400 }
          );
        }

        console.log('✅ Imagen subida exitosamente:', uploadResult.imagenUrl);
      }

      formData = {
        volanta: formDataObj.get('volanta') as string,
        titulo: formDataObj.get('titulo') as string,
        resumen: formDataObj.get('resumen') as string,
        contenido: formDataObj.get('contenido') as string,
        imagenUrl: uploadResult?.imagenUrl || (formDataObj.get('imagenUrl') as string),
        categoriaId: formDataObj.get('categoriaId') as string,
        diariosSeleccionados: formDataObj.get('diariosSeleccionados') as string,
      };
    } else {
      // Manejar JSON
      console.log(`⏱️ [${Date.now() - startTime}ms] Iniciando procesamiento de JSON`);
      formData = await withTimeout(
        request.json(),
        5000 // 5 segundos timeout para JSON
      );
      console.log(`⏱️ [${Date.now() - startTime}ms] JSON obtenido`);
    }

    console.log('Form data received:', formData);

    const {
      volanta,
      titulo,
      resumen,
      contenido,
      imagenUrl,
      categoriaId,
      diariosSeleccionados,
    } = formData;

    // Parsear diarios seleccionados si vienen como string
    let diariosIds: number[] = [];
    if (diariosSeleccionados) {
      try {
        diariosIds = typeof diariosSeleccionados === 'string'
          ? JSON.parse(diariosSeleccionados)
          : diariosSeleccionados;
      } catch (error) {
        console.error('Error parsing diariosSeleccionados:', error);
      }
    }

    // Validaciones básicas
    if (!titulo || !contenido) {
      return NextResponse.json(
        { error: 'Título y contenido son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id: parseInt(session.user.id) }
    });

    if (!user) {
      console.log('User not found with ID:', session.user.id);
      return NextResponse.json(
        { error: 'Usuario no encontrado. Por favor, cierre sesión e inicie sesión nuevamente.' },
        { status: 401 }
      );
    }

    console.log('User found:', user.id, user.email);

    // Verificar que la categoría existe si se proporciona
    if (categoriaId) {
      const categoria = await prisma.categoria.findUnique({
        where: { id: parseInt(categoriaId) }
      });

      if (!categoria) {
        console.log('Category not found with ID:', categoriaId);
        return NextResponse.json(
          { error: 'Categoría no encontrada' },
          { status: 404 }
        );
      }

      console.log('Category found:', categoria.id, categoria.nombre);
    }

    const dataToCreate = {
      volanta,
      titulo,
      resumen,
      contenido,
      imagenUrl,
      categoriaId: categoriaId ? parseInt(categoriaId) : null,
      estado: 'BORRADOR' as const,
      userId: parseInt(session.user.id),
    };

    console.log(`⏱️ [${Date.now() - startTime}ms] Data to create:`, dataToCreate);

    // Crear la noticia
    console.log(`⏱️ [${Date.now() - startTime}ms] Iniciando creación en base de datos...`);
    const noticia = await withTimeout(
      prisma.noticia.create({
        data: dataToCreate,
        include: {
          categoria: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      }),
      5000 // 5 segundos timeout para creación en BD
    );

    console.log(`⏱️ [${Date.now() - startTime}ms] ✅ Noticia created successfully:`, noticia.id);

    // NOTA: Generación de versiones deshabilitada para evitar timeouts
    // Las versiones se pueden generar manualmente desde la página de revisión
    if (diariosIds.length > 0) {
      console.log('📝 Noticia creada para diarios:', diariosIds);
      console.log('💡 Las versiones se pueden generar manualmente desde /noticias/' + noticia.id + '/revision');
    }

    console.log(`⏱️ [${Date.now() - startTime}ms] 🎉 Proceso completado - retornando respuesta`);

    // Agregar información sobre el tiempo de procesamiento
    const processingTime = Date.now() - startTime;
    const response = {
      ...noticia,
      _meta: {
        processingTime: `${processingTime}ms`,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV
      }
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('Error al crear noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 