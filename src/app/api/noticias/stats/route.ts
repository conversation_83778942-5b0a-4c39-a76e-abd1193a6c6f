import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/noticias/stats - Obtener estadísticas de noticias
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener estadísticas generales
    const [
      totalNoticias,
      noticiasPublicadas,
      noticiasBorrador,
      noticiasEnRevision,
      noticiasAprobadas,
      noticiasDestacadas,
      noticiasWebhook,
      noticiasPorCategoria,
    ] = await Promise.all([
      // Total de noticias
      prisma.noticia.count(),
      
      // Noticias publicadas
      prisma.noticia.count({ where: { publicada: true } }),
      
      // Noticias en borrador
      prisma.noticia.count({ where: { estado: 'BORRADOR' } }),
      
      // Noticias en revisión
      prisma.noticia.count({ where: { estado: 'EN_REVISION' } }),
      
      // Noticias aprobadas
      prisma.noticia.count({ where: { estado: 'APROBADA' } }),
      
      // Noticias destacadas
      prisma.noticia.count({ where: { destacada: true } }),

      // Noticias recibidas vía webhook
      prisma.noticia.count({ where: { origen: 'WEBHOOK' } }),

      // Noticias por categoría
      prisma.noticia.groupBy({
        by: ['categoriaId'],
        _count: { categoriaId: true },
        where: { categoria: { isActive: true } },
      }),
    ]);

    // Obtener nombres de categorías para las estadísticas
    const categorias = await prisma.categoria.findMany({
      where: { isActive: true },
      select: { id: true, nombre: true, color: true },
    });

    // Mapear estadísticas por categoría con nombres
    const noticiasPorCategoriaConNombres = noticiasPorCategoria.map(stat => {
      const categoria = categorias.find(c => c.id === stat.categoriaId);
      return {
        categoriaId: stat.categoriaId,
        categoriaNombre: categoria?.nombre || 'Sin categoría',
        categoriaColor: categoria?.color || '#6B7280',
        count: stat._count.categoriaId,
      };
    });

    // Noticias recientes (últimas 5)
    const noticiasRecientes = await prisma.noticia.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        categoria: {
          select: { nombre: true, color: true },
        },
        user: {
          select: { name: true },
        },
      },
    });

    // Noticias recientes de webhook (últimas 3)
    const noticiasWebhookRecientes = await prisma.noticia.findMany({
      where: { origen: 'WEBHOOK' },
      take: 3,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        titulo: true,
        periodista: true,
        estado: true,
        createdAt: true,
        categoria: {
          select: { nombre: true, color: true },
        },
      },
    });

    const stats = {
      total: totalNoticias,
      publicadas: noticiasPublicadas,
      borrador: noticiasBorrador,
      enRevision: noticiasEnRevision,
      aprobadas: noticiasAprobadas,
      destacadas: noticiasDestacadas,
      webhook: noticiasWebhook,
      porCategoria: noticiasPorCategoriaConNombres,
      recientes: noticiasRecientes,
      webhookRecientes: noticiasWebhookRecientes,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error al obtener estadísticas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 