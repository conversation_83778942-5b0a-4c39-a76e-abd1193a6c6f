/**
 * API endpoint de prueba para verificar el endpoint de estados de importación
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🧪 PROBANDO ENDPOINT DE ESTADOS DE IMPORTACIÓN...');

    // Probar con IDs que sabemos que existen
    const idsParaProbar = [1, 3, 4, 6, 999]; // Los primeros 4 existen, 999 no existe

    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/noticias-nacionales/estado-importacion`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || ''
      },
      body: JSON.stringify({ idsExternos: idsParaProbar }),
    });

    const data = await response.json();

    console.log('📊 Respuesta del endpoint de estados:', JSON.stringify(data, null, 2));

    return NextResponse.json({
      success: true,
      data: {
        endpointResponse: data,
        statusCode: response.status,
        idsConsultados: idsParaProbar
      }
    });

  } catch (error) {
    console.error('❌ Error en prueba de estados:', error);
    
    return NextResponse.json(
      { 
        error: 'Error en prueba de estados',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
