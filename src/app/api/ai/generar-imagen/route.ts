import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { AIImageService } from '@/lib/ai-image-service';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { titulo, resumen, contenido, estilo, configuracionId, noticiaId } = body;

    // Validaciones
    if (!titulo) {
      return NextResponse.json({ error: 'El título es requerido' }, { status: 400 });
    }

    // Generar imagen
    const resultado = await AIImageService.generarImagen(
      {
        titulo,
        resumen,
        contenido,
        estilo,
        configuracionId,
      },
      parseInt(session.user.id),
      noticiaId
    );

    return NextResponse.json({
      success: true,
      data: resultado,
    });

  } catch (error) {
    console.error('Error en API de generación de imagen:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Error interno del servidor',
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tipo = searchParams.get('tipo');
    const limite = parseInt(searchParams.get('limite') || '20');

    if (tipo === 'historial') {
      const historial = await AIImageService.obtenerHistorial(parseInt(session.user.id), limite);
      return NextResponse.json({ data: historial });
    }

    if (tipo === 'estadisticas') {
      const estadisticas = await AIImageService.obtenerEstadisticas(parseInt(session.user.id));
      return NextResponse.json({ data: estadisticas });
    }

    return NextResponse.json({ error: 'Tipo de consulta no válido' }, { status: 400 });

  } catch (error) {
    console.error('Error en API de consulta de generaciones:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Error interno del servidor',
    }, { status: 500 });
  }
}
