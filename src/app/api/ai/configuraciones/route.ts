import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener configuraciones activas (sin información sensible como API keys)
    const configuraciones = await prisma.configuracionIA.findMany({
      where: { activo: true },
      select: {
        id: true,
        nombre: true,
        proveedor: true,
        modelo: true,
        activo: true,
        limitesUso: true,
        // No incluir apiKey por seguridad
      },
      orderBy: { createdAt: 'asc' },
    });

    return NextResponse.json({ data: configuraciones });

  } catch (error) {
    console.error('Error al obtener configuraciones de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
