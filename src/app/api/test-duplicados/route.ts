/**
 * API endpoint de prueba para verificar el sistema de duplicados
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🔍 INICIANDO PRUEBA DE DUPLICADOS...');

    // 1. Buscar todas las noticias que tienen webhookData
    const noticiasConWebhookData = await prisma.noticia.findMany({
      where: {
        webhookData: {
          not: null
        }
      },
      select: {
        id: true,
        titulo: true,
        webhookData: true,
        createdAt: true,
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    console.log(`📊 Encontradas ${noticiasConWebhookData.length} noticias con webhookData`);

    // 2. Analizar los webhookData
    const analisis = noticiasConWebhookData.map(noticia => {
      const webhookData = noticia.webhookData as any;
      return {
        id: noticia.id,
        titulo: noticia.titulo,
        webhookData: webhookData,
        idOriginal: webhookData?.idOriginal,
        importada: webhookData?.importada,
        fuenteExterna: webhookData?.fuenteExterna,
        importadaPor: noticia.user?.name,
        fechaCreacion: noticia.createdAt
      };
    });

    console.log('📋 Análisis de webhookData:', JSON.stringify(analisis, null, 2));

    // 3. Probar búsqueda por idOriginal específico
    const idOriginalPrueba = 1002; // ID del modo demo
    console.log(`🔍 Buscando noticia con idOriginal: ${idOriginalPrueba}`);

    const noticiaPorIdOriginal = await prisma.noticia.findFirst({
      where: {
        webhookData: {
          path: ['idOriginal'],
          equals: idOriginalPrueba
        } as any
      },
      select: {
        id: true,
        titulo: true,
        webhookData: true
      }
    });

    console.log('📊 Resultado búsqueda por idOriginal:', noticiaPorIdOriginal);

    // 4. Probar diferentes formas de búsqueda
    const busquedaAlternativa = await prisma.noticia.findMany({
      where: {
        webhookData: {
          string_contains: `"idOriginal":${idOriginalPrueba}`
        } as any
      },
      select: {
        id: true,
        titulo: true,
        webhookData: true
      }
    });

    console.log('📊 Búsqueda alternativa:', busquedaAlternativa);

    return NextResponse.json({
      success: true,
      data: {
        totalNoticiasConWebhookData: noticiasConWebhookData.length,
        analisis: analisis,
        busquedaPorIdOriginal: noticiaPorIdOriginal,
        busquedaAlternativa: busquedaAlternativa,
        idOriginalPrueba: idOriginalPrueba
      }
    });

  } catch (error) {
    console.error('❌ Error en prueba de duplicados:', error);
    
    return NextResponse.json(
      { 
        error: 'Error en prueba de duplicados',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
