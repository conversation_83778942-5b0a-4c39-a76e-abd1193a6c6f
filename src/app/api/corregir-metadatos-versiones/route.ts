import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 Iniciando corrección de metadatos de versiones...');

    // Obtener publicaciones exitosas que tienen versionId pero no tituloOriginal
    const publicacionesParaCorregir = await prisma.publicacionExterna.findMany({
      where: { 
        estado: 'EXITOSO',
        metadatos: {
          contains: 'versionId'
        }
      },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50 // Procesar máximo 50 a la vez
    });

    console.log(`📊 Encontradas ${publicacionesParaCorregir.length} publicaciones para analizar`);

    const resultados = [];
    let corregidas = 0;
    let errores = 0;

    for (const pub of publicacionesParaCorregir) {
      try {
        // Parsear metadatos existentes
        let metadatos: any = {};
        try {
          metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
        } catch (e) {
          console.warn(`Error parsing metadatos for publicacion ${pub.id}:`, e);
          errores++;
          continue;
        }

        // Verificar si ya tiene título en metadatos
        if (metadatos.tituloOriginal || metadatos.tituloPublicado) {
          console.log(`✅ Publicación ${pub.id} ya tiene título en metadatos`);
          continue;
        }

        // Verificar si tiene versionId
        const versionId = metadatos.versionId;
        if (!versionId) {
          console.log(`⚠️ Publicación ${pub.id} no tiene versionId`);
          continue;
        }

        console.log(`🔍 Procesando publicación ${pub.id} con versionId ${versionId}`);

        // Obtener la versión de la noticia
        const version = await prisma.versionNoticia.findFirst({
          where: {
            id: parseInt(versionId),
            noticiaId: pub.noticiaId
          },
          include: {
            diario: {
              select: { nombre: true }
            }
          }
        });

        if (!version) {
          console.log(`❌ No se encontró versión ${versionId} para publicación ${pub.id}`);
          errores++;
          continue;
        }

        // Actualizar metadatos con información de la versión
        const metadatosActualizados = {
          ...metadatos,
          tituloOriginal: version.titulo,
          tituloPublicado: version.titulo,
          esVersion: true,
          versionDiario: version.diario?.nombre || null,
          corregidoAutomaticamente: true,
          fechaCorreccion: new Date().toISOString(),
          corregidoPor: session.user.email,
          datosVersion: {
            titulo: version.titulo,
            subtitulo: version.subtitulo,
            volanta: version.volanta,
            diario: version.diario?.nombre
          }
        };

        // Actualizar en la base de datos
        await prisma.publicacionExterna.update({
          where: { id: pub.id },
          data: {
            metadatos: JSON.stringify(metadatosActualizados)
          }
        });

        console.log(`✅ Corregida publicación ${pub.id}: "${version.titulo}"`);
        
        resultados.push({
          id: pub.id,
          noticiaId: pub.noticiaId,
          versionId: versionId,
          tituloAnterior: pub.noticia.titulo,
          tituloNuevo: version.titulo,
          diario: version.diario?.nombre,
          estado: 'corregida'
        });

        corregidas++;

      } catch (error) {
        console.error(`❌ Error procesando publicación ${pub.id}:`, error);
        errores++;
        
        resultados.push({
          id: pub.id,
          error: error instanceof Error ? error.message : 'Error desconocido',
          estado: 'error'
        });
      }
    }

    console.log(`🎉 Corrección completada: ${corregidas} corregidas, ${errores} errores`);

    return NextResponse.json({
      success: true,
      message: `Corrección completada: ${corregidas} publicaciones corregidas`,
      estadisticas: {
        analizadas: publicacionesParaCorregir.length,
        corregidas: corregidas,
        errores: errores,
        porcentajeExito: Math.round((corregidas / publicacionesParaCorregir.length) * 100)
      },
      resultados: resultados
    });

  } catch (error) {
    console.error('❌ Error en corrección de metadatos:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Análisis previo sin hacer cambios
    const publicacionesParaCorregir = await prisma.publicacionExterna.findMany({
      where: { 
        estado: 'EXITOSO',
        metadatos: {
          contains: 'versionId'
        }
      },
      select: {
        id: true,
        noticiaId: true,
        metadatos: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    });

    let conVersionId = 0;
    let sinTitulo = 0;
    let yaCorregidas = 0;

    for (const pub of publicacionesParaCorregir) {
      try {
        const metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
        
        if (metadatos.versionId) {
          conVersionId++;
          
          if (!metadatos.tituloOriginal && !metadatos.tituloPublicado) {
            sinTitulo++;
          } else {
            yaCorregidas++;
          }
        }
      } catch (e) {
        // Error de parsing
      }
    }

    return NextResponse.json({
      success: true,
      analisis: {
        totalPublicaciones: publicacionesParaCorregir.length,
        conVersionId: conVersionId,
        sinTitulo: sinTitulo,
        yaCorregidas: yaCorregidas,
        necesitanCorreccion: sinTitulo
      },
      mensaje: sinTitulo > 0 
        ? `${sinTitulo} publicaciones necesitan corrección de metadatos`
        : 'Todas las publicaciones tienen metadatos correctos'
    });

  } catch (error) {
    console.error('❌ Error en análisis previo:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
