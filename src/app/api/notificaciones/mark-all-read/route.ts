import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/notificaciones/mark-all-read - Marcar todas las notificaciones como leídas
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Marcar todas las notificaciones no leídas del usuario como leídas
    const result = await prisma.notificacion.updateMany({
      where: {
        userId: parseInt(session.user.id),
        leida: false
      },
      data: {
        leida: true,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      message: 'Todas las notificaciones marcadas como leídas',
      count: result.count
    });

  } catch (error) {
    console.error('Error al marcar todas las notificaciones como leídas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
