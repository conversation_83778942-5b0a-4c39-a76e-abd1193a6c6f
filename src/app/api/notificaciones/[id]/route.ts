import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// PATCH /api/notificaciones/[id] - Marcar notificación como leída/no leída
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const notificacionId = parseInt(id);
    const body = await request.json();
    const { leida } = body;

    // Verificar que la notificación pertenece al usuario
    const notificacion = await prisma.notificacion.findFirst({
      where: {
        id: notificacionId,
        userId: parseInt(session.user.id)
      }
    });

    if (!notificacion) {
      return NextResponse.json(
        { error: 'Notificación no encontrada' },
        { status: 404 }
      );
    }

    // Actualizar estado de lectura
    const notificacionActualizada = await prisma.notificacion.update({
      where: { id: notificacionId },
      data: { 
        leida: leida !== undefined ? leida : true,
        updatedAt: new Date()
      },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            periodista: true,
            estado: true
          }
        }
      }
    });

    return NextResponse.json(notificacionActualizada);

  } catch (error) {
    console.error('Error al actualizar notificación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/notificaciones/[id] - Eliminar notificación
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const notificacionId = parseInt(id);

    // Verificar que la notificación pertenece al usuario
    const notificacion = await prisma.notificacion.findFirst({
      where: {
        id: notificacionId,
        userId: parseInt(session.user.id)
      }
    });

    if (!notificacion) {
      return NextResponse.json(
        { error: 'Notificación no encontrada' },
        { status: 404 }
      );
    }

    // Eliminar notificación
    await prisma.notificacion.delete({
      where: { id: notificacionId }
    });

    return NextResponse.json({ message: 'Notificación eliminada' });

  } catch (error) {
    console.error('Error al eliminar notificación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
