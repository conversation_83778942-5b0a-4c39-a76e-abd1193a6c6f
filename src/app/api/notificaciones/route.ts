import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/notificaciones - Obtener notificaciones del usuario actual
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const onlyUnread = searchParams.get('unread') === 'true';

    // Construir filtros
    const where: any = {
      userId: parseInt(session.user.id)
    };

    if (onlyUnread) {
      where.leida = false;
    }

    // Obtener notificaciones
    const notificaciones = await prisma.notificacion.findMany({
      where,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            periodista: true,
            estado: true,
            categoria: {
              select: {
                nombre: true,
                color: true
              }
            }
          }
        }
      }
    });

    // Contar notificaciones no leídas
    const unreadCount = await prisma.notificacion.count({
      where: {
        userId: parseInt(session.user.id),
        leida: false
      }
    });

    return NextResponse.json({
      notificaciones,
      unreadCount,
      total: notificaciones.length
    });

  } catch (error) {
    console.error('Error al obtener notificaciones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/notificaciones - Crear nueva notificación (uso interno)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { tipo, titulo, mensaje, noticiaId, userId, metadata } = body;

    // Validar campos requeridos
    if (!tipo || !titulo) {
      return NextResponse.json(
        { error: 'Campos requeridos: tipo, titulo' },
        { status: 400 }
      );
    }

    // Crear notificación
    const notificacion = await prisma.notificacion.create({
      data: {
        tipo,
        titulo,
        mensaje,
        noticiaId: noticiaId ? parseInt(noticiaId) : null,
        userId: userId ? parseInt(userId) : parseInt(session.user.id),
        metadata
      },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            periodista: true,
            estado: true
          }
        }
      }
    });

    return NextResponse.json(notificacion, { status: 201 });

  } catch (error) {
    console.error('Error al crear notificación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
