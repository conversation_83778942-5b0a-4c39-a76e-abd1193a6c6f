/**
 * API endpoint de prueba para verificar la importación
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { verificarNoticiaYaImportada } from '@/lib/importacion-service';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🧪 INICIANDO PRUEBA DE IMPORTACIÓN...');

    // Probar con IDs que sabemos que existen
    const idsParaProbar = [1, 3, 4, 6, 999]; // Los primeros 4 existen, 999 no existe

    const resultados = [];

    for (const id of idsParaProbar) {
      console.log(`\n🔍 Probando ID externo: ${id}`);
      const resultado = await verificarNoticiaYaImportada(id);
      console.log(`📊 Resultado para ID ${id}:`, resultado);
      
      resultados.push({
        idExterno: id,
        resultado: resultado
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        resultados: resultados,
        resumen: {
          total: idsParaProbar.length,
          yaImportados: resultados.filter(r => r.resultado.yaImportada).length,
          disponibles: resultados.filter(r => !r.resultado.yaImportada).length
        }
      }
    });

  } catch (error) {
    console.error('❌ Error en prueba de importación:', error);
    
    return NextResponse.json(
      { 
        error: 'Error en prueba de importación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
