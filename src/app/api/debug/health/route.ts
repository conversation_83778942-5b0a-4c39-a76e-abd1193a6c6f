import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/debug/health - Endpoint público para verificar el estado del sistema
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 HEALTH CHECK: Verificando estado del sistema...');

    // Verificar conexión a la base de datos
    let dbStatus = 'ERROR';
    let dbError = null;
    try {
      await prisma.$queryRaw`SELECT 1`;
      dbStatus = 'OK';
      console.log('✅ HEALTH: Base de datos conectada');
    } catch (error) {
      dbError = error instanceof Error ? error.message : 'Error desconocido';
      console.error('❌ HEALTH: Error de base de datos:', dbError);
    }

    // Verificar variables de entorno críticas
    const envVars = {
      NODE_ENV: process.env.NODE_ENV || 'undefined',
      DATABASE_URL: process.env.DATABASE_URL ? 'CONFIGURED' : 'MISSING',
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'CONFIGURED' : 'MISSING',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'MISSING',
      CRON_SECRET: process.env.CRON_SECRET ? 'CONFIGURED' : 'MISSING',
      WEBHOOK_TOKEN: process.env.WEBHOOK_TOKEN ? 'CONFIGURED' : 'MISSING',
      OPENAI_API_KEY: process.env.OPENAI_API_KEY ? 'CONFIGURED' : 'MISSING',
      GEMINI_API_KEY: process.env.GEMINI_API_KEY ? 'CONFIGURED' : 'MISSING',
    };

    // Contar registros básicos
    let counts = {
      users: 0,
      noticias: 0,
      programaciones: 0
    };

    if (dbStatus === 'OK') {
      try {
        const [userCount, noticiasCount, programacionesCount] = await Promise.all([
          prisma.user.count(),
          prisma.noticia.count(),
          prisma.programacionPublicacion.count()
        ]);
        
        counts = {
          users: userCount,
          noticias: noticiasCount,
          programaciones: programacionesCount
        };
      } catch (error) {
        console.error('❌ HEALTH: Error contando registros:', error);
      }
    }

    const result = {
      status: dbStatus === 'OK' ? 'HEALTHY' : 'UNHEALTHY',
      timestamp: new Date().toISOString(),
      database: {
        status: dbStatus,
        error: dbError
      },
      environment: envVars,
      counts,
      version: '2.0.0'
    };

    console.log('📊 HEALTH: Estado del sistema:', {
      status: result.status,
      dbStatus,
      missingVars: Object.entries(envVars).filter(([key, value]) => value === 'MISSING').map(([key]) => key)
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ HEALTH: Error general:', error);
    return NextResponse.json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Error desconocido',
      database: { status: 'ERROR' },
      environment: {},
      counts: {},
      version: '2.0.0'
    }, { status: 500 });
  }
}
