import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 DEBUG: Analizando metadatos de publicaciones...');

    // Obtener publicaciones exitosas con metadatos
    const publicaciones = await prisma.publicacionExterna.findMany({
      where: { estado: 'EXITOSO' },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`📊 Analizando ${publicaciones.length} publicaciones exitosas`);

    const analisis = publicaciones.map(pub => {
      let metadatos: any = {};
      let errorParsing = null;
      
      try {
        metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
      } catch (e) {
        errorParsing = e instanceof Error ? e.message : 'Error desconocido';
        console.warn(`Error parsing metadatos for publicacion ${pub.id}:`, e);
      }

      // Analizar qué campos tiene
      const camposMetadatos = Object.keys(metadatos);
      
      // Determinar título a usar
      const tituloOriginal = pub.noticia.titulo;
      const tituloEnMetadatos = metadatos.tituloOriginal || metadatos.tituloPublicado;
      const tituloFinal = tituloEnMetadatos || tituloOriginal;
      
      return {
        id: pub.id,
        noticiaId: pub.noticiaId,
        tituloNoticia: tituloOriginal,
        tituloEnMetadatos: tituloEnMetadatos,
        tituloFinal: tituloFinal,
        esVersion: metadatos.esVersion || false,
        versionId: metadatos.versionId || null,
        versionDiario: metadatos.versionDiario || null,
        camposMetadatos: camposMetadatos,
        metadatosCompletos: metadatos,
        metadatosRaw: pub.metadatos,
        errorParsing: errorParsing,
        fechaCreacion: pub.createdAt,
        urlPublicacion: pub.urlPublicacion
      };
    });

    // Estadísticas del análisis
    const conMetadatos = analisis.filter(a => a.metadatosRaw);
    const conTituloEnMetadatos = analisis.filter(a => a.tituloEnMetadatos);
    const versiones = analisis.filter(a => a.esVersion);
    const conErrores = analisis.filter(a => a.errorParsing);

    const estadisticas = {
      total: analisis.length,
      conMetadatos: conMetadatos.length,
      conTituloEnMetadatos: conTituloEnMetadatos.length,
      versiones: versiones.length,
      conErrores: conErrores.length,
      porcentajeTitulosCorrectos: Math.round((conTituloEnMetadatos.length / analisis.length) * 100)
    };

    console.log('📊 Estadísticas de metadatos:', estadisticas);

    return NextResponse.json({
      success: true,
      estadisticas,
      analisis,
      recomendaciones: [
        conTituloEnMetadatos.length === 0 ? '❌ Ninguna publicación tiene título en metadatos' : '✅ Algunas publicaciones tienen título en metadatos',
        versiones.length === 0 ? '❌ No se detectaron versiones IA' : `✅ ${versiones.length} versiones IA detectadas`,
        conErrores.length > 0 ? `⚠️ ${conErrores.length} publicaciones con errores de parsing` : '✅ No hay errores de parsing'
      ]
    });

  } catch (error) {
    console.error('❌ Error en análisis de metadatos:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST - Actualizar metadatos de una publicación específica
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { publicacionId, nuevoTitulo } = body;

    if (!publicacionId || !nuevoTitulo) {
      return NextResponse.json({ error: 'publicacionId y nuevoTitulo son requeridos' }, { status: 400 });
    }

    console.log(`🔧 Actualizando metadatos de publicación ${publicacionId} con título: "${nuevoTitulo}"`);

    // Obtener publicación actual
    const publicacion = await prisma.publicacionExterna.findUnique({
      where: { id: parseInt(publicacionId) }
    });

    if (!publicacion) {
      return NextResponse.json({ error: 'Publicación no encontrada' }, { status: 404 });
    }

    // Parsear metadatos existentes
    let metadatos = {};
    try {
      metadatos = publicacion.metadatos ? JSON.parse(publicacion.metadatos) : {};
    } catch (e) {
      console.warn('Error parsing metadatos existentes:', e);
    }

    // Actualizar metadatos con nuevo título
    const metadatosActualizados = {
      ...metadatos,
      tituloOriginal: nuevoTitulo,
      tituloPublicado: nuevoTitulo,
      actualizadoManualmente: true,
      fechaActualizacion: new Date().toISOString(),
      actualizadoPor: session.user.email
    };

    // Guardar metadatos actualizados
    const publicacionActualizada = await prisma.publicacionExterna.update({
      where: { id: parseInt(publicacionId) },
      data: {
        metadatos: JSON.stringify(metadatosActualizados)
      }
    });

    console.log(`✅ Metadatos actualizados para publicación ${publicacionId}`);

    return NextResponse.json({
      success: true,
      message: 'Metadatos actualizados exitosamente',
      publicacion: {
        id: publicacionActualizada.id,
        metadatosAnteriores: metadatos,
        metadatosNuevos: metadatosActualizados
      }
    });

  } catch (error) {
    console.error('❌ Error al actualizar metadatos:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
