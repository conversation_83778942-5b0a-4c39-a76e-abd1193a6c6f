import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/debug/programaciones - Debug endpoint para verificar programaciones
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DEBUG: Verificando estado de programaciones...');

    // Verificar que la tabla existe y obtener información básica
    let tableExists = false;
    let programacionesCount = 0;
    let sampleProgramaciones: any[] = [];
    let error: string | null = null;

    try {
      // Intentar contar registros
      programacionesCount = await prisma.programacionPublicacion.count();
      tableExists = true;
      console.log(`✅ DEBUG: Tabla existe, ${programacionesCount} programaciones encontradas`);

      // Obtener algunas programaciones de muestra
      if (programacionesCount > 0) {
        sampleProgramaciones = await prisma.programacionPublicacion.findMany({
          take: 3,
          select: {
            id: true,
            tipo: true,
            estado: true,
            fechaPublicacion: true,
            noticiaId: true,
            versionId: true,
            diarioExternoId: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        });
      }

    } catch (err) {
      tableExists = false;
      error = err instanceof Error ? err.message : 'Error desconocido';
      console.error('❌ DEBUG: Error accediendo a programaciones:', error);
    }

    // Verificar estructura de la tabla
    let tableStructure: any = null;
    try {
      // Intentar una consulta simple para verificar campos
      const testQuery = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'programacion_publicaciones'
        ORDER BY ordinal_position;
      `;
      tableStructure = testQuery;
    } catch (err) {
      console.log('⚠️ DEBUG: No se pudo obtener estructura de tabla (normal en SQLite)');
    }

    // Verificar relaciones
    let relacionesTest = {
      noticias: false,
      versiones: false,
      diariosExternos: false
    };

    try {
      const noticiasCount = await prisma.noticia.count();
      relacionesTest.noticias = true;
      console.log(`✅ DEBUG: Tabla noticias OK (${noticiasCount} registros)`);
    } catch (err) {
      console.error('❌ DEBUG: Error en tabla noticias:', err);
    }

    try {
      const versionesCount = await prisma.versionNoticia.count();
      relacionesTest.versiones = true;
      console.log(`✅ DEBUG: Tabla versiones OK (${versionesCount} registros)`);
    } catch (err) {
      console.error('❌ DEBUG: Error en tabla versiones:', err);
    }

    try {
      const diariosExternosCount = await prisma.diarioExterno.count();
      relacionesTest.diariosExternos = true;
      console.log(`✅ DEBUG: Tabla diarios externos OK (${diariosExternosCount} registros)`);
    } catch (err) {
      console.error('❌ DEBUG: Error en tabla diarios externos:', err);
    }

    const result = {
      success: true,
      timestamp: new Date().toISOString(),
      tabla: {
        existe: tableExists,
        registros: programacionesCount,
        error: error
      },
      estructura: tableStructure,
      relaciones: relacionesTest,
      muestras: sampleProgramaciones,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        DATABASE_URL: process.env.DATABASE_URL ? 'CONFIGURED' : 'MISSING'
      }
    };

    console.log('📊 DEBUG: Resultado completo:', {
      tableExists,
      programacionesCount,
      relacionesOK: Object.values(relacionesTest).every(Boolean)
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ DEBUG: Error general:', error);
    return NextResponse.json({
      success: false,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Error desconocido',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
