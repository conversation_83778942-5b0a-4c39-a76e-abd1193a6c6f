import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('📰 CREANDO NOTICIA DE PRUEBA...');

    // Obtener usuario admin
    const usuario = await prisma.user.findFirst({ where: { role: 'ADMIN' } });
    if (!usuario) {
      return NextResponse.json({ error: 'Usuario admin no encontrado' }, { status: 404 });
    }

    // Obtener primera categoría
    const categoria = await prisma.categoria.findFirst({ where: { isActive: true } });
    if (!categoria) {
      return NextResponse.json({ error: 'No se encontraron categorías' }, { status: 404 });
    }

    console.log(`✅ Usuario: ${usuario.email}, Categoría: ${categoria.nombre}`);

    // Crear noticia
    const noticia = await prisma.noticia.create({
      data: {
        titulo: 'Noticia de Prueba - Sistema Restaurado',
        subtitulo: 'Esta es una noticia de prueba para verificar que el sistema funciona correctamente',
        volanta: 'SISTEMA',
        contenido: 'Esta noticia fue creada automáticamente para verificar que el sistema de gestión de noticias funciona correctamente después de la restauración de datos. El sistema incluye todas las funcionalidades necesarias para la gestión completa de contenido periodístico.',
        resumen: 'Noticia de prueba creada para verificar el funcionamiento del sistema.',
        estado: 'PUBLICADA',
        categoriaId: categoria.id,
        userId: usuario.id
      },
      include: {
        categoria: true,
        user: true
      }
    });

    console.log(`✅ Noticia creada: ID ${noticia.id}`);

    // Verificar conteo total
    const totalNoticias = await prisma.noticia.count();
    console.log(`📊 Total de noticias: ${totalNoticias}`);

    return NextResponse.json({
      success: true,
      noticia: {
        id: noticia.id,
        titulo: noticia.titulo,
        categoria: noticia.categoria?.nombre || 'Sin categoría',
        autor: noticia.user?.name || 'Sin autor',
        estado: noticia.estado,
        createdAt: noticia.createdAt
      },
      totalNoticias
    });

  } catch (error) {
    console.error('❌ Error al crear noticia:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor', details: error },
      { status: 500 }
    );
  }
}
