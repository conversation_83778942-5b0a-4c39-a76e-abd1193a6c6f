import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 DEBUG: Iniciando análisis de publicaciones externas...');

    // Obtener todas las publicaciones externas
    const todasLasPublicaciones = await prisma.publicacionExterna.findMany({
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            estado: true,
            categoria: {
              select: { nombre: true, color: true }
            },
            user: {
              select: { name: true }
            }
          }
        },
        diarioExterno: {
          select: {
            id: true,
            nombre: true,
            urlBase: true,
            activo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Total de publicaciones encontradas: ${todasLasPublicaciones.length}`);

    // Agrupar por estado
    const porEstado = todasLasPublicaciones.reduce((acc: any, pub) => {
      acc[pub.estado] = (acc[pub.estado] || 0) + 1;
      return acc;
    }, {});

    console.log('📊 Publicaciones por estado:', porEstado);

    // Filtrar solo las exitosas
    const exitosas = todasLasPublicaciones.filter(pub => pub.estado === 'EXITOSO');
    console.log(`✅ Publicaciones exitosas: ${exitosas.length}`);

    // Verificar campos importantes
    const analisisExitosas = exitosas.map(pub => ({
      id: pub.id,
      noticiaId: pub.noticiaId,
      noticiaTitulo: pub.noticia?.titulo || 'SIN TÍTULO',
      noticiaEstado: pub.noticia?.estado || 'SIN ESTADO',
      diario: pub.diarioExterno?.nombre || 'SIN DIARIO',
      diarioActivo: pub.diarioExterno?.activo || false,
      estado: pub.estado,
      urlPublicacion: pub.urlPublicacion || 'SIN URL',
      fechaCreacion: pub.createdAt,
      fechaActualizacion: pub.updatedAt,
      tieneCategoria: !!pub.noticia?.categoria,
      tieneAutor: !!pub.noticia?.user,
      metadatos: pub.metadatos ? 'SÍ' : 'NO'
    }));

    // Verificar problemas comunes
    const problemas = [];

    if (exitosas.length === 0) {
      problemas.push('❌ No hay publicaciones con estado EXITOSO');
    }

    const sinUrl = exitosas.filter(pub => !pub.urlPublicacion);
    if (sinUrl.length > 0) {
      problemas.push(`⚠️ ${sinUrl.length} publicaciones exitosas sin URL`);
    }

    const sinNoticia = exitosas.filter(pub => !pub.noticia);
    if (sinNoticia.length > 0) {
      problemas.push(`⚠️ ${sinNoticia.length} publicaciones sin noticia asociada`);
    }

    const sinDiario = exitosas.filter(pub => !pub.diarioExterno);
    if (sinDiario.length > 0) {
      problemas.push(`⚠️ ${sinDiario.length} publicaciones sin diario asociado`);
    }

    const diariosInactivos = exitosas.filter(pub => pub.diarioExterno && !pub.diarioExterno.activo);
    if (diariosInactivos.length > 0) {
      problemas.push(`⚠️ ${diariosInactivos.length} publicaciones de diarios inactivos`);
    }

    // Probar la consulta exacta que usa la API de redes sociales
    console.log('🧪 Probando consulta de la API de redes sociales...');
    
    const consultaRedesSociales = await prisma.publicacionExterna.findMany({
      where: { estado: 'EXITOSO' },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            categoria: {
              select: { nombre: true, color: true }
            },
            user: {
              select: { name: true }
            }
          }
        },
        diarioExterno: {
          select: {
            id: true,
            nombre: true,
            urlBase: true,
            activo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 12
    });

    console.log(`🧪 Resultado de consulta redes sociales: ${consultaRedesSociales.length} registros`);

    return NextResponse.json({
      success: true,
      debug: {
        totalPublicaciones: todasLasPublicaciones.length,
        porEstado,
        publicacionesExitosas: exitosas.length,
        problemas,
        consultaRedesSociales: consultaRedesSociales.length,
        analisisExitosas,
        muestraConsultaRedesSociales: consultaRedesSociales.map(pub => ({
          id: pub.id,
          titulo: pub.noticia?.titulo,
          diario: pub.diarioExterno?.nombre,
          estado: pub.estado,
          url: pub.urlPublicacion,
          fechaCreacion: pub.createdAt,
          fechaActualizacion: pub.updatedAt
        }))
      }
    });

  } catch (error) {
    console.error('❌ Error en debug de publicaciones externas:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
