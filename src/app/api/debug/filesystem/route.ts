import { NextRequest, NextResponse } from 'next/server';
import { existsSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

// GET /api/debug/filesystem - Debug del sistema de archivos
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DEBUG: Verificando sistema de archivos...');

    const result = {
      success: true,
      timestamp: new Date().toISOString(),
      workingDirectory: process.cwd(),
      paths: {} as any,
      uploads: {} as any,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        PORT: process.env.PORT,
        HOSTNAME: process.env.HOSTNAME
      },
      system: {} as any
    };

    // Verificar directorios principales
    const pathsToCheck = [
      'public',
      'public/uploads',
      'public/uploads/images',
      '.next',
      'node_modules'
    ];

    for (const pathToCheck of pathsToCheck) {
      const fullPath = join(process.cwd(), pathToCheck);
      const exists = existsSync(fullPath);
      
      result.paths[pathToCheck] = {
        exists,
        fullPath,
        isDirectory: exists ? statSync(fullPath).isDirectory() : false
      };

      if (exists && pathToCheck === 'public/uploads/images') {
        try {
          const files = readdirSync(fullPath);
          result.uploads.imageFiles = files.map(file => {
            const filePath = join(fullPath, file);
            const stats = statSync(filePath);
            return {
              name: file,
              size: stats.size,
              created: stats.birthtime,
              modified: stats.mtime
            };
          });
          result.uploads.totalFiles = files.length;
        } catch (error) {
          result.uploads.error = error instanceof Error ? error.message : 'Error desconocido';
        }
      }

      console.log(`📁 ${pathToCheck}: ${exists ? '✅ Existe' : '❌ No existe'} (${fullPath})`);
    }

    // Verificar permisos de escritura
    try {
      const testFile = join(process.cwd(), 'public', 'uploads', 'images', 'test-write.txt');
      const fs = require('fs').promises;
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);
      result.uploads.writePermissions = true;
      console.log('✅ Permisos de escritura OK');
    } catch (error) {
      result.uploads.writePermissions = false;
      result.uploads.writeError = error instanceof Error ? error.message : 'Error desconocido';
      console.log('❌ Sin permisos de escritura:', error);
    }

    // Información del contenedor/servidor
    try {
      const os = require('os');
      result.system = {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        uptime: os.uptime(),
        memory: {
          total: os.totalmem(),
          free: os.freemem()
        }
      };
    } catch (error) {
      result.system = { error: 'No se pudo obtener información del sistema' };
    }

    console.log('📊 DEBUG: Resultado completo:', {
      uploadsExist: result.paths['public/uploads/images']?.exists,
      totalFiles: result.uploads.totalFiles,
      writePermissions: result.uploads.writePermissions
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ DEBUG: Error general:', error);
    return NextResponse.json({
      success: false,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Error desconocido',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
