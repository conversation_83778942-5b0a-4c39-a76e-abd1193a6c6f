import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Verificando conexión a la base de datos...');
    
    // Verificar variables de entorno
    const databaseUrl = process.env.DATABASE_URL;
    console.log('📊 DATABASE_URL configurada:', databaseUrl ? 'SÍ' : 'NO');
    console.log('📊 DATABASE_URL (parcial):', databaseUrl?.replace(/:[^:@]*@/, ':***@'));
    
    // Intentar conexión simple
    console.log('🔌 Intentando conectar...');
    await prisma.$connect();
    console.log('✅ Conexión establecida');
    
    // Verificar que las tablas existen
    console.log('🗄️ Verificando tablas...');
    const tablas = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('📋 Tablas encontradas:', tablas);
    
    // Verificar datos básicos
    console.log('👤 Verificando usuarios...');
    const userCount = await prisma.user.count();
    console.log('👤 Total usuarios:', userCount);
    
    console.log('📂 Verificando categorías...');
    const categoryCount = await prisma.categoria.count();
    console.log('📂 Total categorías:', categoryCount);
    
    console.log('📰 Verificando noticias...');
    const newsCount = await prisma.noticia.count();
    console.log('📰 Total noticias:', newsCount);
    
    return NextResponse.json({
      success: true,
      message: 'Conexión a base de datos exitosa',
      details: {
        databaseConfigured: !!databaseUrl,
        databaseUrl: databaseUrl?.replace(/:[^:@]*@/, ':***@'),
        tablesCount: Array.isArray(tablas) ? tablas.length : 0,
        tables: tablas,
        counts: {
          users: userCount,
          categories: categoryCount,
          news: newsCount
        }
      }
    });
    
  } catch (error: any) {
    console.error('❌ Error de conexión a base de datos:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error de conexión a base de datos',
      details: {
        message: error.message,
        code: error.code,
        databaseConfigured: !!process.env.DATABASE_URL,
        databaseUrl: process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@')
      }
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
