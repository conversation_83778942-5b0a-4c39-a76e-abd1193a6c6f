import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 VERIFICANDO CONFIGURACIÓN ACTUAL DE IA...');

    // Obtener configuración activa
    const config = await prisma.aIConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    if (!config) {
      console.log('❌ No se encontró configuración activa');
      return NextResponse.json({
        error: 'No se encontró configuración activa',
        hasConfig: false
      });
    }

    console.log('📊 CONFIGURACIÓN ACTUAL:');
    console.log(`   ID: ${config.id}`);
    console.log(`   Proveedor por defecto: ${config.defaultProvider}`);
    console.log(`   OpenAI Model: ${config.openaiModel}`);
    console.log(`   Gemini Model: ${config.geminiModel}`);
    console.log(`   Activa: ${config.isActive}`);

    // Verificar diarios que usan configuración global
    const diariosGlobales = await prisma.diario.findMany({
      where: { useGlobalConfig: true },
      select: {
        id: true,
        nombre: true,
        isActive: true
      }
    });

    console.log(`📰 ${diariosGlobales.length} diarios usando configuración global`);

    // Verificar todas las configuraciones
    const todasLasConfigs = await prisma.aIConfig.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        id: true,
        defaultProvider: true,
        isActive: true,
        createdAt: true
      }
    });

    const resultado = {
      configActual: {
        id: config.id,
        defaultProvider: config.defaultProvider,
        openaiModel: config.openaiModel,
        geminiModel: config.geminiModel,
        openaiApiKey: config.openaiApiKey ? 'Configurada' : 'No configurada',
        geminiApiKey: config.geminiApiKey ? 'Configurada' : 'No configurada',
        isActive: config.isActive,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
      },
      diariosGlobales: diariosGlobales.map(d => ({
        id: d.id,
        nombre: d.nombre,
        isActive: d.isActive
      })),
      historialConfigs: todasLasConfigs,
      problema: config.defaultProvider === 'GEMINI' ? 'Proveedor por defecto configurado como GEMINI' : null,
      solucion: config.defaultProvider === 'GEMINI' ? 'Cambiar a OpenAI' : 'Configuración correcta'
    };

    return NextResponse.json(resultado);

  } catch (error) {
    console.error('❌ Error al verificar configuración:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
