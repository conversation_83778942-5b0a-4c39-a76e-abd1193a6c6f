import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 VERIFICANDO DATOS EN LA BASE DE DATOS...');

    // Contar registros en todas las tablas
    const [
      usuarios,
      categorias,
      noticias,
      diarios,
      aiConfigs,
      configuracionesIA,
      generaciones,
      limites,
      versiones,
      correcciones
    ] = await Promise.all([
      prisma.user.count(),
      prisma.categoria.count(),
      prisma.noticia.count(),
      prisma.diario.count(),
      prisma.aIConfig.count(),
      prisma.configuracionIA.count(),
      prisma.generacionImagen.count(),
      prisma.limiteUsoIA.count(),
      prisma.versionNoticia.count(),
      prisma.correccion.count()
    ]);

    // Obtener datos de verificación (sin información sensible)
    const usuariosData = await prisma.user.findMany({
      select: {
        id: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    });

    const categoriasData = await prisma.categoria.findMany({
      select: {
        id: true,
        nombre: true,
        isActive: true,
        _count: {
          select: {
            noticias: true
          }
        }
      }
    });

    const noticiasRecientes = await prisma.noticia.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Últimos 7 días
        }
      }
    });

    const diariosData = await prisma.diario.findMany({
      select: {
        id: true,
        nombre: true,
        isActive: true,
        aiProvider: true
      }
    });

    const aiConfigData = await prisma.aIConfig.findFirst({
      select: {
        id: true,
        defaultProvider: true,
        // NO incluir API keys por seguridad
      }
    });

    const configuracionesIAData = await prisma.configuracionIA.findMany({
      select: {
        id: true,
        nombre: true,
        proveedor: true,
        modelo: true,
        activo: true
      }
    });

    // Verificar categorías predefinidas
    const categoriasPredefinidas = [
      'Política', 'Economía', 'Deportes', 'Tecnología', 'Salud',
      'Cultura', 'Internacional', 'Sociedad', 'Medio Ambiente', 'Opinión'
    ];

    const categoriasFaltantes = [];
    for (const nombreCat of categoriasPredefinidas) {
      const categoria = await prisma.categoria.findUnique({
        where: { nombre: nombreCat }
      });
      if (!categoria) {
        categoriasFaltantes.push(nombreCat);
      }
    }

    const resultado = {
      success: true,
      timestamp: new Date().toISOString(),
      // NO exponer CRON_SECRET por seguridad
      resumen: {
        usuarios,
        categorias,
        noticias,
        diarios,
        aiConfigs,
        configuracionesIA,
        generaciones,
        limites,
        versiones,
        correcciones,
        noticiasRecientes
      },
      estadisticas: {
        usuariosPorRol: {
          admin: usuariosData.filter(u => u.role === 'ADMIN').length,
          editor: usuariosData.filter(u => u.role === 'EDITOR').length,
          user: usuariosData.filter(u => u.role === 'USER').length
        },
        usuariosActivos: usuariosData.filter(u => u.isActive).length,
        categoriasActivas: categoriasData.filter(c => c.isActive).length,
        diariosActivos: diariosData.filter(d => d.isActive).length,
        configuracionesIAActivas: configuracionesIAData.filter(c => c.activo).length
      },
      analisis: {
        tieneUsuarioAdmin: usuariosData.some(u => u.role === 'ADMIN'),
        categoriasFaltantes,
        tieneConfiguracionIA: !!aiConfigData,
        tieneDiarios: diariosData.length > 0,
        tieneConfiguracionesIA: configuracionesIAData.length > 0,
        tieneActividadReciente: noticiasRecientes > 0
      },
      recomendaciones: [] as string[]
    };

    // Generar recomendaciones
    if (!resultado.analisis.tieneUsuarioAdmin) {
      resultado.recomendaciones.push('⚠️ Crear usuario administrador');
    }

    if (resultado.analisis.categoriasFaltantes.length > 0) {
      resultado.recomendaciones.push(`📂 Recrear ${resultado.analisis.categoriasFaltantes.length} categorías faltantes`);
    }

    if (!resultado.analisis.tieneConfiguracionIA) {
      resultado.recomendaciones.push('🤖 Crear configuración global de IA');
    }

    if (!resultado.analisis.tieneDiarios) {
      resultado.recomendaciones.push('📰 Crear diarios de ejemplo');
    }

    if (resultado.estadisticas.usuariosActivos === 0) {
      resultado.recomendaciones.push('👥 Activar usuarios para permitir acceso');
    }

    if (resultado.estadisticas.categoriasActivas === 0) {
      resultado.recomendaciones.push('📁 Activar categorías para organizar noticias');
    }

    if (!resultado.analisis.tieneActividadReciente) {
      resultado.recomendaciones.push('📝 No hay noticias recientes - verificar flujo de trabajo');
    }

    if (resultado.recomendaciones.length === 0) {
      resultado.recomendaciones.push('✅ Sistema configurado correctamente');
    }

    console.log('📊 Resumen de verificación:', resultado.resumen);
    console.log('📈 Estadísticas:', resultado.estadisticas);
    console.log('🔍 Análisis:', resultado.analisis);
    console.log('💡 Recomendaciones:', resultado.recomendaciones);

    return NextResponse.json(resultado);

  } catch (error) {
    console.error('❌ Error al verificar datos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor', details: error },
      { status: 500 }
    );
  }
}
