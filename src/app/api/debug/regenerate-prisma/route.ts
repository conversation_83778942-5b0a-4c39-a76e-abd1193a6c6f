import { NextRequest, NextResponse } from 'next/server';
import { execSync } from 'child_process';

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Regenerando Prisma Client...');
    
    // Verificar variables de entorno
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      return NextResponse.json({
        success: false,
        error: 'DATABASE_URL no configurada'
      }, { status: 500 });
    }
    
    console.log('📊 DATABASE_URL configurada:', databaseUrl.replace(/:[^:@]*@/, ':***@'));
    
    // Regenerar Prisma Client
    console.log('📦 Ejecutando prisma generate...');
    const generateOutput = execSync('npx prisma generate', { 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    console.log('✅ Prisma generate completado:', generateOutput);
    
    // Aplicar migraciones
    console.log('🗄️ Aplicando migraciones...');
    try {
      const migrateOutput = execSync('npx prisma migrate deploy', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      console.log('✅ Migraciones aplicadas:', migrateOutput);
    } catch (migrateError: any) {
      console.log('⚠️ Error en migrate deploy, intentando db push...');
      const pushOutput = execSync('npx prisma db push', { 
        encoding: 'utf8',
        cwd: process.cwd()
      });
      console.log('✅ DB push completado:', pushOutput);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Prisma regenerado exitosamente',
      details: {
        databaseUrl: databaseUrl.replace(/:[^:@]*@/, ':***@'),
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error: any) {
    console.error('❌ Error regenerando Prisma:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error regenerando Prisma',
      details: {
        message: error.message,
        stdout: error.stdout,
        stderr: error.stderr
      }
    }, { status: 500 });
  }
}
