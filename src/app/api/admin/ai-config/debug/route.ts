import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Diagnóstico de configuración de IA
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [AI CONFIG DEBUG] Iniciando diagnóstico...');

    const diagnostics = {
      timestamp: new Date().toISOString(),
      database: {
        connected: false,
        error: null as string | null
      },
      aiConfigTable: {
        exists: false,
        count: 0,
        records: [] as any[],
        error: null as string | null
      },
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        DATABASE_URL: process.env.DATABASE_URL ? 'Configurada' : 'No configurada',
        OPENAI_API_KEY: process.env.OPENAI_API_KEY ? 'Configurada' : 'No configurada',
        GEMINI_API_KEY: process.env.GEMINI_API_KEY ? 'Configurada' : 'No configurada'
      },
      prismaClient: {
        status: 'unknown' as 'unknown' | 'working' | 'error',
        error: null as string | null
      }
    };

    // 1. Verificar conexión a la base de datos
    try {
      console.log('🔌 Verificando conexión a la base de datos...');
      await prisma.$connect();
      diagnostics.database.connected = true;
      console.log('✅ Conexión a la base de datos exitosa');
    } catch (error) {
      console.error('❌ Error conectando a la base de datos:', error);
      diagnostics.database.error = error instanceof Error ? error.message : 'Error desconocido';
    }

    // 2. Verificar tabla AIConfig
    try {
      console.log('📋 Verificando tabla AIConfig...');
      
      // Intentar contar registros
      const count = await prisma.aIConfig.count();
      diagnostics.aiConfigTable.exists = true;
      diagnostics.aiConfigTable.count = count;
      console.log(`✅ Tabla AIConfig existe con ${count} registros`);

      // Obtener registros existentes
      if (count > 0) {
        const records = await prisma.aIConfig.findMany({
          select: {
            id: true,
            openaiModel: true,
            geminiModel: true,
            defaultProvider: true,
            isActive: true,
            createdAt: true,
            updatedAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 5
        });
        diagnostics.aiConfigTable.records = records;
        console.log(`📊 Últimos ${records.length} registros obtenidos`);
      }

    } catch (error) {
      console.error('❌ Error verificando tabla AIConfig:', error);
      diagnostics.aiConfigTable.error = error instanceof Error ? error.message : 'Error desconocido';
      
      // Verificar si es un error de tabla no encontrada
      if (error instanceof Error && error.message.includes('does not exist')) {
        diagnostics.aiConfigTable.exists = false;
      }
    }

    // 3. Verificar estado del cliente Prisma
    try {
      console.log('🔧 Verificando cliente Prisma...');
      
      // Hacer una consulta simple para verificar que Prisma funciona
      await prisma.$queryRaw`SELECT 1 as test`;
      diagnostics.prismaClient.status = 'working';
      console.log('✅ Cliente Prisma funcionando correctamente');
      
    } catch (error) {
      console.error('❌ Error con cliente Prisma:', error);
      diagnostics.prismaClient.status = 'error';
      diagnostics.prismaClient.error = error instanceof Error ? error.message : 'Error desconocido';
    }

    // 4. Recomendaciones basadas en el diagnóstico
    const recommendations = [];
    
    if (!diagnostics.database.connected) {
      recommendations.push('🔧 Verificar configuración de DATABASE_URL');
      recommendations.push('🔧 Verificar que la base de datos esté ejecutándose');
    }
    
    if (!diagnostics.aiConfigTable.exists) {
      recommendations.push('🔧 Ejecutar migraciones de Prisma: npx prisma migrate deploy');
      recommendations.push('🔧 Generar cliente Prisma: npx prisma generate');
    }
    
    if (diagnostics.aiConfigTable.count === 0) {
      recommendations.push('🔧 Crear configuración inicial de IA');
    }
    
    if (diagnostics.prismaClient.status === 'error') {
      recommendations.push('🔧 Regenerar cliente Prisma');
      recommendations.push('🔧 Verificar esquema de base de datos');
    }

    console.log('🎯 [AI CONFIG DEBUG] Diagnóstico completado');

    return NextResponse.json({
      success: true,
      diagnostics,
      recommendations,
      summary: {
        databaseOk: diagnostics.database.connected,
        tableOk: diagnostics.aiConfigTable.exists,
        prismaOk: diagnostics.prismaClient.status === 'working',
        hasRecords: diagnostics.aiConfigTable.count > 0
      }
    });

  } catch (error) {
    console.error('❌ [AI CONFIG DEBUG] Error en diagnóstico:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Error en diagnóstico',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
