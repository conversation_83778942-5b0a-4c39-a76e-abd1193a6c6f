import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { testOpenAIConnection, testGeminiConnection } from '@/lib/ai-service';
import { getCachedModels } from '@/lib/ai-model-cache';

// GET - Obtener configuración de IA
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const includeModels = searchParams.get('includeModels') === 'true';

    const config = await prisma.aIConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    let responseData: any;

    if (!config) {
      // Retornar configuración por defecto
      responseData = {
        openaiApiKey: process.env.OPENAI_API_KEY ? '***' : null,
        openaiModel: 'gpt-4o-mini',
        openaiMaxTokens: 2000,
        openaiTemperature: 0.7,
        geminiApiKey: process.env.GEMINI_API_KEY ? '***' : null,
        geminiModel: 'gemini-2.0-flash-exp',
        geminiMaxTokens: 2000,
        geminiTemperature: 0.7,
        defaultProvider: 'OPENAI',
        isActive: true
      };
    } else {
      // SIEMPRE mostrar API keys desde variables de entorno (nunca de la base de datos)
      responseData = {
        ...config,
        openaiApiKey: process.env.OPENAI_API_KEY ? '***' : null,
        geminiApiKey: process.env.GEMINI_API_KEY ? '***' : null
      };
    }

    // Include available models if requested
    if (includeModels) {
      try {
        const apiKeys = {
          openai: config?.openaiApiKey || process.env.OPENAI_API_KEY,
          gemini: config?.geminiApiKey || process.env.GEMINI_API_KEY
        };

        const modelsResult = await getCachedModels('ALL', false, apiKeys);
        responseData.availableModels = modelsResult;
      } catch (error) {
        console.error('Error getting available models:', error);
        responseData.availableModels = {
          error: 'No se pudieron cargar los modelos disponibles'
        };
      }
    }

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error getting AI config:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear o actualizar configuración de IA
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      openaiApiKey,
      openaiModel,
      openaiMaxTokens,
      openaiTemperature,
      geminiApiKey,
      geminiModel,
      geminiMaxTokens,
      geminiTemperature,
      defaultProvider
    } = body;

    // Validaciones básicas
    if (!openaiModel || !geminiModel || !defaultProvider) {
      return NextResponse.json(
        { error: 'Campos requeridos faltantes' },
        { status: 400 }
      );
    }

    if (!['OPENAI', 'GEMINI'].includes(defaultProvider)) {
      return NextResponse.json(
        { error: 'Proveedor por defecto inválido' },
        { status: 400 }
      );
    }

    // Desactivar configuraciones anteriores
    await prisma.aIConfig.updateMany({
      where: { isActive: true },
      data: { isActive: false }
    });

    // Crear nueva configuración
    const newConfig = await prisma.aIConfig.create({
      data: {
        openaiApiKey: openaiApiKey === '***' ? undefined : openaiApiKey,
        openaiModel,
        openaiMaxTokens: parseInt(openaiMaxTokens) || 2000,
        openaiTemperature: parseFloat(openaiTemperature) || 0.7,
        geminiApiKey: geminiApiKey === '***' ? undefined : geminiApiKey,
        geminiModel,
        geminiMaxTokens: parseInt(geminiMaxTokens) || 2000,
        geminiTemperature: parseFloat(geminiTemperature) || 0.7,
        defaultProvider,
        isActive: true
      }
    });

    // Ocultar las API keys en la respuesta
    return NextResponse.json({
      ...newConfig,
      openaiApiKey: newConfig.openaiApiKey ? '***' : null,
      geminiApiKey: newConfig.geminiApiKey ? '***' : null
    });

  } catch (error) {
    console.error('Error saving AI config:', error);

    // Log detallado del error para debugging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Verificar si es un error de Prisma
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('Prisma error code:', (error as any).code);
      console.error('Prisma error meta:', (error as any).meta);
    }

    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
