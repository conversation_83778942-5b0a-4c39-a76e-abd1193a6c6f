import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getCachedModels, clearModelCache, getCacheStats } from '@/lib/ai-model-cache';
import { prisma } from '@/lib/prisma';

// GET - Get available AI models
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider') as 'OPENAI' | 'GEMINI' | 'ALL' || 'ALL';
    const forceRefresh = searchParams.get('refresh') === 'true';
    const includeStats = searchParams.get('stats') === 'true';

    // Get current AI config to use API keys
    const config = await prisma.aIConfig.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    const apiKeys = {
      openai: config?.openaiApiKey || process.env.OPENAI_API_KEY,
      gemini: config?.geminiApiKey || process.env.GEMINI_API_KEY
    };

    // Get models from cache or API
    const modelsResult = await getCachedModels(provider, forceRefresh, apiKeys);

    let response: any = {
      success: true,
      data: modelsResult
    };

    // Include cache statistics if requested
    if (includeStats) {
      const stats = await getCacheStats();
      response.cacheStats = stats;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error getting AI models:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// POST - Refresh model cache manually
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { action, provider } = body;

    if (action === 'refresh') {
      // Get current AI config to use API keys
      const config = await prisma.aIConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      });

      const apiKeys = {
        openai: config?.openaiApiKey || process.env.OPENAI_API_KEY,
        gemini: config?.geminiApiKey || process.env.GEMINI_API_KEY
      };

      // Force refresh models
      const refreshedModels = await getCachedModels(
        provider || 'ALL',
        true, // Force refresh
        apiKeys
      );

      return NextResponse.json({
        success: true,
        message: 'Modelos actualizados exitosamente',
        data: refreshedModels,
        timestamp: new Date().toISOString()
      });

    } else if (action === 'clear') {
      // Clear cache
      await clearModelCache(provider);

      return NextResponse.json({
        success: true,
        message: `Cache limpiado para ${provider || 'todos los proveedores'}`,
        timestamp: new Date().toISOString()
      });

    } else {
      return NextResponse.json(
        { error: 'Acción no válida. Use "refresh" o "clear"' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error managing AI models cache:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// DELETE - Clear model cache
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider') as 'OPENAI' | 'GEMINI' | undefined;

    await clearModelCache(provider);

    return NextResponse.json({
      success: true,
      message: `Cache eliminado para ${provider || 'todos los proveedores'}`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error clearing AI models cache:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
