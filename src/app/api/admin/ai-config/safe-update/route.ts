import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST - Actualización segura de configuración de IA
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 [SAFE UPDATE] Iniciando actualización segura de configuración IA...');

    const body = await request.json();
    console.log('📝 [SAFE UPDATE] Datos recibidos:', {
      ...body,
      openaiApiKey: body.openaiApiKey ? '***' : null,
      geminiApiKey: body.geminiApiKey ? '***' : null
    });

    const {
      openaiApiKey,
      openaiModel,
      openaiMaxTokens,
      openaiTemperature,
      geminiApiKey,
      geminiModel,
      geminiMaxTokens,
      geminiTemperature,
      defaultProvider
    } = body;

    // Validaciones mejoradas
    if (!openaiModel || !geminiModel || !defaultProvider) {
      console.error('❌ [SAFE UPDATE] Campos requeridos faltantes');
      return NextResponse.json(
        { 
          error: 'Campos requeridos faltantes',
          required: ['openaiModel', 'geminiModel', 'defaultProvider'],
          received: { openaiModel, geminiModel, defaultProvider }
        },
        { status: 400 }
      );
    }

    if (!['OPENAI', 'GEMINI'].includes(defaultProvider)) {
      console.error('❌ [SAFE UPDATE] Proveedor inválido:', defaultProvider);
      return NextResponse.json(
        { 
          error: 'Proveedor por defecto inválido',
          validProviders: ['OPENAI', 'GEMINI'],
          received: defaultProvider
        },
        { status: 400 }
      );
    }

    // Migrar modelos obsoletos automáticamente
    let finalGeminiModel = geminiModel;
    if (geminiModel === 'gemini-pro' || geminiModel === 'gemini-1.5-flash') {
      finalGeminiModel = 'gemini-2.5-flash';
      console.log(`🔄 [SAFE UPDATE] Migrando modelo obsoleto ${geminiModel} → ${finalGeminiModel}`);
    }

    // Verificar conexión a la base de datos
    try {
      await prisma.$connect();
      console.log('✅ [SAFE UPDATE] Conexión a base de datos exitosa');
    } catch (dbError) {
      console.error('❌ [SAFE UPDATE] Error de conexión a base de datos:', dbError);
      return NextResponse.json(
        { 
          error: 'Error de conexión a base de datos',
          details: dbError instanceof Error ? dbError.message : 'Error desconocido'
        },
        { status: 500 }
      );
    }

    // Verificar si existe configuración activa
    let existingConfig;
    try {
      existingConfig = await prisma.aIConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      });
      console.log('📊 [SAFE UPDATE] Configuración existente:', existingConfig ? 'Encontrada' : 'No encontrada');
    } catch (findError) {
      console.error('❌ [SAFE UPDATE] Error buscando configuración existente:', findError);
      return NextResponse.json(
        { 
          error: 'Error accediendo a configuraciones existentes',
          details: findError instanceof Error ? findError.message : 'Error desconocido'
        },
        { status: 500 }
      );
    }

    let newConfig;

    if (existingConfig) {
      // Actualizar configuración existente
      console.log('🔄 [SAFE UPDATE] Actualizando configuración existente...');
      try {
        newConfig = await prisma.aIConfig.update({
          where: { id: existingConfig.id },
          data: {
            openaiApiKey: openaiApiKey === '***' ? existingConfig.openaiApiKey : openaiApiKey,
            openaiModel,
            openaiMaxTokens: parseInt(openaiMaxTokens) || 2000,
            openaiTemperature: parseFloat(openaiTemperature) || 0.7,
            geminiApiKey: geminiApiKey === '***' ? existingConfig.geminiApiKey : geminiApiKey,
            geminiModel: finalGeminiModel,
            geminiMaxTokens: parseInt(geminiMaxTokens) || 2000,
            geminiTemperature: parseFloat(geminiTemperature) || 0.7,
            defaultProvider,
            isActive: true,
            updatedAt: new Date()
          }
        });
        console.log('✅ [SAFE UPDATE] Configuración actualizada exitosamente');
      } catch (updateError) {
        console.error('❌ [SAFE UPDATE] Error actualizando configuración:', updateError);
        return NextResponse.json(
          { 
            error: 'Error actualizando configuración',
            details: updateError instanceof Error ? updateError.message : 'Error desconocido'
          },
          { status: 500 }
        );
      }
    } else {
      // Crear nueva configuración
      console.log('➕ [SAFE UPDATE] Creando nueva configuración...');
      try {
        newConfig = await prisma.aIConfig.create({
          data: {
            openaiApiKey: openaiApiKey === '***' ? undefined : openaiApiKey,
            openaiModel,
            openaiMaxTokens: parseInt(openaiMaxTokens) || 2000,
            openaiTemperature: parseFloat(openaiTemperature) || 0.7,
            geminiApiKey: geminiApiKey === '***' ? undefined : geminiApiKey,
            geminiModel: finalGeminiModel,
            geminiMaxTokens: parseInt(geminiMaxTokens) || 2000,
            geminiTemperature: parseFloat(geminiTemperature) || 0.7,
            defaultProvider,
            isActive: true
          }
        });
        console.log('✅ [SAFE UPDATE] Nueva configuración creada exitosamente');
      } catch (createError) {
        console.error('❌ [SAFE UPDATE] Error creando configuración:', createError);
        return NextResponse.json(
          { 
            error: 'Error creando configuración',
            details: createError instanceof Error ? createError.message : 'Error desconocido'
          },
          { status: 500 }
        );
      }
    }

    // Respuesta exitosa
    const response = {
      success: true,
      message: existingConfig ? 'Configuración actualizada' : 'Configuración creada',
      data: {
        ...newConfig,
        openaiApiKey: newConfig.openaiApiKey ? '***' : null,
        geminiApiKey: newConfig.geminiApiKey ? '***' : null
      },
      migrations: finalGeminiModel !== geminiModel ? {
        geminiModel: `${geminiModel} → ${finalGeminiModel}`
      } : null,
      timestamp: new Date().toISOString()
    };

    console.log('🎉 [SAFE UPDATE] Actualización completada exitosamente');
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ [SAFE UPDATE] Error general:', error);
    
    // Log detallado del error
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
