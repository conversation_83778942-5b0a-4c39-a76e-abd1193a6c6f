import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Obtener configuración general del sistema
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Buscar configuración existente
    const config = await prisma.systemConfig.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    if (!config) {
      // Retornar configuración por defecto
      return NextResponse.json({
        siteName: 'Panel Unificado V2',
        siteDescription: 'Sistema de gestión de noticias y contenido',
        siteUrl: 'http://localhost:3017',
        adminEmail: '<EMAIL>',
        timezone: 'America/Argentina/Buenos_Aires',
        language: 'es',
        theme: 'light',
        enableNotifications: true,
        enableRegistration: false,
        maxFileSize: 10,
        sessionTimeout: 24,
        backupFrequency: 'daily',
        maintenanceMode: false
      });
    }

    return NextResponse.json(config.settings);

  } catch (error) {
    console.error('Error getting system config:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear o actualizar configuración general
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      siteName,
      siteDescription,
      siteUrl,
      adminEmail,
      timezone,
      language,
      theme,
      enableNotifications,
      enableRegistration,
      maxFileSize,
      sessionTimeout,
      backupFrequency,
      maintenanceMode
    } = body;

    // Validaciones básicas
    if (!siteName || !siteUrl || !adminEmail) {
      return NextResponse.json(
        { error: 'Nombre del sitio, URL y email del administrador son requeridos' },
        { status: 400 }
      );
    }

    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(adminEmail)) {
      return NextResponse.json(
        { error: 'Email del administrador no es válido' },
        { status: 400 }
      );
    }

    // Validar URL
    try {
      new URL(siteUrl);
    } catch {
      return NextResponse.json(
        { error: 'URL del sitio no es válida' },
        { status: 400 }
      );
    }

    // Validar rangos numéricos
    if (maxFileSize < 1 || maxFileSize > 100) {
      return NextResponse.json(
        { error: 'El tamaño máximo de archivo debe estar entre 1 y 100 MB' },
        { status: 400 }
      );
    }

    if (sessionTimeout < 1 || sessionTimeout > 168) {
      return NextResponse.json(
        { error: 'El timeout de sesión debe estar entre 1 y 168 horas' },
        { status: 400 }
      );
    }

    const configData = {
      siteName,
      siteDescription,
      siteUrl,
      adminEmail,
      timezone,
      language,
      theme,
      enableNotifications: Boolean(enableNotifications),
      enableRegistration: Boolean(enableRegistration),
      maxFileSize: parseInt(maxFileSize),
      sessionTimeout: parseInt(sessionTimeout),
      backupFrequency,
      maintenanceMode: Boolean(maintenanceMode)
    };

    // Buscar configuración existente
    const existingConfig = await prisma.systemConfig.findFirst();

    let savedConfig;
    if (existingConfig) {
      // Actualizar configuración existente
      savedConfig = await prisma.systemConfig.update({
        where: { id: existingConfig.id },
        data: {
          settings: configData,
          updatedBy: parseInt(session.user.id)
        }
      });
    } else {
      // Crear nueva configuración
      savedConfig = await prisma.systemConfig.create({
        data: {
          settings: configData,
          createdBy: parseInt(session.user.id),
          updatedBy: parseInt(session.user.id)
        }
      });
    }

    // Log de auditoría
    await prisma.auditLog.create({
      data: {
        action: existingConfig ? 'UPDATE_SYSTEM_CONFIG' : 'CREATE_SYSTEM_CONFIG',
        entityType: 'SystemConfig',
        entityId: savedConfig.id.toString(),
        userId: parseInt(session.user.id),
        details: {
          changes: configData,
          timestamp: new Date().toISOString()
        }
      }
    }).catch((error: any) => {
      // Si no existe la tabla de auditoría, continuar sin error
      console.log('Audit log not available:', error.message);
    });

    return NextResponse.json({
      success: true,
      message: 'Configuración guardada exitosamente',
      data: savedConfig.settings
    });

  } catch (error) {
    console.error('Error saving system config:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
