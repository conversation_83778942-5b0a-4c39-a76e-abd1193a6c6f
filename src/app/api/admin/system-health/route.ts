import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Medir tiempo de respuesta de la base de datos
    const dbStart = Date.now();
    const dbTest = await prisma.user.count();
    const dbResponseTime = Date.now() - dbStart;

    // Obtener estadísticas de usuarios
    const [totalUsers, activeUsers, onlineUsers] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      // Simular usuarios online (en una implementación real, esto vendría de sesiones activas)
      Math.floor(Math.random() * 10) + 1
    ]);

    // Obtener estadísticas de contenido
    const [totalNoticias, totalCorrecciones, totalGeneraciones] = await Promise.all([
      prisma.noticia.count(),
      prisma.correccion.count(),
      prisma.generacionImagen.count()
    ]);

    // Simular métricas del sistema (en producción, estas vendrían del sistema operativo)
    const systemMetrics = {
      memory: {
        used: Math.floor(Math.random() * 2048) + 512, // 512-2560 MB
        total: 4096, // 4 GB
        percentage: 0
      },
      storage: {
        used: Math.floor(Math.random() * 50) + 10, // 10-60 GB
        total: 100, // 100 GB
        percentage: 0
      }
    };

    // Calcular porcentajes
    systemMetrics.memory.percentage = Math.round((systemMetrics.memory.used / systemMetrics.memory.total) * 100);
    systemMetrics.storage.percentage = Math.round((systemMetrics.storage.used / systemMetrics.storage.total) * 100);

    // Determinar estado de la base de datos
    let dbStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    if (dbResponseTime > 1000) dbStatus = 'error';
    else if (dbResponseTime > 500) dbStatus = 'warning';

    // Simular estado de la API
    const apiResponseTime = Math.floor(Math.random() * 200) + 50; // 50-250ms
    const apiErrorRate = Math.floor(Math.random() * 5); // 0-5%
    
    let apiStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    if (apiErrorRate > 3 || apiResponseTime > 200) apiStatus = 'warning';
    if (apiErrorRate > 5 || apiResponseTime > 500) apiStatus = 'error';

    // Obtener estadísticas de IA
    const [configuracionesIA, generacionesEsteMes] = await Promise.all([
      prisma.configuracionIA.count({ where: { activo: true } }),
      prisma.generacionImagen.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ]);

    const health = {
      database: {
        status: dbStatus,
        connections: Math.floor(Math.random() * 10) + 1,
        responseTime: dbResponseTime
      },
      memory: systemMetrics.memory,
      storage: systemMetrics.storage,
      api: {
        status: apiStatus,
        responseTime: apiResponseTime,
        errorRate: apiErrorRate
      },
      users: {
        active: activeUsers,
        total: totalUsers,
        online: onlineUsers
      },
      content: {
        noticias: totalNoticias,
        correcciones: totalCorrecciones,
        generaciones: totalGeneraciones
      },
      ia: {
        configuraciones: configuracionesIA,
        generacionesEsteMes
      },
      timestamp: new Date().toISOString(),
      uptime: process.uptime() // Tiempo que lleva corriendo el proceso Node.js
    };

    return NextResponse.json(health);

  } catch (error) {
    console.error('Error al obtener estado del sistema:', error);
    
    // Retornar estado de error
    return NextResponse.json({
      database: {
        status: 'error',
        connections: 0,
        responseTime: 0
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      },
      storage: {
        used: 0,
        total: 0,
        percentage: 0
      },
      api: {
        status: 'error',
        responseTime: 0,
        errorRate: 100
      },
      users: {
        active: 0,
        total: 0,
        online: 0
      },
      content: {
        noticias: 0,
        correcciones: 0,
        generaciones: 0
      },
      ia: {
        configuraciones: 0,
        generacionesEsteMes: 0
      },
      timestamp: new Date().toISOString(),
      uptime: 0,
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}
