import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Obtener una categoría específica
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const categoriaId = parseInt(id);
    
    if (isNaN(categoriaId)) {
      return NextResponse.json(
        { error: 'ID de categoría inválido' },
        { status: 400 }
      );
    }

    const categoria = await prisma.categoria.findUnique({
      where: { id: categoriaId },
      include: {
        _count: {
          select: {
            noticias: true
          }
        }
      }
    });

    if (!categoria) {
      return NextResponse.json(
        { error: 'Categoría no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json(categoria);

  } catch (error) {
    console.error('Error al obtener categoría:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar una categoría
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const categoriaId = parseInt(id);
    
    if (isNaN(categoriaId)) {
      return NextResponse.json(
        { error: 'ID de categoría inválido' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { nombre, descripcion, color, isActive, orden } = body;

    // Verificar que la categoría existe
    const categoriaExistente = await prisma.categoria.findUnique({
      where: { id: categoriaId }
    });

    if (!categoriaExistente) {
      return NextResponse.json(
        { error: 'Categoría no encontrada' },
        { status: 404 }
      );
    }

    // Validaciones
    if (!nombre || nombre.trim().length === 0) {
      return NextResponse.json(
        { error: 'El nombre de la categoría es requerido' },
        { status: 400 }
      );
    }

    if (nombre.trim().length > 100) {
      return NextResponse.json(
        { error: 'El nombre no puede exceder 100 caracteres' },
        { status: 400 }
      );
    }

    // Verificar que el nombre sea único (excluyendo la categoría actual)
    const existingCategory = await prisma.categoria.findFirst({
      where: { 
        nombre: nombre.trim(),
        id: { not: categoriaId }
      }
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Ya existe otra categoría con ese nombre' },
        { status: 400 }
      );
    }

    // Validar color (formato hex)
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (color && !colorRegex.test(color)) {
      return NextResponse.json(
        { error: 'El color debe estar en formato hexadecimal válido' },
        { status: 400 }
      );
    }

    // Actualizar la categoría
    const categoriaActualizada = await prisma.categoria.update({
      where: { id: categoriaId },
      data: {
        nombre: nombre.trim(),
        descripcion: descripcion?.trim() || null,
        color: color || categoriaExistente.color,
        isActive: isActive !== undefined ? Boolean(isActive) : categoriaExistente.isActive,
        orden: orden !== undefined ? parseInt(orden) : categoriaExistente.orden
      },
      include: {
        _count: {
          select: {
            noticias: true
          }
        }
      }
    });

    // Log de auditoría
    await prisma.auditLog.create({
      data: {
        action: 'UPDATE_CATEGORIA',
        entityType: 'Categoria',
        entityId: categoriaId.toString(),
        userId: parseInt(session.user.id),
        details: {
          before: categoriaExistente,
          after: categoriaActualizada,
          timestamp: new Date().toISOString()
        }
      }
    }).catch((error: any) => {
      console.log('Audit log not available:', error.message);
    });

    return NextResponse.json({
      success: true,
      message: 'Categoría actualizada exitosamente',
      categoria: categoriaActualizada
    });

  } catch (error) {
    console.error('Error al actualizar categoría:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar una categoría
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const categoriaId = parseInt(id);
    
    if (isNaN(categoriaId)) {
      return NextResponse.json(
        { error: 'ID de categoría inválido' },
        { status: 400 }
      );
    }

    // Verificar que la categoría existe
    const categoria = await prisma.categoria.findUnique({
      where: { id: categoriaId },
      include: {
        _count: {
          select: {
            noticias: true
          }
        }
      }
    });

    if (!categoria) {
      return NextResponse.json(
        { error: 'Categoría no encontrada' },
        { status: 404 }
      );
    }

    // Verificar que no tenga noticias asociadas
    if (categoria._count.noticias > 0) {
      return NextResponse.json(
        { 
          error: `No se puede eliminar la categoría porque tiene ${categoria._count.noticias} noticia(s) asociada(s). Primero debe reasignar o eliminar las noticias.`,
          noticiasCount: categoria._count.noticias
        },
        { status: 400 }
      );
    }

    // Eliminar la categoría
    await prisma.categoria.delete({
      where: { id: categoriaId }
    });

    // Log de auditoría
    await prisma.auditLog.create({
      data: {
        action: 'DELETE_CATEGORIA',
        entityType: 'Categoria',
        entityId: categoriaId.toString(),
        userId: parseInt(session.user.id),
        details: {
          deletedCategoria: categoria,
          timestamp: new Date().toISOString()
        }
      }
    }).catch((error: any) => {
      console.log('Audit log not available:', error.message);
    });

    return NextResponse.json({
      success: true,
      message: 'Categoría eliminada exitosamente'
    });

  } catch (error) {
    console.error('Error al eliminar categoría:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
