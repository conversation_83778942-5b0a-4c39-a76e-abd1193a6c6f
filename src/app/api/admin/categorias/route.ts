import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Obtener todas las categorías con filtros y paginación
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const estado = searchParams.get('estado') || 'all'; // 'active', 'inactive', 'all'
    const orderBy = searchParams.get('orderBy') || 'orden'; // 'nombre', 'createdAt', 'orden'
    const orderDir = searchParams.get('orderDir') || 'asc'; // 'asc', 'desc'

    const skip = (page - 1) * limit;

    // Construir filtros
    const where: any = {};
    
    if (search) {
      where.OR = [
        { nombre: { contains: search, mode: 'insensitive' } },
        { descripcion: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (estado === 'active') {
      where.isActive = true;
    } else if (estado === 'inactive') {
      where.isActive = false;
    }

    // Construir ordenamiento
    const orderByClause: any = {};
    if (orderBy === 'nombre') {
      orderByClause.nombre = orderDir;
    } else if (orderBy === 'createdAt') {
      orderByClause.createdAt = orderDir;
    } else {
      // Usar id como fallback hasta que se resuelva el problema con orden
      orderByClause.id = orderDir;
    }

    // Obtener categorías con conteo de noticias
    const [categorias, total] = await Promise.all([
      prisma.categoria.findMany({
        where,
        skip,
        take: limit,
        orderBy: orderByClause,
        include: {
          _count: {
            select: {
              noticias: true
            }
          }
        }
      }),
      prisma.categoria.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      categorias,
      pagination: {
        page,
        limit,
        total,
        pages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error al obtener categorías:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nueva categoría
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { nombre, descripcion, color, isActive, orden } = body;

    // Validaciones
    if (!nombre || nombre.trim().length === 0) {
      return NextResponse.json(
        { error: 'El nombre de la categoría es requerido' },
        { status: 400 }
      );
    }

    if (nombre.trim().length > 100) {
      return NextResponse.json(
        { error: 'El nombre no puede exceder 100 caracteres' },
        { status: 400 }
      );
    }

    // Verificar que el nombre sea único
    const existingCategory = await prisma.categoria.findUnique({
      where: { nombre: nombre.trim() }
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: 'Ya existe una categoría con ese nombre' },
        { status: 400 }
      );
    }

    // Validar color (formato hex)
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (color && !colorRegex.test(color)) {
      return NextResponse.json(
        { error: 'El color debe estar en formato hexadecimal válido' },
        { status: 400 }
      );
    }

    // Si no se proporciona orden, usar el siguiente disponible
    let finalOrden = orden;
    if (!finalOrden) {
      const maxOrden = await prisma.categoria.aggregate({
        _max: { orden: true }
      });
      finalOrden = (maxOrden._max.orden || 0) + 1;
    }

    // Crear la categoría
    const nuevaCategoria = await prisma.categoria.create({
      data: {
        nombre: nombre.trim(),
        descripcion: descripcion?.trim() || null,
        color: color || '#6B7280',
        isActive: isActive !== undefined ? Boolean(isActive) : true,
        orden: finalOrden
      },
      include: {
        _count: {
          select: {
            noticias: true
          }
        }
      }
    });

    // Log de auditoría
    await prisma.auditLog.create({
      data: {
        action: 'CREATE_CATEGORIA',
        entityType: 'Categoria',
        entityId: nuevaCategoria.id.toString(),
        userId: parseInt(session.user.id),
        details: {
          categoria: nuevaCategoria,
          timestamp: new Date().toISOString()
        }
      }
    }).catch((error: any) => {
      console.log('Audit log not available:', error.message);
    });

    return NextResponse.json({
      success: true,
      message: 'Categoría creada exitosamente',
      categoria: nuevaCategoria
    }, { status: 201 });

  } catch (error) {
    console.error('Error al crear categoría:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar orden de categorías (para drag & drop)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { categorias } = body; // Array de { id, orden }

    if (!Array.isArray(categorias)) {
      return NextResponse.json(
        { error: 'Se requiere un array de categorías con id y orden' },
        { status: 400 }
      );
    }

    // Actualizar orden de todas las categorías
    const updatePromises = categorias.map(cat => 
      prisma.categoria.update({
        where: { id: cat.id },
        data: { orden: cat.orden }
      })
    );

    await Promise.all(updatePromises);

    // Log de auditoría
    await prisma.auditLog.create({
      data: {
        action: 'REORDER_CATEGORIAS',
        entityType: 'Categoria',
        entityId: 'multiple',
        userId: parseInt(session.user.id),
        details: {
          newOrder: categorias,
          timestamp: new Date().toISOString()
        }
      }
    }).catch((error: any) => {
      console.log('Audit log not available:', error.message);
    });

    return NextResponse.json({
      success: true,
      message: 'Orden de categorías actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error al actualizar orden de categorías:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
