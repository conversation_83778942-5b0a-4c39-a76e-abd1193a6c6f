import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar si ya existen configuraciones
    const existingConfigs = await prisma.configuracionIA.count();

    if (existingConfigs > 0) {
      return NextResponse.json({
        message: `Ya existen ${existingConfigs} configuraciones de IA`,
        count: existingConfigs
      });
    }

    // Insertar configuraciones por defecto usando Prisma ORM
    const configuraciones = [
      {
        nombre: 'Imagen 3 - Calidad Estándar',
        proveedor: 'GOOGLE_GEMINI',
        modelo: 'imagen-3.0-generate-001',
        parametros: JSON.stringify({
          aspectRatio: "1:1",
          numberOfImages: 1,
          personGeneration: "allow_adult"
        }),
        promptPorDefecto: 'Create a professional, high-quality image for a news article about: {titulo}. The image should be journalistic, clear, and appropriate for news media. Style: photorealistic, well-lit, professional.',
        limitesUso: JSON.stringify({
          diario: 20,
          semanal: 100,
          mensual: 400
        }),
        activo: true
      },
      {
        nombre: 'Imagen 4 - Alta Calidad',
        proveedor: 'GOOGLE_GEMINI',
        modelo: 'imagen-4.0-generate-preview-06-06',
        parametros: JSON.stringify({
          aspectRatio: "1:1",
          numberOfImages: 1,
          personGeneration: "allow_adult"
        }),
        promptPorDefecto: 'Generate a high-quality, professional image for a news article about: {titulo}. The image should be photorealistic, well-composed, and suitable for journalism. Focus on clarity, professional lighting, and news-appropriate content.',
        limitesUso: JSON.stringify({
          diario: 10,
          semanal: 50,
          mensual: 200
        }),
        activo: true
      },
      {
        nombre: 'Gemini 2.0 Flash - Conversacional',
        proveedor: 'GOOGLE_GEMINI',
        modelo: 'gemini-2.0-flash-preview-image-generation',
        parametros: JSON.stringify({
          responseModalities: ["TEXT", "IMAGE"]
        }),
        promptPorDefecto: 'Hi, can you create a professional, high-quality image for a news article about: {titulo}. The image should be journalistic, clear, and appropriate for news media. Make it photorealistic, well-lit, and professional.',
        limitesUso: JSON.stringify({
          diario: 15,
          semanal: 75,
          mensual: 300
        }),
        activo: true
      }
    ];

    const createdConfigs = await prisma.configuracionIA.createMany({
      data: configuraciones
    });

    return NextResponse.json({
      success: true,
      message: 'Configuraciones de IA creadas exitosamente',
      count: createdConfigs.count
    });

  } catch (error) {
    console.error('Error al crear tablas de IA:', error);
    return NextResponse.json(
      { error: `Error interno del servidor: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    );
  }
}
