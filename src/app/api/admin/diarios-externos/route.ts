import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Obtener todos los diarios externos
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    const where = search
      ? {
          OR: [
            { nombre: { contains: search, mode: 'insensitive' as const } },
            { descripcion: { contains: search, mode: 'insensitive' as const } },
            { url: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [diarios, total] = await Promise.all([
      prisma.diarioExterno.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: {
              publicacionesExternas: true,
            },
          },
        },
      }),
      prisma.diarioExterno.count({ where }),
    ]);

    return NextResponse.json({
      diarios,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error al obtener diarios externos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo diario externo
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      nombre,
      descripcion,
      urlBase,
      bearerToken,
      categoriaImagenId,
      endpointImagen,
      endpointCategoria,
      endpointArticulo,
      configuracion,
    } = body;

    // Validaciones básicas
    if (!nombre) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    if (!urlBase) {
      return NextResponse.json(
        { error: 'La URL base es requerida' },
        { status: 400 }
      );
    }

    if (!bearerToken) {
      return NextResponse.json(
        { error: 'El token de autenticación es requerido' },
        { status: 400 }
      );
    }

    if (!categoriaImagenId) {
      return NextResponse.json(
        { error: 'La categoría de imagen es requerida' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un diario con el mismo nombre
    const existingDiario = await prisma.diarioExterno.findFirst({
      where: { nombre },
    });

    if (existingDiario) {
      return NextResponse.json(
        { error: 'Ya existe un diario externo con ese nombre' },
        { status: 400 }
      );
    }

    // Crear el diario externo (solo con campos existentes)
    const diarioExterno = await prisma.diarioExterno.create({
      data: {
        nombre,
        urlBase,
        bearerToken,
        categoriaImagenId: parseInt(categoriaImagenId),
        activo: true,
      },
    });

    return NextResponse.json(diarioExterno, { status: 201 });
  } catch (error) {
    console.error('Error al crear diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
