import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/diarios-externos/[id] - Obtener un diario externo específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const diarioId = parseInt(id);

    if (isNaN(diarioId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioId },
      include: {
        categoriaMapeos: {
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          }
        },
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: diario
    });

  } catch (error) {
    console.error('Error al obtener diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/diarios-externos/[id] - Actualizar un diario externo
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const diarioId = parseInt(id);

    if (isNaN(diarioId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const {
      nombre,
      urlBase,
      bearerToken,
      categoriaImagenId,
      activo,
    } = body;

    // Validaciones básicas
    if (!nombre) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    if (!urlBase) {
      return NextResponse.json(
        { error: 'La URL base es requerida' },
        { status: 400 }
      );
    }

    if (!bearerToken) {
      return NextResponse.json(
        { error: 'El token de autenticación es requerido' },
        { status: 400 }
      );
    }

    if (!categoriaImagenId) {
      return NextResponse.json(
        { error: 'La categoría de imagen es requerida' },
        { status: 400 }
      );
    }

    // Verificar que el diario existe
    const diarioExistente = await prisma.diarioExterno.findUnique({
      where: { id: diarioId }
    });

    if (!diarioExistente) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Actualizar el diario externo
    const diarioActualizado = await prisma.diarioExterno.update({
      where: { id: diarioId },
      data: {
        nombre,
        urlBase,
        bearerToken,
        categoriaImagenId: parseInt(categoriaImagenId),
        activo: activo !== undefined ? Boolean(activo) : diarioExistente.activo,
      },
      include: {
        categoriaMapeos: {
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          }
        },
        _count: {
          select: {
            categoriaMapeos: true,
            publicacionesExternas: true,
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: diarioActualizado,
      message: 'Diario externo actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error al actualizar diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/diarios-externos/[id] - Eliminar un diario externo
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const diarioId = parseInt(id);

    if (isNaN(diarioId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que el diario existe
    const diarioExistente = await prisma.diarioExterno.findUnique({
      where: { id: diarioId },
      include: {
        _count: {
          select: {
            publicacionesExternas: true,
          }
        }
      }
    });

    if (!diarioExistente) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Verificar si tiene publicaciones asociadas
    if (diarioExistente._count.publicacionesExternas > 0) {
      return NextResponse.json({
        error: 'No se puede eliminar el diario porque tiene publicaciones asociadas'
      }, { status: 400 });
    }

    // Eliminar el diario externo (los mapeos se eliminan automáticamente por CASCADE)
    await prisma.diarioExterno.delete({
      where: { id: diarioId }
    });

    return NextResponse.json({
      success: true,
      message: 'Diario externo eliminado exitosamente'
    });

  } catch (error) {
    console.error('Error al eliminar diario externo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
