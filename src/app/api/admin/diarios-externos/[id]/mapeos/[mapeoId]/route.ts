import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// DELETE /api/admin/diarios-externos/[id]/mapeos/[mapeoId] - Eliminar un mapeo específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; mapeoId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id, mapeoId: mapeoIdParam } = await params;
    const diarioId = parseInt(id);
    const mapeoId = parseInt(mapeoIdParam);

    if (isNaN(diarioId) || isNaN(mapeoId)) {
      return NextResponse.json({ error: 'IDs inválidos' }, { status: 400 });
    }

    // Verificar que el mapeo existe y pertenece al diario
    const mapeo = await prisma.categoriaMapeo.findFirst({
      where: {
        id: mapeoId,
        diarioExternoId: diarioId
      },
      include: {
        categoriaLocal: {
          select: {
            nombre: true
          }
        }
      }
    });

    if (!mapeo) {
      return NextResponse.json({ error: 'Mapeo no encontrado' }, { status: 404 });
    }

    // Eliminar el mapeo
    await prisma.categoriaMapeo.delete({
      where: { id: mapeoId }
    });

    return NextResponse.json({
      success: true,
      message: `Mapeo para la categoría "${mapeo.categoriaLocal.nombre}" eliminado exitosamente`
    });

  } catch (error) {
    console.error('Error al eliminar mapeo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
