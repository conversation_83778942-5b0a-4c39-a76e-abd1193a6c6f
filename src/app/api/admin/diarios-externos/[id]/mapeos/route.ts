import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/diarios-externos/[id]/mapeos - Obtener mapeos de categorías
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const diarioId = parseInt(id);

    if (isNaN(diarioId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Obtener mapeos
    const mapeos = await prisma.categoriaMapeo.findMany({
      where: { diarioExternoId: diarioId },
      include: {
        categoriaLocal: {
          select: {
            id: true,
            nombre: true,
            color: true,
          }
        }
      },
      orderBy: {
        categoriaLocal: {
          nombre: 'asc'
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: mapeos
    });

  } catch (error) {
    console.error('Error al obtener mapeos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/admin/diarios-externos/[id]/mapeos - Crear nuevos mapeos
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const diarioId = parseInt(id);

    if (isNaN(diarioId)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { mapeos } = body;

    if (!Array.isArray(mapeos) || mapeos.length === 0) {
      return NextResponse.json(
        { error: 'Se requiere un array de mapeos' },
        { status: 400 }
      );
    }

    // Verificar que el diario existe
    const diario = await prisma.diarioExterno.findUnique({
      where: { id: diarioId }
    });

    if (!diario) {
      return NextResponse.json({ error: 'Diario no encontrado' }, { status: 404 });
    }

    // Validar mapeos
    for (const mapeo of mapeos) {
      if (!mapeo.categoriaLocalId || !mapeo.categoriaExternaId) {
        return NextResponse.json(
          { error: 'Cada mapeo debe tener categoriaLocalId y categoriaExternaId' },
          { status: 400 }
        );
      }

      // Verificar que la categoría local existe
      const categoriaLocal = await prisma.categoria.findUnique({
        where: { id: mapeo.categoriaLocalId }
      });

      if (!categoriaLocal) {
        return NextResponse.json(
          { error: `Categoría local con ID ${mapeo.categoriaLocalId} no encontrada` },
          { status: 400 }
        );
      }

      // Verificar que no existe ya un mapeo para esta categoría local en este diario
      const mapeoExistente = await prisma.categoriaMapeo.findUnique({
        where: {
          diarioExternoId_categoriaLocalId: {
            diarioExternoId: diarioId,
            categoriaLocalId: mapeo.categoriaLocalId
          }
        }
      });

      if (mapeoExistente) {
        return NextResponse.json(
          { error: `Ya existe un mapeo para la categoría local ${categoriaLocal.nombre}` },
          { status: 400 }
        );
      }
    }

    // Crear mapeos
    const nuevosMapeos = await Promise.all(
      mapeos.map(mapeo =>
        prisma.categoriaMapeo.create({
          data: {
            diarioExternoId: diarioId,
            categoriaLocalId: mapeo.categoriaLocalId,
            categoriaExternaId: mapeo.categoriaExternaId,
          },
          include: {
            categoriaLocal: {
              select: {
                id: true,
                nombre: true,
                color: true,
              }
            }
          }
        })
      )
    );

    return NextResponse.json({
      success: true,
      data: nuevosMapeos,
      message: `${nuevosMapeos.length} mapeo(s) creado(s) exitosamente`
    });

  } catch (error) {
    console.error('Error al crear mapeos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
