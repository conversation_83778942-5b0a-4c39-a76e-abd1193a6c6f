import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// POST /api/admin/regenerate-prisma - Regenerar cliente de Prisma
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔄 [REGENERATE PRISMA] Regenerando cliente de Prisma...');

    try {
      // Intentar regenerar el cliente de Prisma
      const { stdout, stderr } = await execAsync('npx prisma generate', {
        cwd: process.cwd(),
        timeout: 30000 // 30 segundos timeout
      });

      console.log('✅ [REGENERATE PRISMA] Cliente regenerado exitosamente');
      console.log('📋 Stdout:', stdout);
      
      if (stderr) {
        console.log('⚠️ Stderr:', stderr);
      }

      return NextResponse.json({
        success: true,
        message: 'Cliente de Prisma regenerado exitosamente',
        output: stdout,
        warnings: stderr || null,
        timestamp: new Date().toISOString()
      });

    } catch (execError: any) {
      console.error('❌ [REGENERATE PRISMA] Error ejecutando comando:', execError);
      
      // Si falla el comando, intentar una alternativa
      return NextResponse.json({
        success: false,
        error: 'Error regenerando cliente de Prisma',
        details: execError.message,
        suggestion: 'Ejecuta manualmente: npx prisma generate',
        alternativeApproach: 'Usando consultas SQL directas mientras tanto',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [REGENERATE PRISMA] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
