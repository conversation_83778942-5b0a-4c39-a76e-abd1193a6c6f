import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const configuraciones = await prisma.configuracionIA.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            generaciones: true,
          },
        },
      },
    });

    return NextResponse.json({ data: configuraciones });

  } catch (error) {
    console.error('Error al obtener configuraciones de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      nombre,
      proveedor,
      modelo,
      apiKey,
      endpoint,
      parametros,
      promptPorDefecto,
      limitesUso,
      activo,
    } = body;

    // Validaciones
    if (!nombre || !proveedor || !modelo) {
      return NextResponse.json(
        { error: 'Nombre, proveedor y modelo son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que el nombre sea único
    const existente = await prisma.configuracionIA.findUnique({
      where: { nombre },
    });

    if (existente) {
      return NextResponse.json(
        { error: 'Ya existe una configuración con ese nombre' },
        { status: 400 }
      );
    }

    const configuracion = await prisma.configuracionIA.create({
      data: {
        nombre,
        proveedor,
        modelo,
        apiKey,
        endpoint,
        parametros: parametros || {},
        promptPorDefecto,
        limitesUso: limitesUso || { diario: 10, semanal: 50, mensual: 200 },
        activo: activo !== undefined ? activo : true,
      },
    });

    return NextResponse.json({
      success: true,
      data: configuracion,
    });

  } catch (error) {
    console.error('Error al crear configuración de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
