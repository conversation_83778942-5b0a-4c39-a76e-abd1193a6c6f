import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const configuracion = await prisma.configuracionIA.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            generaciones: true,
            limites: true,
          },
        },
      },
    });

    if (!configuracion) {
      return NextResponse.json({ error: 'Configuración no encontrada' }, { status: 404 });
    }

    return NextResponse.json({ data: configuracion });

  } catch (error) {
    console.error('Error al obtener configuración de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    const body = await request.json();
    const {
      nombre,
      proveedor,
      modelo,
      apiKey,
      endpoint,
      parametros,
      promptPorDefecto,
      limitesUso,
      activo,
    } = body;

    // Verificar que la configuración existe
    const existente = await prisma.configuracionIA.findUnique({
      where: { id },
    });

    if (!existente) {
      return NextResponse.json({ error: 'Configuración no encontrada' }, { status: 404 });
    }

    // Verificar nombre único (si se está cambiando)
    if (nombre && nombre !== existente.nombre) {
      const nombreExistente = await prisma.configuracionIA.findUnique({
        where: { nombre },
      });

      if (nombreExistente) {
        return NextResponse.json(
          { error: 'Ya existe una configuración con ese nombre' },
          { status: 400 }
        );
      }
    }

    const configuracion = await prisma.configuracionIA.update({
      where: { id },
      data: {
        ...(nombre && { nombre }),
        ...(proveedor && { proveedor }),
        ...(modelo && { modelo }),
        ...(apiKey !== undefined && { apiKey }),
        ...(endpoint !== undefined && { endpoint }),
        ...(parametros && { parametros }),
        ...(promptPorDefecto !== undefined && { promptPorDefecto }),
        ...(limitesUso && { limitesUso }),
        ...(activo !== undefined && { activo }),
      },
    });

    return NextResponse.json({
      success: true,
      data: configuracion,
    });

  } catch (error) {
    console.error('Error al actualizar configuración de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }

    // Verificar que la configuración existe
    const existente = await prisma.configuracionIA.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            generaciones: true,
          },
        },
      },
    });

    if (!existente) {
      return NextResponse.json({ error: 'Configuración no encontrada' }, { status: 404 });
    }

    // No permitir eliminar si tiene generaciones asociadas
    if (existente._count.generaciones > 0) {
      return NextResponse.json(
        { error: 'No se puede eliminar una configuración con generaciones asociadas' },
        { status: 400 }
      );
    }

    await prisma.configuracionIA.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: 'Configuración eliminada exitosamente',
    });

  } catch (error) {
    console.error('Error al eliminar configuración de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
