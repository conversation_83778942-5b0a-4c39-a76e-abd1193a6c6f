import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const ahora = new Date();
    const inicioMes = new Date(ahora.getFullYear(), ahora.getMonth(), 1);
    const inicioSemana = new Date(ahora.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Obtener estadísticas de IA
    const [
      configuracionesActivas,
      totalConfiguraciones,
      generacionesImagen,
      generacionesImagenMes,
      generacionesImagenSemana,
      versionesTexto,
      versionesTextoMes,
      limitesUso,
      usosPorConfiguracion
    ] = await Promise.all([
      // Configuraciones activas
      prisma.configuracionIA.count({ where: { activo: true } }),
      
      // Total configuraciones
      prisma.configuracionIA.count(),
      
      // Generaciones de imagen totales
      prisma.generacionImagen.count(),
      
      // Generaciones de imagen este mes
      prisma.generacionImagen.count({
        where: { createdAt: { gte: inicioMes } }
      }),
      
      // Generaciones de imagen esta semana
      prisma.generacionImagen.count({
        where: { createdAt: { gte: inicioSemana } }
      }),
      
      // Versiones de texto (reescrituras) totales
      prisma.versionNoticia.count(),
      
      // Versiones de texto este mes
      prisma.versionNoticia.count({
        where: { createdAt: { gte: inicioMes } }
      }),
      
      // Límites de uso actuales
      prisma.limiteUsoIA.findMany({
        include: {
          configuracion: {
            select: { nombre: true, modelo: true }
          }
        }
      }),
      
      // Uso por configuración
      prisma.generacionImagen.groupBy({
        by: ['configuracionIAId'],
        _count: { configuracionIAId: true },
        orderBy: { _count: { configuracionIAId: 'desc' } }
      })
    ]);

    // Obtener nombres de configuraciones para el uso
    const configuraciones = await prisma.configuracionIA.findMany({
      select: { id: true, nombre: true, modelo: true, proveedor: true }
    });

    const usosPorConfiguracionConNombres = usosPorConfiguracion.map((uso: any) => {
      const config = configuraciones.find((c: any) => c.id === uso.configuracionIAId);
      return {
        configuracionId: uso.configuracionIAId,
        nombre: config?.nombre || 'Desconocida',
        modelo: config?.modelo || 'N/A',
        proveedor: config?.proveedor || 'N/A',
        usos: uso._count.configuracionIAId
      };
    });

    // Calcular estadísticas de límites
    const limitesEstadisticas = limitesUso.reduce((acc: any, limite: any) => {
      const porcentajeUso = limite.limite > 0 ? Math.round((limite.usado / limite.limite) * 100) : 0;
      
      if (porcentajeUso >= 90) acc.criticos++;
      else if (porcentajeUso >= 70) acc.altos++;
      else acc.normales++;
      
      return acc;
    }, { criticos: 0, altos: 0, normales: 0 });

    // Estadísticas de proveedores
    const proveedoresStats = configuraciones.reduce((acc: any, config: any) => {
      acc[config.proveedor] = (acc[config.proveedor] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const stats = {
      configuraciones: {
        activas: configuracionesActivas,
        total: totalConfiguraciones,
        inactivas: totalConfiguraciones - configuracionesActivas,
        porProveedor: proveedoresStats
      },
      generacionesImagen: {
        total: generacionesImagen,
        esteMes: generacionesImagenMes,
        estaSemana: generacionesImagenSemana,
        promedioDiario: Math.round(generacionesImagenMes / ahora.getDate())
      },
      generacionesTexto: {
        total: versionesTexto,
        esteMes: versionesTextoMes
      },
      limites: {
        total: limitesUso.length,
        estadisticas: limitesEstadisticas,
        detalles: limitesUso.map((limite: any) => ({
          configuracion: limite.configuracion.nombre,
          modelo: limite.configuracion.modelo,
          periodo: limite.periodo,
          usado: limite.usado,
          limite: limite.limite,
          porcentaje: limite.limite > 0 ? Math.round((limite.usado / limite.limite) * 100) : 0
        }))
      },
      usosPorConfiguracion: usosPorConfiguracionConNombres,
      metricas: {
        eficienciaUso: configuracionesActivas > 0 ? Math.round((generacionesImagenMes / configuracionesActivas)) : 0,
        crecimientoSemanal: generacionesImagenSemana > 0 ? Math.round(((generacionesImagenSemana / 7) / (generacionesImagenMes / ahora.getDate())) * 100) : 0
      }
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error al obtener estadísticas de IA:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
