import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener estadísticas de usuarios
    const [
      totalUsuarios,
      usuariosActivos,
      usuariosPorRol,
      usuariosRecientes
    ] = await Promise.all([
      // Total de usuarios
      prisma.user.count(),
      
      // Usuarios activos
      prisma.user.count({ where: { isActive: true } }),
      
      // Usuarios por rol
      prisma.user.groupBy({
        by: ['role'],
        _count: { role: true },
      }),
      
      // Usuarios recientes (últimos 10)
      prisma.user.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          isActive: true,
          createdAt: true,
          _count: {
            select: {
              noticias: true
            }
          }
        }
      })
    ]);

    // Formatear usuarios por rol
    const porRol = usuariosPorRol.reduce((acc: any, item: any) => {
      acc[item.role] = item._count.role;
      return acc;
    }, {} as Record<string, number>);

    // Estadísticas adicionales
    const [
      usuariosConNoticias,
      usuariosSinActividad
    ] = await Promise.all([
      // Usuarios que han creado noticias
      prisma.user.count({
        where: {
          noticias: {
            some: {}
          }
        }
      }),
      
      // Usuarios sin actividad (sin noticias)
      prisma.user.count({
        where: {
          noticias: {
            none: {}
          }
        }
      })
    ]);

    const stats = {
      total: totalUsuarios,
      activos: usuariosActivos,
      inactivos: totalUsuarios - usuariosActivos,
      porRol,
      conNoticias: usuariosConNoticias,
      sinActividad: usuariosSinActividad,
      recientes: usuariosRecientes,
      // Métricas adicionales
      metricas: {
        tasaActividad: totalUsuarios > 0 ? Math.round((usuariosConNoticias / totalUsuarios) * 100) : 0,
        usuariosNuevosEsteMes: await prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        })
      }
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error al obtener estadísticas de usuarios:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
