import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// POST /api/admin/build-project - Compilar proyecto para deploy
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔨 [BUILD] Iniciando compilación del proyecto...');

    try {
      // Compilar el proyecto
      const { stdout, stderr } = await execAsync('npm run build', {
        cwd: process.cwd(),
        timeout: 300000, // 5 minutos timeout
        env: {
          ...process.env,
          NODE_ENV: 'production'
        }
      });

      console.log('✅ [BUILD] Compilación exitosa');
      console.log('📋 Build output:', stdout);
      
      if (stderr) {
        console.log('⚠️ Build warnings:', stderr);
      }

      // Analizar el output para extraer información útil
      const buildInfo = {
        success: true,
        timestamp: new Date().toISOString(),
        output: stdout,
        warnings: stderr || null,
        size: extractBuildSize(stdout),
        routes: extractRoutes(stdout),
        duration: extractBuildTime(stdout)
      };

      return NextResponse.json({
        success: true,
        message: 'Proyecto compilado exitosamente',
        buildInfo,
        nextSteps: [
          '✅ Compilación completada',
          '📦 Archivos listos en .next/',
          '🚀 Listo para deploy',
          '💡 Verificar variables de entorno para producción'
        ],
        deploymentChecklist: [
          'Variables de entorno configuradas',
          'Base de datos accesible',
          'API keys válidas',
          'Dominio configurado'
        ]
      });

    } catch (execError: any) {
      console.error('❌ [BUILD] Error en compilación:', execError);
      
      return NextResponse.json({
        success: false,
        error: 'Error en compilación',
        details: execError.message,
        stdout: execError.stdout || '',
        stderr: execError.stderr || '',
        suggestions: [
          'Verificar errores de TypeScript',
          'Revisar imports faltantes',
          'Comprobar sintaxis de código',
          'Verificar dependencias instaladas'
        ],
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ [BUILD] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Funciones auxiliares para extraer información del build
function extractBuildSize(output: string): string | null {
  const sizeMatch = output.match(/Total size:\s*([^\n]+)/);
  return sizeMatch ? sizeMatch[1] : null;
}

function extractRoutes(output: string): string[] {
  const routes: string[] = [];
  const routeMatches = output.matchAll(/Route \(app\)\s+Size\s+First Load JS\s*\n([\s\S]*?)(?=\n\n|\n$)/g);
  
  for (const match of routeMatches) {
    const routeLines = match[1].split('\n').filter(line => line.trim());
    routes.push(...routeLines.map(line => line.trim()));
  }
  
  return routes;
}

function extractBuildTime(output: string): string | null {
  const timeMatch = output.match(/Compiled successfully in ([^\n]+)/);
  return timeMatch ? timeMatch[1] : null;
}
