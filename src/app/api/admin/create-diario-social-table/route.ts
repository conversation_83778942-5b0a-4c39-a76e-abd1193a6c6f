import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// POST /api/admin/create-diario-social-table - Crear tabla de configuración de diarios
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔧 [CREATE TABLE] Creando tabla diario_social_configs...');

    // Ejecutar SQL para crear la tabla
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "diario_social_configs" (
          "id" SERIAL NOT NULL,
          "diarioId" INTEGER NOT NULL,
          "uploadPostProfile" TEXT NOT NULL,
          "facebookEnabled" BOOLEAN NOT NULL DEFAULT false,
          "twitterEnabled" BOOLEAN NOT NULL DEFAULT false,
          "instagramEnabled" BOOLEAN NOT NULL DEFAULT false,
          "linkedinEnabled" BOOLEAN NOT NULL DEFAULT false,
          "facebookPageId" TEXT,
          "activo" BOOLEAN NOT NULL DEFAULT true,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

          CONSTRAINT "diario_social_configs_pkey" PRIMARY KEY ("id")
      );
    `;

    // Crear índices únicos
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "diario_social_configs_diarioId_key" 
      ON "diario_social_configs"("diarioId");
    `;

    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "diario_social_configs_uploadPostProfile_key" 
      ON "diario_social_configs"("uploadPostProfile");
    `;

    // Agregar foreign key constraint (puede fallar si ya existe)
    try {
      await prisma.$executeRaw`
        ALTER TABLE "diario_social_configs" 
        ADD CONSTRAINT "diario_social_configs_diarioId_fkey" 
        FOREIGN KEY ("diarioId") REFERENCES "diarios"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
      `;
    } catch (error) {
      console.log('⚠️ Foreign key constraint ya existe o no se pudo crear');
    }

    console.log('✅ [CREATE TABLE] Tabla diario_social_configs creada exitosamente');

    return NextResponse.json({
      success: true,
      message: 'Tabla diario_social_configs creada exitosamente',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [CREATE TABLE] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error creando tabla',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
