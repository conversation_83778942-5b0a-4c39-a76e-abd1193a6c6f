import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// POST /api/admin/check-typescript - Verificar errores de TypeScript
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 [TYPESCRIPT] Verificando errores de TypeScript...');

    try {
      // Verificar TypeScript
      const { stdout, stderr } = await execAsync('npx tsc --noEmit', {
        cwd: process.cwd(),
        timeout: 120000, // 2 minutos timeout
      });

      console.log('✅ [TYPESCRIPT] Sin errores de TypeScript');
      
      return NextResponse.json({
        success: true,
        message: 'Sin errores de TypeScript detectados',
        output: stdout,
        warnings: stderr || null,
        readyForBuild: true,
        timestamp: new Date().toISOString()
      });

    } catch (execError: any) {
      console.error('❌ [TYPESCRIPT] Errores encontrados:', execError);
      
      return NextResponse.json({
        success: false,
        error: 'Errores de TypeScript encontrados',
        details: execError.message,
        stdout: execError.stdout || '',
        stderr: execError.stderr || '',
        readyForBuild: false,
        suggestions: [
          'Revisar errores de tipos',
          'Verificar imports faltantes',
          'Comprobar interfaces y tipos',
          'Corregir errores antes del build'
        ],
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ [TYPESCRIPT] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Error verificando TypeScript',
      details: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
