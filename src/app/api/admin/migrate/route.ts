import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// POST /api/admin/migrate - Ejecutar migraciones de Prisma
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario sea administrador
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    console.log('🔧 ADMIN: Iniciando ejecución de migraciones...');

    // Ejecutar migraciones
    const { stdout, stderr } = await execAsync('npx prisma migrate deploy', {
      cwd: process.cwd(),
      env: { ...process.env }
    });

    console.log('✅ ADMIN: Migraciones ejecutadas exitosamente');
    console.log('📋 STDOUT:', stdout);
    if (stderr) {
      console.log('⚠️ STDERR:', stderr);
    }

    return NextResponse.json({
      success: true,
      message: 'Migraciones ejecutadas exitosamente',
      output: stdout,
      warnings: stderr || null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ ADMIN: Error ejecutando migraciones:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    
    return NextResponse.json({
      success: false,
      error: 'Error ejecutando migraciones',
      details: errorMessage,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET /api/admin/migrate - Verificar estado de migraciones
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario sea administrador
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Permisos insuficientes' }, { status: 403 });
    }

    console.log('🔍 ADMIN: Verificando estado de migraciones...');

    // Verificar estado de migraciones
    const { stdout, stderr } = await execAsync('npx prisma migrate status', {
      cwd: process.cwd(),
      env: { ...process.env }
    });

    console.log('📋 ADMIN: Estado de migraciones obtenido');

    return NextResponse.json({
      success: true,
      status: stdout,
      warnings: stderr || null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ ADMIN: Error verificando migraciones:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    
    return NextResponse.json({
      success: false,
      error: 'Error verificando estado de migraciones',
      details: errorMessage,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
