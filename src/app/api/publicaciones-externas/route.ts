import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 GET /api/publicaciones-externas: Iniciando consulta...');

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const estado = searchParams.get('estado');
    const diarioId = searchParams.get('diarioId');
    const fechaDesde = searchParams.get('fechaDesde');
    const fechaHasta = searchParams.get('fechaHasta');

    console.log('📋 Parámetros recibidos:', { page, limit, estado, diarioId, fechaDesde, fechaHasta });
    console.log('🔍 Construyendo filtros...');

    // Construir filtros
    let whereClause: any = {};

    if (estado) {
      whereClause.estado = estado;
    }

    if (diarioId) {
      whereClause.diarioExternoId = parseInt(diarioId);
    }

    if (fechaDesde || fechaHasta) {
      whereClause.createdAt = {};
      if (fechaDesde) {
        whereClause.createdAt.gte = new Date(fechaDesde);
      }
      if (fechaHasta) {
        whereClause.createdAt.lte = new Date(fechaHasta);
      }
    }

    console.log('🔍 Where clause construido:', JSON.stringify(whereClause, null, 2));

    // Obtener total de registros para paginación
    console.log('🔍 Obteniendo total de registros...');
    console.log('📋 Where clause:', JSON.stringify(whereClause, null, 2));
    const total = await prisma.publicacionExterna.count({
      where: whereClause
    });

    console.log(`📊 Total de registros encontrados: ${total}`);

    // Obtener publicaciones con paginación (sin include primero)
    console.log('🔍 Obteniendo publicaciones básicas...');
    const publicacionesBasicas = await prisma.publicacionExterna.findMany({
      where: whereClause,
      select: {
        id: true,
        noticiaId: true,
        diarioExternoId: true,
        imagenExternaId: true,
        articuloExternoId: true,
        urlPublicacion: true,
        estado: true,
        errorMensaje: true,
        metadatos: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    });

    console.log(`📊 Publicaciones básicas obtenidas: ${publicacionesBasicas.length}`);

    // Verificar qué noticias existen
    const noticiaIds = [...new Set(publicacionesBasicas.map(pub => pub.noticiaId))];
    const noticiasExistentes = await prisma.noticia.findMany({
      where: { id: { in: noticiaIds } },
      select: {
        id: true,
        titulo: true,
        imagenUrl: true,
        categoria: {
          select: { nombre: true, color: true }
        },
        user: {
          select: { name: true }
        }
      }
    });

    console.log(`📊 Noticias encontradas: ${noticiasExistentes.length} de ${noticiaIds.length}`);

    // Verificar qué diarios existen
    const diarioIds = [...new Set(publicacionesBasicas.map(pub => pub.diarioExternoId))];
    const diariosExistentes = await prisma.diarioExterno.findMany({
      where: { id: { in: diarioIds } },
      select: {
        id: true,
        nombre: true,
        urlBase: true,
        activo: true
      }
    });

    console.log(`📊 Diarios encontrados: ${diariosExistentes.length} de ${diarioIds.length}`);

    // Combinar datos manualmente
    const publicaciones = publicacionesBasicas.map(pub => {
      const noticia = noticiasExistentes.find(n => n.id === pub.noticiaId);
      const diarioExterno = diariosExistentes.find(d => d.id === pub.diarioExternoId);

      return {
        ...pub,
        noticia: noticia || null,
        diarioExterno: diarioExterno || null
      };
    });

    // Formatear respuesta
    const publicacionesFormateadas = publicaciones.map(pub => {
      // Parsear metadatos si existen
      let metadatos: any = {};
      try {
        metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
      } catch (e) {
        console.warn(`Error parsing metadatos for publicacion ${pub.id}:`, e);
      }

      // Determinar el título correcto a mostrar
      const tituloAMostrar = metadatos.tituloOriginal ||
                            metadatos.tituloPublicado ||
                            pub.noticia?.titulo ||
                            'Título no disponible';

      return {
        id: pub.id,
        noticiaId: pub.noticiaId,
        noticiaTitulo: tituloAMostrar, // ← USAR TÍTULO DE LA VERSIÓN SI EXISTE
        categoria: pub.noticia?.categoria?.nombre || 'Sin categoría',
        categoriaColor: pub.noticia?.categoria?.color || '#gray',
        autor: pub.noticia?.user?.name || 'Autor no disponible',
        diario: {
          id: pub.diarioExterno?.id || 0,
          nombre: pub.diarioExterno?.nombre || 'Diario no disponible',
          urlBase: pub.diarioExterno?.urlBase || '',
          activo: pub.diarioExterno?.activo || false
        },
        estado: pub.estado,
        urlPublicacion: pub.urlPublicacion, // ← ENLACE CREADO
        fechaPublicacion: metadatos.fechaPublicacion || pub.createdAt, // Usar metadatos o createdAt
        fechaCreacion: pub.createdAt,
        mensajeError: pub.errorMensaje,
        // Campos adicionales desde metadatos
        tituloPublicado: tituloAMostrar,
        imagenPublicadaUrl: metadatos.imagenPublicadaUrl || pub.noticia?.imagenUrl || null,
        idExternoArticulo: pub.articuloExternoId,
        // Información de versión
        esVersion: metadatos.esVersion || false,
        versionId: metadatos.versionId || null,
        versionDiario: metadatos.versionDiario || null
      };
    });

    console.log(`✅ Consulta exitosa: ${publicaciones.length} publicaciones encontradas de ${total} total`);

    // Debug adicional para publicaciones exitosas
    if (estado === 'EXITOSO') {
      console.log('🔍 Debug publicaciones exitosas:');
      publicaciones.forEach((pub, index) => {
        console.log(`  ${index + 1}. ID: ${pub.id}, Título: ${pub.noticia?.titulo || 'SIN TÍTULO'}, Estado: ${pub.estado}, URL: ${pub.urlPublicacion || 'SIN URL'}`);
      });
    }

    return NextResponse.json({
      success: true,
      data: publicacionesFormateadas,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      filters: {
        estado,
        diarioId,
        fechaDesde,
        fechaHasta
      }
    });

  } catch (error) {
    console.error('❌ Error al obtener publicaciones externas:', error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'No stack available');

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Endpoint para obtener estadísticas de publicaciones externas
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'stats') {
      // Estadísticas generales
      const stats = await prisma.publicacionExterna.groupBy({
        by: ['estado'],
        _count: {
          id: true
        }
      });

      const statsPorDiario = await prisma.publicacionExterna.groupBy({
        by: ['diarioExternoId'],
        _count: {
          id: true
        }
      });

      return NextResponse.json({
        success: true,
        estadisticas: {
          porEstado: stats,
          porDiario: statsPorDiario,
          total: await prisma.publicacionExterna.count()
        }
      });
    }

    return NextResponse.json({ error: 'Acción no válida' }, { status: 400 });

  } catch (error) {
    console.error('❌ Error en POST /api/publicaciones-externas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
