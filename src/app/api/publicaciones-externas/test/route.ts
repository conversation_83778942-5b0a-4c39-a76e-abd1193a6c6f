import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 TEST: Iniciando prueba de publicaciones externas...');

    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('✅ Autenticación verificada');

    // Paso 1: Verificar conexión básica
    try {
      const count = await prisma.publicacionExterna.count();
      console.log(`✅ Conexión a BD exitosa - ${count} publicaciones externas`);
    } catch (error) {
      console.error('❌ Error en conexión básica:', error);
      return NextResponse.json({
        error: 'Error de conexión a base de datos',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'conexion_basica'
      }, { status: 500 });
    }

    // Paso 2: Verificar consulta simple
    try {
      const publicaciones = await prisma.publicacionExterna.findMany({
        select: {
          id: true,
          estado: true,
          createdAt: true
        },
        take: 5
      });
      console.log(`✅ Consulta simple exitosa - ${publicaciones.length} registros`);
    } catch (error) {
      console.error('❌ Error en consulta simple:', error);
      return NextResponse.json({
        error: 'Error en consulta simple',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'consulta_simple'
      }, { status: 500 });
    }

    // Paso 3: Verificar relaciones
    try {
      const publicacionConRelacion = await prisma.publicacionExterna.findFirst({
        include: {
          noticia: {
            select: { id: true, titulo: true }
          }
        }
      });
      console.log(`✅ Relación con noticia exitosa`);
    } catch (error) {
      console.error('❌ Error en relación con noticia:', error);
      return NextResponse.json({
        error: 'Error en relación con noticia',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'relacion_noticia'
      }, { status: 500 });
    }

    // Paso 4: Verificar relación con diario externo
    try {
      const publicacionConDiario = await prisma.publicacionExterna.findFirst({
        include: {
          diarioExterno: {
            select: { id: true, nombre: true }
          }
        }
      });
      console.log(`✅ Relación con diario externo exitosa`);
    } catch (error) {
      console.error('❌ Error en relación con diario externo:', error);
      return NextResponse.json({
        error: 'Error en relación con diario externo',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'relacion_diario'
      }, { status: 500 });
    }

    // Paso 5: Verificar consulta con filtros
    try {
      const publicacionesExitosas = await prisma.publicacionExterna.findMany({
        where: { estado: 'EXITOSO' },
        select: {
          id: true,
          estado: true,
          urlPublicacion: true
        },
        take: 3
      });
      console.log(`✅ Consulta con filtros exitosa - ${publicacionesExitosas.length} exitosas`);
    } catch (error) {
      console.error('❌ Error en consulta con filtros:', error);
      return NextResponse.json({
        error: 'Error en consulta con filtros',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'consulta_filtros'
      }, { status: 500 });
    }

    // Paso 6: Verificar groupBy
    try {
      const stats = await prisma.publicacionExterna.groupBy({
        by: ['estado'],
        _count: { id: true }
      });
      console.log(`✅ GroupBy exitoso - ${stats.length} estados diferentes`);
    } catch (error) {
      console.error('❌ Error en groupBy:', error);
      return NextResponse.json({
        error: 'Error en groupBy',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'group_by'
      }, { status: 500 });
    }

    // Paso 7: Verificar consulta completa como en la API original
    try {
      const publicacionesCompletas = await prisma.publicacionExterna.findMany({
        where: { estado: 'EXITOSO' },
        include: {
          noticia: {
            select: {
              id: true,
              titulo: true,
              imagenUrl: true,
              categoria: {
                select: { nombre: true, color: true }
              },
              user: {
                select: { name: true }
              }
            }
          },
          diarioExterno: {
            select: {
              id: true,
              nombre: true,
              urlBase: true,
              activo: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 2
      });
      console.log(`✅ Consulta completa exitosa - ${publicacionesCompletas.length} registros`);
    } catch (error) {
      console.error('❌ Error en consulta completa:', error);
      return NextResponse.json({
        error: 'Error en consulta completa',
        details: error instanceof Error ? error.message : 'Error desconocido',
        step: 'consulta_completa'
      }, { status: 500 });
    }

    console.log('🎉 Todas las pruebas pasaron exitosamente');

    return NextResponse.json({
      success: true,
      message: 'Todas las pruebas de publicaciones externas pasaron',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error general en test:', error);
    return NextResponse.json({
      error: 'Error general en test',
      details: error instanceof Error ? error.message : 'Error desconocido',
      stack: error instanceof Error ? error.stack : 'No stack available'
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
