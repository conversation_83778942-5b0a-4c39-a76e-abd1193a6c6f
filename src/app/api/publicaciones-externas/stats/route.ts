import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🔍 GET /api/publicaciones-externas/stats: Iniciando consulta...');

    // Estadísticas generales
    console.log('📊 Obteniendo estadísticas generales...');
    const totalPublicaciones = await prisma.publicacionExterna.count();
    console.log(`✅ Total publicaciones: ${totalPublicaciones}`);

    const publicacionesExitosas = await prisma.publicacionExterna.count({
      where: { estado: 'EXITOSO' }
    });
    console.log(`✅ Publicaciones exitosas: ${publicacionesExitosas}`);

    const publicacionesError = await prisma.publicacionExterna.count({
      where: {
        estado: {
          in: ['ERROR_IMAGEN', 'ERROR_ARTICULO', 'ERROR_CONEXION']
        }
      }
    });
    console.log(`✅ Publicaciones con error: ${publicacionesError}`);

    const publicacionesPendientes = await prisma.publicacionExterna.count({
      where: { estado: 'PENDIENTE' }
    });
    console.log(`✅ Publicaciones pendientes: ${publicacionesPendientes}`);

    // Estadísticas por diario
    console.log('📊 Obteniendo estadísticas por diario...');
    const statsPorDiario = await prisma.publicacionExterna.groupBy({
      by: ['diarioExternoId'],
      _count: {
        id: true
      },
      where: {
        estado: 'EXITOSO'
      }
    });
    console.log(`✅ Stats por diario obtenidas: ${statsPorDiario.length} diarios`);

    // Obtener nombres de diarios
    const diarioIds = statsPorDiario.map(stat => stat.diarioExternoId);
    const diarios = diarioIds.length > 0 ? await prisma.diarioExterno.findMany({
      where: {
        id: {
          in: diarioIds
        }
      },
      select: {
        id: true,
        nombre: true
      }
    }) : [];

    const statsPorDiarioConNombres = statsPorDiario.map(stat => {
      const diario = diarios.find(d => d.id === stat.diarioExternoId);
      return {
        diarioId: stat.diarioExternoId,
        diarioNombre: diario?.nombre || 'Desconocido',
        publicaciones: stat._count.id
      };
    });

    // Publicaciones recientes exitosas (últimas 5) - CONSULTA DEFENSIVA
    console.log('📊 Obteniendo publicaciones recientes...');

    // 1. Obtener publicaciones básicas
    const publicacionesBasicas = await prisma.publicacionExterna.findMany({
      where: { estado: 'EXITOSO' },
      select: {
        id: true,
        noticiaId: true,
        diarioExternoId: true,
        urlPublicacion: true,
        metadatos: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`📊 Publicaciones básicas obtenidas: ${publicacionesBasicas.length}`);

    // 2. Obtener IDs únicos
    const noticiaIds = [...new Set(publicacionesBasicas.map(p => p.noticiaId).filter(Boolean))];
    const diarioIdsRecientes = [...new Set(publicacionesBasicas.map(p => p.diarioExternoId).filter(Boolean))];

    console.log(`📊 IDs a buscar - Noticias: ${noticiaIds.length}, Diarios: ${diarioIdsRecientes.length}`);

    // 3. Obtener noticias existentes
    const noticiasExistentes = noticiaIds.length > 0 ? await prisma.noticia.findMany({
      where: { id: { in: noticiaIds } },
      select: {
        id: true,
        titulo: true,
        categoria: {
          select: { nombre: true, color: true }
        }
      }
    }) : [];

    // 4. Obtener diarios existentes
    const diariosExistentes = diarioIdsRecientes.length > 0 ? await prisma.diarioExterno.findMany({
      where: { id: { in: diarioIdsRecientes } },
      select: { id: true, nombre: true }
    }) : [];

    console.log(`📊 Encontrados - Noticias: ${noticiasExistentes.length}, Diarios: ${diariosExistentes.length}`);

    // 5. Combinar datos manualmente
    const publicacionesRecientes = publicacionesBasicas.map(pub => ({
      ...pub,
      noticia: noticiasExistentes.find(n => n.id === pub.noticiaId) || null,
      diarioExterno: diariosExistentes.find(d => d.id === pub.diarioExternoId) || null
    }));

    // Estadísticas por mes (últimos 6 meses)
    const seisMesesAtras = new Date();
    seisMesesAtras.setMonth(seisMesesAtras.getMonth() - 6);

    const statsPorMes = await prisma.publicacionExterna.groupBy({
      by: ['createdAt'],
      _count: {
        id: true
      },
      where: {
        estado: 'EXITOSO',
        createdAt: {
          gte: seisMesesAtras
        }
      }
    });

    // Agrupar por mes
    const publicacionesPorMes = statsPorMes.reduce((acc: any, stat) => {
      const fecha = new Date(stat.createdAt);
      const mesAno = `${fecha.getFullYear()}-${String(fecha.getMonth() + 1).padStart(2, '0')}`;

      if (!acc[mesAno]) {
        acc[mesAno] = 0;
      }
      acc[mesAno] += stat._count.id;

      return acc;
    }, {});

    // Tasa de éxito
    const tasaExito = totalPublicaciones > 0 ? 
      Math.round((publicacionesExitosas / totalPublicaciones) * 100) : 0;

    console.log(`✅ Estadísticas calculadas: ${totalPublicaciones} total, ${publicacionesExitosas} exitosas`);

    return NextResponse.json({
      success: true,
      estadisticas: {
        general: {
          total: totalPublicaciones,
          exitosas: publicacionesExitosas,
          error: publicacionesError,
          pendientes: publicacionesPendientes,
          tasaExito
        },
        porDiario: statsPorDiarioConNombres,
        porMes: publicacionesPorMes,
        recientes: publicacionesRecientes.map(pub => {
          // Parsear metadatos para obtener el título correcto
          let metadatos: any = {};
          try {
            metadatos = pub.metadatos ? JSON.parse(pub.metadatos) : {};
          } catch (e) {
            console.warn(`Error parsing metadatos for publicacion ${pub.id}:`, e);
          }

          const tituloAMostrar = metadatos.tituloOriginal ||
                                metadatos.tituloPublicado ||
                                pub.noticia?.titulo ||
                                'Título no disponible';

          return {
            id: pub.id,
            titulo: tituloAMostrar, // ← USAR TÍTULO DE LA VERSIÓN SI EXISTE
            diario: pub.diarioExterno?.nombre || 'Diario no disponible',
            categoria: pub.noticia?.categoria?.nombre || 'Sin categoría',
            categoriaColor: pub.noticia?.categoria?.color || '#gray',
            fecha: pub.createdAt,
            url: pub.urlPublicacion,
            esVersion: metadatos.esVersion || false,
            versionDiario: metadatos.versionDiario || null
          };
        })
      }
    });

  } catch (error) {
    console.error('❌ Error al obtener estadísticas de publicaciones externas:', error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'No stack available');

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
