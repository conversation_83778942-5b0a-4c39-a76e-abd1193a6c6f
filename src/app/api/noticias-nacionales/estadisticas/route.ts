/**
 * API endpoint para obtener estadísticas de importación de noticias nacionales
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { obtenerEstadisticasImportacion } from '@/lib/importacion-service';
import { probarConexionExterna } from '@/lib/external-db';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener estadísticas de importación
    const estadisticas = await obtenerEstadisticasImportacion();

    // Probar conexión externa
    const conexionExterna = await probarConexionExterna();

    return NextResponse.json({
      success: true,
      data: {
        ...estadisticas,
        conexionExterna
      }
    });

  } catch (error) {
    console.error('Error al obtener estadísticas de importación:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al obtener estadísticas',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
