/**
 * API endpoint para obtener noticias nacionales de la base de datos externa
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { obtenerNoticiasExternas, probarConexionExterna } from '@/lib/external-db';

export async function GET(request: NextRequest) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const busqueda = searchParams.get('busqueda') || undefined;
    const fechaDesde = searchParams.get('fechaDesde') 
      ? new Date(searchParams.get('fechaDesde')!) 
      : undefined;
    const fechaHasta = searchParams.get('fechaHasta') 
      ? new Date(searchParams.get('fechaHasta')!) 
      : undefined;
    const categoria = searchParams.get('categoria') || undefined;
    const autor = searchParams.get('autor') || undefined;

    // Validar parámetros
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Parámetros de paginación inválidos' },
        { status: 400 }
      );
    }

    // Calcular offset
    const offset = (page - 1) * limit;

    // Probar conexión antes de hacer la consulta
    const conexionOk = await probarConexionExterna();
    if (!conexionOk) {
      return NextResponse.json(
        { 
          error: 'No se puede conectar a la base de datos externa',
          details: 'El servicio de noticias nacionales no está disponible en este momento'
        },
        { status: 503 }
      );
    }

    // Obtener noticias con filtros
    const resultado = await obtenerNoticiasExternas({
      busqueda,
      fechaDesde,
      fechaHasta,
      categoria,
      autor,
      limite: limit,
      offset
    });

    // Calcular información de paginación
    const totalPages = Math.ceil(resultado.total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        noticias: resultado.noticias,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: resultado.total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error en API de noticias nacionales:', error);
    
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint para probar la conexión a la base de datos externa
 */
export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación y permisos de admin
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Solo admins pueden probar la conexión
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Permisos insuficientes' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'test-connection') {
      const conexionOk = await probarConexionExterna();
      
      return NextResponse.json({
        success: true,
        connected: conexionOk,
        message: conexionOk 
          ? 'Conexión exitosa a la base de datos externa'
          : 'No se pudo conectar a la base de datos externa'
      });
    }

    return NextResponse.json(
      { error: 'Acción no válida' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error al probar conexión externa:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al probar la conexión',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
