/**
 * API endpoint para importar una noticia nacional específica al sistema principal
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { obtenerNoticiaExternaPorId } from '@/lib/external-db';
import { importarNoticia } from '@/lib/importacion-service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const noticiaExternaId = parseInt(resolvedParams.id);
    if (isNaN(noticiaExternaId)) {
      return NextResponse.json(
        { error: 'ID de noticia inválido' },
        { status: 400 }
      );
    }

    // Obtener la noticia de la base de datos externa
    const noticiaExterna = await obtenerNoticiaExternaPorId(noticiaExternaId);
    if (!noticiaExterna) {
      return NextResponse.json(
        { error: 'Noticia no encontrada en la base de datos externa' },
        { status: 404 }
      );
    }

    // Importar la noticia usando el servicio
    const resultado = await importarNoticia(noticiaExterna, session.user.id);

    if (!resultado.exito) {
      return NextResponse.json(
        {
          error: resultado.mensaje,
          details: resultado.error
        },
        { status: 400 }
      );
    }

    console.log(`✅ Noticia importada exitosamente: ${resultado.noticiaId} - "${noticiaExterna.titulo}"`);

    return NextResponse.json({
      success: true,
      message: resultado.mensaje,
      data: {
        noticia: {
          id: resultado.noticiaId
        },
        noticiaOriginal: {
          id: noticiaExterna.id,
          titulo: noticiaExterna.titulo,
          autor: noticiaExterna.autor,
          categoria: noticiaExterna.categoria
        }
      }
    });

  } catch (error) {
    console.error('Error al importar noticia nacional:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al importar la noticia',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
