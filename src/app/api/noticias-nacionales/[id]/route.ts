/**
 * API endpoint para obtener una noticia nacional específica de la base de datos externa
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { obtenerNoticiaExternaPorId } from '@/lib/external-db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const noticiaId = parseInt(resolvedParams.id);
    if (isNaN(noticiaId)) {
      return NextResponse.json(
        { error: 'ID de noticia inválido' },
        { status: 400 }
      );
    }

    // Obtener la noticia de la base de datos externa
    const noticia = await obtenerNoticiaExternaPorId(noticiaId);
    
    if (!noticia) {
      return NextResponse.json(
        { error: 'Noticia no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: noticia
    });

  } catch (error) {
    console.error('Error al obtener noticia nacional:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al obtener la noticia',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
