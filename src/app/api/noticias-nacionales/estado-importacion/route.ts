/**
 * API endpoint para verificar el estado de importación de noticias externas
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { obtenerEstadosImportacion } from '@/lib/importacion-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 ENDPOINT /api/noticias-nacionales/estado-importacion EJECUTÁNDOSE');

    // Verificar autenticación
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener los IDs externos del cuerpo de la petición
    const { idsExternos } = await request.json();

    console.log('📋 IDs externos recibidos en el endpoint:', idsExternos);

    if (!idsExternos || !Array.isArray(idsExternos)) {
      console.log('❌ No se recibieron IDs externos válidos');
      return NextResponse.json(
        { error: 'Se requiere un array de IDs externos' },
        { status: 400 }
      );
    }

    // Convertir y validar que todos los IDs sean números válidos
    const idsValidos = idsExternos
      .map(id => typeof id === 'string' ? parseInt(id, 10) : id)
      .filter(id => typeof id === 'number' && !isNaN(id));
    
    if (idsValidos.length === 0) {
      return NextResponse.json(
        { error: 'No se proporcionaron IDs válidos' },
        { status: 400 }
      );
    }

    // Obtener los estados de importación
    const estadosMap = await obtenerEstadosImportacion(idsValidos);

    // Convertir el Map a un objeto para la respuesta JSON
    const estadosObj: Record<number, any> = {};
    estadosMap.forEach((estado, id) => {
      estadosObj[id] = estado;
    });

    const respuesta = {
      success: true,
      data: {
        estados: estadosObj,
        totalConsultados: idsValidos.length,
        yaImportados: Array.from(estadosMap.values()).filter(estado => estado.yaImportada).length
      }
    };

    console.log('📤 RESPUESTA FINAL DEL ENDPOINT:', JSON.stringify(respuesta, null, 2));

    return NextResponse.json(respuesta);

  } catch (error) {
    console.error('Error al verificar estados de importación:', error);
    
    return NextResponse.json(
      { 
        error: 'Error al verificar estados de importación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}
