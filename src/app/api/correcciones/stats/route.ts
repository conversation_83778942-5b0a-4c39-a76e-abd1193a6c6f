import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import type { Prisma } from '@prisma/client';

// Tipo para las estadísticas de correcciones
interface CorreccionStats {
  total: number;
  pendientes: number;
  enRevision: number;
  completadas: number;
  rechazadas: number;
  porPrioridad: {
    baja: number;
    media: number;
    alta: number;
    urgente: number;
  };
  recientes: Array<{
    id: number;
    titulo: string;
    contenido: string;
    estado: string;
    prioridad: string;
    createdAt: Date;
    updatedAt: Date;
    userId: number;
    user: {
      id: number;
      name: string | null;
      email: string;
      role: string;
    };
  }>;
}

// GET /api/correcciones/stats - Obtener estadísticas de correcciones
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Construir filtros base
    const where: Prisma.CorreccionWhereInput = {};
    
    // Si no es admin, solo mostrar sus propias correcciones
    if (session.user.role !== 'ADMIN') {
      where.userId = parseInt(session.user.id);
    }

    // Obtener conteos por estado
    const [total, pendientes, enRevision, completadas, rechazadas] = await Promise.all([
      prisma.correccion.count({ where }),
      prisma.correccion.count({ where: { ...where, estado: 'PENDIENTE' } }),
      prisma.correccion.count({ where: { ...where, estado: 'EN_REVISION' } }),
      prisma.correccion.count({ where: { ...where, estado: 'COMPLETADA' } }),
      prisma.correccion.count({ where: { ...where, estado: 'RECHAZADA' } }),
    ]);

    // Obtener correcciones recientes (últimas 5)
    const recientes = await prisma.correccion.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    // Obtener estadísticas por prioridad
    const [baja, media, alta, urgente] = await Promise.all([
      prisma.correccion.count({ where: { ...where, prioridad: 'BAJA' } }),
      prisma.correccion.count({ where: { ...where, prioridad: 'MEDIA' } }),
      prisma.correccion.count({ where: { ...where, prioridad: 'ALTA' } }),
      prisma.correccion.count({ where: { ...where, prioridad: 'URGENTE' } }),
    ]);

    const stats: CorreccionStats = {
      total,
      pendientes,
      enRevision,
      completadas,
      rechazadas,
      porPrioridad: {
        baja,
        media,
        alta,
        urgente,
      },
      recientes,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error al obtener estadísticas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 