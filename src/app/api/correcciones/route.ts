import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/correcciones - Obtener todas las correcciones con filtros
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const estado = searchParams.get('estado');
    const prioridad = searchParams.get('prioridad');
    const medio = searchParams.get('medio');
    const fechaDesde = searchParams.get('fechaDesde');
    const fechaHasta = searchParams.get('fechaHasta');
    const search = searchParams.get('search');

    // Construir filtros
    const where: any = {};
    
    if (estado) where.estado = estado;
    if (prioridad) where.prioridad = prioridad;
    if (medio) where.medio = { contains: medio, mode: 'insensitive' };
    if (search) {
      where.OR = [
        { titulo: { contains: search, mode: 'insensitive' } },
        { contenido: { contains: search, mode: 'insensitive' } },
        { medio: { contains: search, mode: 'insensitive' } },
      ];
    }
    if (fechaDesde || fechaHasta) {
      where.fechaPublicacion = {};
      if (fechaDesde) where.fechaPublicacion.gte = new Date(fechaDesde);
      if (fechaHasta) where.fechaPublicacion.lte = new Date(fechaHasta);
    }

    // Si no es admin, solo mostrar sus propias correcciones
    if (session.user.role !== 'ADMIN') {
      where.userId = parseInt(session.user.id);
    }

    const correcciones = await prisma.correccion.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(correcciones);
  } catch (error) {
    console.error('Error al obtener correcciones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/correcciones - Crear nueva corrección
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { titulo, contenido, medio, fechaPublicacion, prioridad, observaciones } = body;

    // Validaciones básicas
    if (!titulo || !contenido || !medio || !fechaPublicacion || !prioridad) {
      return NextResponse.json(
        { error: 'Faltan campos requeridos' },
        { status: 400 }
      );
    }

    const correccion = await prisma.correccion.create({
      data: {
        titulo,
        contenido,
        medio,
        fechaPublicacion: new Date(fechaPublicacion),
        fechaCorreccion: new Date(),
        estado: 'PENDIENTE',
        prioridad,
        observaciones,
        userId: parseInt(session.user.id),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return NextResponse.json(correccion, { status: 201 });
  } catch (error) {
    console.error('Error al crear corrección:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 