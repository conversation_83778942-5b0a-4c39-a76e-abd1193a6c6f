import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 POST /api/programaciones: Iniciando creación...');

    const body = await request.json();
    console.log('📋 Body recibido:', body);

    const {
      noticiaId,
      versionId,
      diarioExternoId,
      tipo,
      fechaPublicacion,
      descripcion
    } = body;

    // Validaciones
    if (!noticiaId || !diarioExternoId || !tipo || !fechaPublicacion) {
      return NextResponse.json(
        { error: 'Faltan campos requeridos' },
        { status: 400 }
      );
    }

    // Validar que la fecha sea futura
    const fechaProgramada = new Date(fechaPublicacion);
    const ahora = new Date();
    
    if (fechaProgramada <= ahora) {
      return NextResponse.json(
        { error: 'La fecha de programación debe ser futura' },
        { status: 400 }
      );
    }

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id: noticiaId }
    });

    if (!noticia) {
      return NextResponse.json(
        { error: 'Noticia no encontrada' },
        { status: 404 }
      );
    }

    // Si es una versión, verificar que existe
    if (versionId) {
      const version = await prisma.versionNoticia.findUnique({
        where: { id: versionId }
      });

      if (!version) {
        return NextResponse.json(
          { error: 'Versión no encontrada' },
          { status: 404 }
        );
      }
    }

    // Verificar que el diario externo existe y está activo
    const diarioExterno = await prisma.diarioExterno.findUnique({
      where: { id: diarioExternoId }
    });

    if (!diarioExterno || !diarioExterno.activo) {
      return NextResponse.json(
        { error: 'Diario externo no encontrado o inactivo' },
        { status: 404 }
      );
    }

    // Crear la programación
    console.log('💾 Creando programación con datos:', {
      noticiaId,
      versionId: versionId || null,
      diarioExternoId,
      tipo,
      fechaPublicacion: fechaProgramada,
      descripcion: descripcion || `Programación ${tipo}`,
      estado: 'PENDIENTE'
    });

    const programacion = await prisma.programacionPublicacion.create({
      data: {
        noticiaId,
        versionId: versionId || null,
        diarioExternoId,
        tipo,
        fechaPublicacion: fechaProgramada,
        descripcion: descripcion || `Programación ${tipo}`,
        estado: 'PENDIENTE',
        creadoPor: 'sistema' // Campo correcto según esquema Prisma
      },
      include: {
        noticia: {
          select: { titulo: true }
        },
        version: {
          select: { 
            titulo: true,
            diario: { select: { nombre: true } }
          }
        },
        diarioExterno: {
          select: { nombre: true }
        }
      }
    });

    console.log(`✅ Programación creada: ID ${programacion.id} para ${fechaProgramada.toLocaleString()}`);

    return NextResponse.json({
      success: true,
      programacion
    });

  } catch (error) {
    console.error('❌ Error al crear programación:', error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'No stack available');

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 GET /api/programaciones: Iniciando consulta...');

    const { searchParams } = new URL(request.url);
    const noticiaId = searchParams.get('noticiaId');
    const estado = searchParams.get('estado');

    console.log('📋 Parámetros recibidos:', { noticiaId, estado });

    let whereClause: any = {};

    if (noticiaId) {
      whereClause.noticiaId = parseInt(noticiaId);
    }

    if (estado) {
      whereClause.estado = estado;
    }

    console.log('🔍 Where clause:', whereClause);

    // Primero verificar que la tabla existe
    try {
      const count = await prisma.programacionPublicacion.count();
      console.log(`✅ Tabla programacion_publicaciones existe con ${count} registros`);
    } catch (countError) {
      console.error('❌ Error verificando tabla:', countError);
      throw new Error(`Tabla programacion_publicaciones no accesible: ${countError}`);
    }

    const programaciones = await prisma.programacionPublicacion.findMany({
      where: whereClause,
      include: {
        noticia: {
          select: { titulo: true }
        },
        version: {
          select: {
            titulo: true,
            diario: { select: { nombre: true } }
          }
        },
        diarioExterno: {
          select: { nombre: true }
        }
      },
      orderBy: { fechaPublicacion: 'desc' }
    });

    console.log(`✅ Consulta exitosa: ${programaciones.length} programaciones encontradas`);
    return NextResponse.json(programaciones);

  } catch (error) {
    console.error('❌ Error al obtener programaciones:', error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'No stack available');

    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';

    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
