import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Obtener una programación específica
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const programacionId = parseInt(id);
    if (isNaN(programacionId)) {
      return NextResponse.json({ error: 'ID de programación inválido' }, { status: 400 });
    }

    const programacion = await prisma.programacionPublicacion.findUnique({
      where: { id: programacionId },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            estado: true
          }
        },
        version: {
          select: {
            id: true,
            titulo: true,
            estado: true,
            diario: {
              select: {
                id: true,
                nombre: true
              }
            }
          }
        }
      }
    });

    if (!programacion) {
      return NextResponse.json({ error: 'Programación no encontrada' }, { status: 404 });
    }

    return NextResponse.json(programacion);
  } catch (error) {
    console.error('Error al obtener programación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar una programación
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const programacionId = parseInt(id);
    if (isNaN(programacionId)) {
      return NextResponse.json({ error: 'ID de programación inválido' }, { status: 400 });
    }

    const body = await request.json();
    const { fechaPublicacion, horaPublicacion, descripcion, estado } = body;

    // Verificar que la programación existe
    const programacionExistente = await prisma.programacionPublicacion.findUnique({
      where: { id: programacionId }
    });

    if (!programacionExistente) {
      return NextResponse.json({ error: 'Programación no encontrada' }, { status: 404 });
    }

    // Solo permitir editar programaciones pendientes
    if (programacionExistente.estado !== 'PENDIENTE') {
      return NextResponse.json(
        { error: 'Solo se pueden editar programaciones pendientes' },
        { status: 400 }
      );
    }

    // Validar nueva fecha/hora si se proporciona
    if (fechaPublicacion && horaPublicacion) {
      const fechaHoraProgramada = new Date(`${fechaPublicacion}T${horaPublicacion}`);
      if (fechaHoraProgramada <= new Date()) {
        return NextResponse.json(
          { error: 'La fecha y hora deben ser futuras' },
          { status: 400 }
        );
      }
    }

    // Preparar datos de actualización
    const datosActualizacion: any = {
      updatedAt: new Date()
    };

    if (fechaPublicacion) datosActualizacion.fechaPublicacion = fechaPublicacion;
    if (horaPublicacion) datosActualizacion.horaPublicacion = horaPublicacion;
    if (descripcion) datosActualizacion.descripcion = descripcion;
    if (estado && ['PENDIENTE', 'CANCELADA'].includes(estado)) {
      datosActualizacion.estado = estado;
    }

    const programacionActualizada = await prisma.programacionPublicacion.update({
      where: { id: programacionId },
      data: datosActualizacion,
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            estado: true
          }
        },
        version: {
          select: {
            id: true,
            titulo: true,
            estado: true,
            diario: {
              select: {
                id: true,
                nombre: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json(programacionActualizada);
  } catch (error) {
    console.error('Error al actualizar programación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar una programación
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const programacionId = parseInt(id);
    if (isNaN(programacionId)) {
      return NextResponse.json({ error: 'ID de programación inválido' }, { status: 400 });
    }

    // Verificar que la programación existe
    const programacion = await prisma.programacionPublicacion.findUnique({
      where: { id: programacionId }
    });

    if (!programacion) {
      return NextResponse.json({ error: 'Programación no encontrada' }, { status: 404 });
    }

    // Eliminar la programación
    await prisma.programacionPublicacion.delete({
      where: { id: programacionId }
    });

    return NextResponse.json({ message: 'Programación eliminada exitosamente' });
  } catch (error) {
    console.error('Error al eliminar programación:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
