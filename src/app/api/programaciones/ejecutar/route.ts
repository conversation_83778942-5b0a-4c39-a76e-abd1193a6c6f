import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// POST - Ejecutar programaciones pendientes
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener programaciones pendientes que ya deberían ejecutarse
    const ahora = new Date();
    const programacionesPendientes = await prisma.programacionPublicacion.findMany({
      where: {
        estado: 'PENDIENTE',
        fechaPublicacion: {
          lte: ahora
        }
      },
      include: {
        noticia: true,
        version: {
          include: {
            diario: true
          }
        },
        diarioExterno: {
          include: {
            categoriaMapeos: true
          }
        }
      },
      orderBy: {
        fechaPublicacion: 'asc'
      }
    });

    if (programacionesPendientes.length === 0) {
      return NextResponse.json({
        message: 'No hay programaciones pendientes para ejecutar',
        ejecutadas: 0
      });
    }

    const resultados = [];

    for (const programacion of programacionesPendientes) {
      try {
        // Marcar como ejecutando
        await prisma.programacionPublicacion.update({
          where: { id: programacion.id },
          data: { estado: 'EJECUTANDO' }
        });

        if (programacion.tipo === 'ORIGINAL') {
          // Publicar noticia original
          await prisma.noticia.update({
            where: { id: programacion.noticiaId },
            data: {
              publicada: true,
              fechaPublicacion: new Date()
            }
          });

          // Si hay un diario externo configurado, también publicar externamente
          if (programacion.diarioExternoId && programacion.diarioExterno) {
            console.log(`🌐 PROGRAMACION: Publicando noticia original ${programacion.noticiaId} a diario externo ${programacion.diarioExterno.nombre}`);

            const resultadoPublicacion = await ejecutarPublicacionExternaViaAPI(
              programacion.noticiaId,
              programacion.diarioExterno.id,
              null // Sin versionId para noticia original
            );

            if (resultadoPublicacion.success) {
              // Marcar como completada con metadatos de publicación externa
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'COMPLETADA',
                  ejecutadoEn: new Date(),
                  metadatos: JSON.stringify({
                    publicadoEn: 'externo',
                    urlPublicacion: resultadoPublicacion.urlPublicacion
                  })
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'ORIGINAL_EXTERNA',
                titulo: programacion.noticia.titulo,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'COMPLETADA',
                urlPublicacion: resultadoPublicacion.urlPublicacion
              });

              console.log(`✅ PROGRAMACION: Programación ${programacion.id} (ORIGINAL_EXTERNA) ejecutada exitosamente`);
            } else {
              // Marcar como error
              await prisma.programacionPublicacion.update({
                where: { id: programacion.id },
                data: {
                  estado: 'ERROR',
                  ejecutadoEn: new Date(),
                  errorMensaje: resultadoPublicacion.error
                }
              });

              resultados.push({
                id: programacion.id,
                tipo: 'ORIGINAL_EXTERNA',
                titulo: programacion.noticia.titulo,
                diarioExterno: programacion.diarioExterno.nombre,
                estado: 'ERROR',
                error: resultadoPublicacion.error
              });

              console.log(`❌ PROGRAMACION: Programación ${programacion.id} (ORIGINAL_EXTERNA) falló: ${resultadoPublicacion.error}`);
            }
          } else {
            // Solo publicación local (sin diario externo)
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'COMPLETADA',
                ejecutadoEn: new Date()
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'ORIGINAL',
              titulo: programacion.noticia.titulo,
              estado: 'COMPLETADA'
            });

            console.log(`✅ PROGRAMACION: Programación ${programacion.id} (ORIGINAL) ejecutada exitosamente`);
          }

        } else if (programacion.tipo === 'VERSION' && programacion.version) {
          // Publicar versión específica
          await prisma.versionNoticia.update({
            where: { id: programacion.versionId! },
            data: {
              estadoPublicacion: 'PUBLICADA',
              urlPublicacion: `${process.env.NEXT_PUBLIC_BASE_URL}/versiones/${programacion.versionId}`
            }
          });

          // Marcar como completada
          await prisma.programacionPublicacion.update({
            where: { id: programacion.id },
            data: {
              estado: 'COMPLETADA',
              ejecutadoEn: new Date()
            }
          });

          resultados.push({
            id: programacion.id,
            tipo: 'VERSION',
            titulo: programacion.noticia.titulo,
            diario: programacion.version.diario.nombre,
            estado: 'COMPLETADA'
          });

        } else if (programacion.tipo === 'EXTERNA' && programacion.diarioExterno) {
          // Publicar a diario externo usando la misma API que la publicación manual
          const resultadoPublicacion = await ejecutarPublicacionExternaViaAPI(
            programacion.noticiaId,
            programacion.diarioExterno.id,
            programacion.versionId
          );

          if (resultadoPublicacion.success) {
            // Marcar como completada
            await prisma.programacionPublicacion.update({
              where: { id: programacion.id },
              data: {
                estado: 'COMPLETADA',
                ejecutadoEn: new Date(),
                metadatos: JSON.stringify({
                  ...(programacion.metadatos ? JSON.parse(programacion.metadatos as string) : {}),
                  urlPublicacion: resultadoPublicacion.urlPublicacion,
                  articuloExternoId: resultadoPublicacion.articuloId
                })
              }
            });

            resultados.push({
              id: programacion.id,
              tipo: 'EXTERNA',
              titulo: programacion.noticia.titulo,
              diario: programacion.diarioExterno.nombre,
              estado: 'COMPLETADA',
              urlPublicacion: resultadoPublicacion.urlPublicacion
            });
          } else {
            throw new Error(resultadoPublicacion.error);
          }
        }

      } catch (error) {
        console.error(`Error ejecutando programación ${programacion.id}:`, error);
        
        // Marcar como error
        await prisma.programacionPublicacion.update({
          where: { id: programacion.id },
          data: {
            estado: 'ERROR',
            errorMensaje: error instanceof Error ? error.message : 'Error desconocido',
            ejecutadoEn: new Date()
          }
        });

        resultados.push({
          id: programacion.id,
          tipo: programacion.tipo,
          titulo: programacion.noticia.titulo,
          estado: 'ERROR',
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    return NextResponse.json({
      message: `${resultados.filter(r => r.estado === 'COMPLETADA').length} programaciones ejecutadas exitosamente`,
      ejecutadas: resultados.filter(r => r.estado === 'COMPLETADA').length,
      errores: resultados.filter(r => r.estado === 'ERROR').length,
      resultados
    });

  } catch (error) {
    console.error('Error ejecutando programaciones:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// GET - Verificar programaciones pendientes
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const ahora = new Date();
    const programacionesPendientes = await prisma.programacionPublicacion.findMany({
      where: {
        estado: 'PENDIENTE',
        fechaPublicacion: {
          lte: ahora
        }
      },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true
          }
        },
        version: {
          include: {
            diario: {
              select: {
                id: true,
                nombre: true
              }
            }
          }
        }
      },
      orderBy: {
        fechaPublicacion: 'asc'
      }
    });

    return NextResponse.json({
      pendientes: programacionesPendientes.length,
      programaciones: programacionesPendientes
    });

  } catch (error) {
    console.error('Error obteniendo programaciones pendientes:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función auxiliar para ejecutar publicación externa usando la misma API que la publicación manual
async function ejecutarPublicacionExternaViaAPI(
  noticiaId: number,
  diarioExternoId: number,
  versionId?: number | null
) {
  try {
    console.log(`🤖 PROGRAMACION: Ejecutando publicación externa para noticia ${noticiaId} en diario ${diarioExternoId}${versionId ? ` con versión ${versionId}` : ''}`);

    // Preparar el payload exactamente como lo hace la publicación manual
    const payload: any = {
      diarioExternoId: diarioExternoId
    };

    // Solo agregar versionId si está presente
    if (versionId) {
      payload.versionId = versionId;
    }

    console.log(`🤖 PROGRAMACION: Payload para API:`, payload);

    // Hacer la llamada a la misma API que usa la publicación manual
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3018'}/api/noticias/${noticiaId}/publish-external`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    console.log(`🤖 PROGRAMACION: Respuesta de API:`, {
      status: response.status,
      success: result.success,
      error: result.error
    });

    if (response.ok && result.success) {
      return {
        success: true,
        urlPublicacion: result.data?.urlPublicacion,
        articuloId: result.data?.articuloExternoId,
        message: 'Publicación exitosa via API'
      };
    } else if (response.status === 409) {
      // Ya publicado anteriormente
      return {
        success: true,
        urlPublicacion: result.data?.urlPublicacion,
        articuloId: result.data?.articuloExternoId,
        message: 'Ya publicado anteriormente'
      };
    } else {
      throw new Error(result.error || `Error HTTP ${response.status}`);
    }

  } catch (error) {
    console.error('🤖 PROGRAMACION: Error en publicación externa via API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}


