import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 TEST: Verificando publicaciones externas...');

    // Consulta más simple posible
    const publicaciones = await prisma.publicacionExterna.findMany({
      select: {
        id: true,
        noticiaId: true,
        diarioExternoId: true,
        estado: true,
        urlPublicacion: true,
        createdAt: true,
        metadatos: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`🧪 Encontradas ${publicaciones.length} publicaciones`);

    const exitosas = publicaciones.filter(p => p.estado === 'EXITOSO');
    console.log(`🧪 De las cuales ${exitosas.length} son exitosas`);

    // Consulta con relaciones
    const conRelaciones = await prisma.publicacionExterna.findMany({
      where: { estado: 'EXITOSO' },
      include: {
        noticia: {
          select: {
            id: true,
            titulo: true,
            estado: true
          }
        },
        diarioExterno: {
          select: {
            id: true,
            nombre: true,
            activo: true
          }
        }
      },
      take: 5
    });

    console.log(`🧪 Con relaciones: ${conRelaciones.length} publicaciones exitosas`);

    return NextResponse.json({
      success: true,
      data: {
        totalPublicaciones: publicaciones.length,
        publicacionesExitosas: exitosas.length,
        conRelaciones: conRelaciones.length,
        muestra: publicaciones.map(p => ({
          id: p.id,
          noticiaId: p.noticiaId,
          estado: p.estado,
          tieneUrl: !!p.urlPublicacion,
          fechaCreacion: p.createdAt,
          tieneMetadatos: !!p.metadatos
        })),
        exitosasConRelaciones: conRelaciones.map(p => ({
          id: p.id,
          titulo: p.noticia?.titulo,
          diario: p.diarioExterno?.nombre,
          diarioActivo: p.diarioExterno?.activo,
          noticiaEstado: p.noticia?.estado,
          url: p.urlPublicacion
        }))
      }
    });

  } catch (error) {
    console.error('❌ Error en test:', error);
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
