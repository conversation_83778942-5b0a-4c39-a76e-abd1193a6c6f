import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Global ignores (equivalent to .eslintignore)
  {
    ignores: [
      // Dependencies
      "node_modules/**",
      ".pnpm-store/**",

      // Build outputs
      ".next/**",
      "out/**",
      "dist/**",
      "build/**",

      // Environment files
      ".env*",

      // Database
      "prisma/dev.db",
      "prisma/migrations/**",

      // Logs
      "*.log",
      "npm-debug.log*",
      "yarn-debug.log*",
      "yarn-error.log*",

      // Coverage and test outputs
      "coverage/**",
      ".nyc_output/**",

      // Temporary and cache directories
      "tmp/**",
      "temp/**",
      ".npm/**",

      // OS and IDE files
      ".DS_Store",
      ".vscode/**",
      ".idea/**",
      "*.swp",
      "*.swo",

      // Generated and backup files
      "*.d.ts.map",
      "*.js.map",
      "*.backup",
      "*.bak",
      "*.orig"
    ]
  },

  // Extend Next.js recommended configurations
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  // Global configuration for all files
  {
    rules: {
      // TypeScript specific rules
      "@typescript-eslint/no-unused-vars": ["warn", {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        caughtErrorsIgnorePattern: "^_"
      }],
      "@typescript-eslint/no-explicit-any": "warn",

      // React specific rules
      "react-hooks/exhaustive-deps": "warn",
      "react/no-unescaped-entities": "warn",

      // General JavaScript rules
      "no-console": ["warn", { allow: ["warn", "error"] }],
      "prefer-const": "warn",
      "no-var": "error",

      // Next.js specific rules
      "@next/next/no-img-element": "warn",
      "@next/next/no-html-link-for-pages": "warn"
    }
  },

  // Configuration for specific file patterns
  {
    files: ["**/*.ts", "**/*.tsx"],
    rules: {
      // Stricter TypeScript rules for .ts/.tsx files
      "@typescript-eslint/no-explicit-any": "error"
    }
  },

  // Configuration for development and script files
  {
    files: ["scripts/**/*", "*.config.*", "*.mjs", "*.js"],
    rules: {
      // Allow console in scripts and config files
      "no-console": "off",
      "@typescript-eslint/no-explicit-any": "warn"
    }
  },

  // Configuration for test files (if you add them later)
  {
    files: ["**/*.test.*", "**/*.spec.*", "**/__tests__/**/*"],
    rules: {
      // More relaxed rules for test files
      "@typescript-eslint/no-explicit-any": "warn",
      "no-console": "off"
    }
  }
];

export default eslintConfig;
