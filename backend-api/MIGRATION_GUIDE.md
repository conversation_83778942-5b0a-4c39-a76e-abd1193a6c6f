# 🔄 GUÍA DE MIGRACIÓN - SEPARACIÓN BACKEND/FRONTEND

## 📋 RESUMEN DE LA MIGRACIÓN

Esta guía documenta la separación completa del monolito Next.js en un backend API independiente y un frontend SPA.

### **🏗️ ARQUITECTURA ANTERIOR**
- **Monolito Next.js**: Frontend + Backend en una sola aplicación
- **API Routes**: `/src/app/api/*` integradas en Next.js
- **Autenticación**: NextAuth.js con sesiones
- **Base de datos**: Acceso directo desde componentes

### **🚀 NUEVA ARQUITECTURA**
- **Backend API**: Express.js + TypeScript independiente
- **Frontend SPA**: Next.js puro consumiendo API
- **Autenticación**: JWT tokens con refresh
- **Base de datos**: Solo accesible desde backend

---

## 🔧 SERVICIOS MIGRADOS

### **1. AI Services**
**Antes**: `src/lib/ai-service.ts`, `src/lib/ai-image-service.ts`
**Ahora**: `backend-api/src/services/ai-service.ts`, `backend-api/src/services/ai-image-service.ts`

**Cambios principales**:
- ✅ Soporte para OpenAI y Google Gemini
- ✅ Fallback automático entre proveedores
- ✅ Gestión de límites de uso
- ✅ Optimización de prompts
- ✅ Manejo de errores mejorado

### **2. External Publication Service**
**Antes**: `src/lib/external-publication-service.ts`
**Ahora**: `backend-api/src/services/external-publication-service.ts`

**Cambios principales**:
- ✅ Publicación asíncrona con estados
- ✅ Manejo de errores granular
- ✅ Reintentos automáticos
- ✅ Mapeo de categorías
- ✅ Subida de imágenes optimizada

### **3. Notification Service**
**Antes**: Integrado en componentes
**Ahora**: `backend-api/src/services/notification-service.ts`

**Cambios principales**:
- ✅ Notificaciones masivas
- ✅ Filtros avanzados
- ✅ Limpieza automática
- ✅ Estadísticas detalladas
- ✅ Tipos de notificación

### **4. Webhook Service**
**Antes**: `src/app/api/webhooks/*`
**Ahora**: `backend-api/src/services/webhook-service.ts`

**Cambios principales**:
- ✅ Validación de tokens segura
- ✅ Procesamiento asíncrono
- ✅ Estadísticas de uso
- ✅ Manejo de errores robusto
- ✅ Creación automática de categorías

### **5. Image Service**
**Antes**: `src/lib/image-upload-service.ts`, `src/lib/image-optimization-service.ts`
**Ahora**: `backend-api/src/services/image-service.ts`

**Cambios principales**:
- ✅ Optimización automática con Sharp
- ✅ Múltiples formatos (JPEG, PNG, WebP)
- ✅ Detección de formato óptimo
- ✅ Validación robusta
- ✅ Estadísticas de compresión

---

## 📡 ENDPOINTS DE LA API

### **Autenticación**
```
POST   /api/auth/login           # Iniciar sesión
POST   /api/auth/refresh         # Renovar token
GET    /api/auth/me              # Usuario actual
POST   /api/auth/change-password # Cambiar contraseña
POST   /api/auth/logout          # Cerrar sesión
```

### **Noticias**
```
GET    /api/noticias             # Listar noticias
GET    /api/noticias/:id         # Obtener noticia
POST   /api/noticias             # Crear noticia
PUT    /api/noticias/:id         # Actualizar noticia
DELETE /api/noticias/:id         # Eliminar noticia
GET    /api/noticias/:id/stats   # Estadísticas
```

### **Inteligencia Artificial**
```
POST   /api/ai/rewrite           # Reescribir noticia
POST   /api/ai/generate-image    # Generar imagen
GET    /api/ai/image-generation/:id # Estado generación
GET    /api/ai/models            # Modelos disponibles
```

### **Publicación Externa**
```
POST   /api/external-publication/publish      # Publicar
GET    /api/external-publication/status/:id   # Estado
GET    /api/external-publication/noticia/:id  # Publicaciones
POST   /api/external-publication/retry/:id    # Reintentar
POST   /api/external-publication/test-connection # Probar conexión
GET    /api/external-publication/diarios      # Diarios disponibles
GET    /api/external-publication/stats        # Estadísticas
```

### **Subida de Archivos**
```
POST   /api/upload/image         # Subir imagen
POST   /api/upload/images        # Subir múltiples
DELETE /api/upload/image/:filename # Eliminar imagen
```

### **Webhooks**
```
POST   /api/webhooks/noticia     # Recibir noticia
GET    /api/webhooks/noticia     # Información
POST   /api/webhooks/test        # Webhook de prueba
GET    /api/webhooks/stats       # Estadísticas
```

---

## 🔐 CAMBIOS EN AUTENTICACIÓN

### **Antes (NextAuth.js)**
```javascript
// Verificar sesión
const session = await getServerSession(authOptions);
if (!session) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}

// Acceder a usuario
const userId = session.user.id;
```

### **Ahora (JWT)**
```javascript
// Middleware de autenticación
app.use('/api', authenticateToken);

// En rutas protegidas
router.get('/protected', authenticateToken, (req: AuthenticatedRequest, res) => {
  const userId = req.user!.userId;
  const userRole = req.user!.role;
});
```

---

## 🗄️ CAMBIOS EN BASE DE DATOS

### **Antes**
```javascript
// Acceso directo desde componentes
import { prisma } from '@/lib/prisma';

const noticias = await prisma.noticia.findMany();
```

### **Ahora**
```javascript
// Solo desde servicios del backend
import { prisma } from '@/config/database';

// En servicios
export class NoticiaService {
  static async getNoticias() {
    return await prisma.noticia.findMany();
  }
}
```

---

## 🚀 PASOS PARA COMPLETAR LA MIGRACIÓN

### **1. Configurar Backend**
```bash
cd backend-api
npm install
cp .env.example .env
# Configurar variables de entorno
npm run db:generate
npm run db:migrate
npm run dev
```

### **2. Actualizar Frontend**
```bash
# Instalar cliente HTTP
npm install axios

# Crear servicio de API
// src/lib/api-client.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

// Interceptor para tokens
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### **3. Migrar Componentes**
```javascript
// Antes - Server Component con Prisma
async function NoticiasPage() {
  const noticias = await prisma.noticia.findMany();
  return <NoticiasList noticias={noticias} />;
}

// Ahora - Client Component con API
'use client';
function NoticiasPage() {
  const [noticias, setNoticias] = useState([]);
  
  useEffect(() => {
    apiClient.get('/api/noticias')
      .then(res => setNoticias(res.data.data.items));
  }, []);
  
  return <NoticiasList noticias={noticias} />;
}
```

### **4. Actualizar Autenticación**
```javascript
// Crear contexto de autenticación
const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  
  const login = async (credentials) => {
    const response = await apiClient.post('/api/auth/login', credentials);
    const { token, refreshToken, user } = response.data.data;
    
    localStorage.setItem('accessToken', token);
    localStorage.setItem('refreshToken', refreshToken);
    setToken(token);
    setUser(user);
  };
  
  return (
    <AuthContext.Provider value={{ user, token, login }}>
      {children}
    </AuthContext.Provider>
  );
}
```

---

## ✅ CHECKLIST DE MIGRACIÓN

### **Backend**
- [x] ✅ Configuración de Express.js + TypeScript
- [x] ✅ Middleware de autenticación JWT
- [x] ✅ Middleware de validación con Zod
- [x] ✅ Middleware de seguridad (CORS, Helmet, Rate Limiting)
- [x] ✅ Rutas de autenticación
- [x] ✅ Rutas CRUD para noticias
- [x] ✅ Rutas de categorías
- [x] ✅ Servicios de IA migrados
- [x] ✅ Servicio de publicación externa
- [x] ✅ Servicio de notificaciones
- [x] ✅ Servicio de webhooks
- [x] ✅ Servicio de imágenes
- [x] ✅ Health checks
- [x] ✅ Manejo de errores centralizado
- [x] ✅ Logging estructurado
- [x] ✅ Documentación completa

### **Frontend (Pendiente)**
- [ ] 🔄 Configurar cliente HTTP (axios)
- [ ] 🔄 Crear contexto de autenticación
- [ ] 🔄 Migrar componentes a Client Components
- [ ] 🔄 Actualizar formularios para usar API
- [ ] 🔄 Implementar manejo de estados global
- [ ] 🔄 Actualizar sistema de notificaciones
- [ ] 🔄 Migrar páginas de administración
- [ ] 🔄 Actualizar sistema de subida de archivos
- [ ] 🔄 Implementar refresh de tokens
- [ ] 🔄 Actualizar tests

### **Infraestructura (Pendiente)**
- [ ] 🔄 Configurar despliegue separado
- [ ] 🔄 Configurar variables de entorno
- [ ] 🔄 Configurar proxy reverso
- [ ] 🔄 Configurar monitoreo
- [ ] 🔄 Configurar backups
- [ ] 🔄 Documentar proceso de despliegue

---

## 🎯 BENEFICIOS OBTENIDOS

### **Escalabilidad**
- ✅ Backend y frontend pueden escalar independientemente
- ✅ Múltiples frontends pueden consumir la misma API
- ✅ Microservicios futuros más fáciles de implementar

### **Mantenibilidad**
- ✅ Separación clara de responsabilidades
- ✅ Código más organizado y modular
- ✅ Testing más fácil y específico

### **Seguridad**
- ✅ Base de datos solo accesible desde backend
- ✅ Autenticación JWT más segura
- ✅ Rate limiting y validación robusta

### **Performance**
- ✅ Frontend más ligero (SPA)
- ✅ Caching más eficiente
- ✅ Optimización de imágenes automática

---

## 📞 SOPORTE

Para dudas sobre la migración:
1. Revisar esta documentación
2. Consultar logs del backend
3. Verificar configuración de variables de entorno
4. Revisar documentación de APIs en `/api/docs`

**¡Migración del backend completada exitosamente! 🎉**
