{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/config/*": ["./config/*"], "@/controllers/*": ["./controllers/*"], "@/services/*": ["./services/*"], "@/middleware/*": ["./middleware/*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}