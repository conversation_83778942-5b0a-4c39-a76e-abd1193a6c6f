import { prisma } from '@/config/database';
import { NotificationService } from './notification-service';

export interface WebhookNoticiaData {
  titulo: string;
  volanta?: string;
  subtitulo?: string;
  resumen?: string;
  contenido: string;
  autor?: string;
  periodista?: string;
  fuente?: string;
  urlFuente?: string;
  imagenUrl?: string;
  imagenAlt?: string;
  categoria?: string;
  destacada?: boolean;
}

export interface ProcessedWebhookResult {
  success: boolean;
  noticiaId?: number;
  categoriaId?: number;
  notificationsCreated: number;
  message: string;
  errors?: string[];
}

export interface WebhookStats {
  total: number;
  today: number;
  thisWeek: number;
  thisMonth: number;
  bySource: Record<string, number>;
  recentActivity: Array<{
    date: string;
    count: number;
  }>;
}

export class WebhookService {
  // Procesar noticia recibida desde webhook
  static async processWebhookNoticia(
    data: WebhookNoticiaData,
    source: string = 'webhook'
  ): Promise<ProcessedWebhookResult> {
    const errors: string[] = [];
    let noticiaId: number | undefined;
    let categoriaId: number | undefined;
    let notificationsCreated = 0;

    try {
      console.log('📥 Procesando noticia desde webhook:', {
        titulo: data.titulo.substring(0, 50) + '...',
        periodista: data.periodista,
        fuente: data.fuente,
        source,
        timestamp: new Date().toISOString(),
      });

      // Validar datos requeridos
      if (!data.titulo || data.titulo.trim().length === 0) {
        errors.push('Título es requerido');
      }

      if (!data.contenido || data.contenido.trim().length === 0) {
        errors.push('Contenido es requerido');
      }

      if (errors.length > 0) {
        return {
          success: false,
          message: 'Datos inválidos',
          errors,
          notificationsCreated: 0,
        };
      }

      // Buscar o crear categoría
      if (data.categoria) {
        try {
          let categoriaObj = await prisma.categoria.findFirst({
            where: {
              nombre: {
                equals: data.categoria.trim(),
                mode: 'insensitive',
              },
              isActive: true,
            },
          });

          if (!categoriaObj) {
            categoriaObj = await prisma.categoria.create({
              data: {
                nombre: data.categoria.trim(),
                descripcion: `Categoría creada automáticamente desde webhook (${source})`,
                color: this.generateCategoryColor(data.categoria),
                isActive: true,
              },
            });
            console.log(`✅ Categoría creada: ${categoriaObj.nombre} (ID: ${categoriaObj.id})`);
          }

          categoriaId = categoriaObj.id;
        } catch (categoryError) {
          console.error('Error procesando categoría:', categoryError);
          errors.push(`Error al procesar categoría: ${categoryError instanceof Error ? categoryError.message : 'Error desconocido'}`);
        }
      }

      // Buscar usuario por defecto para webhooks (primer admin activo)
      const defaultUser = await prisma.user.findFirst({
        where: {
          role: 'ADMIN',
          isActive: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      if (!defaultUser) {
        errors.push('No se encontró un usuario administrador activo para asignar la noticia');
        return {
          success: false,
          message: 'Error de configuración del sistema',
          errors,
          notificationsCreated: 0,
        };
      }

      // Crear noticia
      const noticia = await prisma.noticia.create({
        data: {
          titulo: data.titulo.trim(),
          volanta: data.volanta?.trim() || null,
          subtitulo: data.subtitulo?.trim() || null,
          resumen: data.resumen?.trim() || null,
          contenido: data.contenido.trim(),
          imagenUrl: data.imagenUrl?.trim() || null,
          imagenAlt: data.imagenAlt?.trim() || null,
          autor: data.autor?.trim() || null,
          fuente: data.fuente?.trim() || null,
          urlFuente: data.urlFuente?.trim() || null,
          periodista: data.periodista?.trim() || null,
          categoriaId,
          destacada: data.destacada || false,
          estado: 'BORRADOR',
          publicada: false,
          origen: 'WEBHOOK',
          userId: defaultUser.id,
          webhookData: JSON.stringify({
            receivedAt: new Date().toISOString(),
            source,
            originalData: data,
            processedBy: 'webhook-service',
          }),
        },
      });

      noticiaId = noticia.id;
      console.log(`✅ Noticia creada desde webhook: ID ${noticia.id}`);

      // Crear notificaciones para editores y admins
      try {
        const notificationResult = await NotificationService.notifyWebhookNoticia(
          noticia.id,
          noticia.titulo,
          data.periodista,
          data.fuente
        );
        notificationsCreated = notificationResult.created;
      } catch (notificationError) {
        console.error('Error creando notificaciones:', notificationError);
        errors.push(`Error al crear notificaciones: ${notificationError instanceof Error ? notificationError.message : 'Error desconocido'}`);
      }

      // Registrar estadísticas del webhook
      await this.recordWebhookActivity(source, 'noticia', true);

      const message = `Noticia procesada exitosamente. ID: ${noticia.id}${categoriaId ? `, Categoría: ${data.categoria}` : ''}. ${notificationsCreated} notificaciones enviadas.`;

      return {
        success: true,
        noticiaId: noticia.id,
        categoriaId,
        notificationsCreated,
        message,
        errors: errors.length > 0 ? errors : undefined,
      };

    } catch (error) {
      console.error('❌ Error procesando webhook noticia:', error);
      
      // Registrar error en estadísticas
      await this.recordWebhookActivity(source, 'noticia', false);

      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      errors.push(errorMessage);

      return {
        success: false,
        noticiaId,
        categoriaId,
        notificationsCreated,
        message: `Error procesando noticia: ${errorMessage}`,
        errors,
      };
    }
  }

  // Generar color para categoría basado en el nombre
  private static generateCategoryColor(categoryName: string): string {
    const colors = [
      '#3B82F6', // Blue
      '#10B981', // Green
      '#F59E0B', // Yellow
      '#EF4444', // Red
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#84CC16', // Lime
      '#F97316', // Orange
      '#EC4899', // Pink
      '#6B7280', // Gray
    ];

    // Usar hash simple del nombre para seleccionar color consistente
    let hash = 0;
    for (let i = 0; i < categoryName.length; i++) {
      hash = categoryName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  // Registrar actividad del webhook para estadísticas
  private static async recordWebhookActivity(
    source: string,
    type: string,
    success: boolean
  ): Promise<void> {
    try {
      // Esto podría ser una tabla separada para estadísticas más detalladas
      // Por ahora, solo loggeamos la actividad
      console.log(`📊 Webhook activity: ${source} - ${type} - ${success ? 'SUCCESS' : 'ERROR'}`);
    } catch (error) {
      console.error('Error recording webhook activity:', error);
      // No lanzar error aquí para no afectar el procesamiento principal
    }
  }

  // Obtener estadísticas de webhooks
  static async getWebhookStats(): Promise<WebhookStats> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const [total, todayCount, weekCount, monthCount] = await Promise.all([
        prisma.noticia.count({
          where: { origen: 'WEBHOOK' },
        }),
        prisma.noticia.count({
          where: {
            origen: 'WEBHOOK',
            createdAt: { gte: today },
          },
        }),
        prisma.noticia.count({
          where: {
            origen: 'WEBHOOK',
            createdAt: { gte: thisWeek },
          },
        }),
        prisma.noticia.count({
          where: {
            origen: 'WEBHOOK',
            createdAt: { gte: thisMonth },
          },
        }),
      ]);

      // Obtener actividad reciente (últimos 7 días)
      const recentActivity = await this.getRecentWebhookActivity(7);

      // Obtener estadísticas por fuente
      const bySourceData = await prisma.noticia.groupBy({
        by: ['fuente'],
        where: {
          origen: 'WEBHOOK',
          fuente: { not: null },
        },
        _count: { fuente: true },
      });

      const bySource = bySourceData.reduce((acc, item) => {
        if (item.fuente) {
          acc[item.fuente] = item._count.fuente;
        }
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        today: todayCount,
        thisWeek: weekCount,
        thisMonth: monthCount,
        bySource,
        recentActivity,
      };

    } catch (error) {
      console.error('Error getting webhook stats:', error);
      throw new Error(`Error al obtener estadísticas de webhooks: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Obtener actividad reciente de webhooks
  private static async getRecentWebhookActivity(days: number): Promise<Array<{
    date: string;
    count: number;
  }>> {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      // Obtener noticias de webhook por día
      const webhookNoticias = await prisma.noticia.findMany({
        where: {
          origen: 'WEBHOOK',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      });

      // Agrupar por día
      const activityMap = new Map<string, number>();
      
      // Inicializar todos los días con 0
      for (let i = 0; i < days; i++) {
        const date = new Date(endDate.getTime() - i * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        activityMap.set(dateStr, 0);
      }

      // Contar noticias por día
      webhookNoticias.forEach(noticia => {
        const dateStr = noticia.createdAt.toISOString().split('T')[0];
        const current = activityMap.get(dateStr) || 0;
        activityMap.set(dateStr, current + 1);
      });

      // Convertir a array y ordenar por fecha
      return Array.from(activityMap.entries())
        .map(([date, count]) => ({ date, count }))
        .sort((a, b) => a.date.localeCompare(b.date));

    } catch (error) {
      console.error('Error getting recent webhook activity:', error);
      return [];
    }
  }

  // Validar token de webhook
  static validateWebhookToken(providedToken: string, expectedToken: string): boolean {
    if (!providedToken || !expectedToken) {
      return false;
    }

    // Comparación segura para evitar timing attacks
    if (providedToken.length !== expectedToken.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < providedToken.length; i++) {
      result |= providedToken.charCodeAt(i) ^ expectedToken.charCodeAt(i);
    }

    return result === 0;
  }

  // Obtener información del webhook para documentación
  static getWebhookInfo(): {
    endpoint: string;
    method: string;
    description: string;
    authentication: string;
    requiredFields: string[];
    optionalFields: string[];
    example: WebhookNoticiaData;
  } {
    return {
      endpoint: '/api/webhooks/noticia',
      method: 'POST',
      description: 'Endpoint para recibir noticias desde sistemas externos',
      authentication: 'Bearer token en header Authorization o webhookToken en body',
      requiredFields: ['titulo', 'contenido'],
      optionalFields: [
        'volanta',
        'subtitulo',
        'resumen',
        'autor',
        'periodista',
        'fuente',
        'urlFuente',
        'imagenUrl',
        'imagenAlt',
        'categoria',
        'destacada',
      ],
      example: {
        titulo: 'Título de la noticia',
        volanta: 'Volanta opcional',
        contenido: 'Contenido completo de la noticia...',
        resumen: 'Resumen opcional de la noticia',
        periodista: 'Nombre del periodista',
        fuente: 'Nombre de la fuente',
        categoria: 'Política',
        destacada: false,
      },
    };
  }

  // Limpiar datos antiguos de webhooks (para mantenimiento)
  static async cleanupOldWebhookData(daysOld: number = 90): Promise<{
    deleted: number;
    success: boolean;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      // Solo eliminar noticias de webhook que estén en estado BORRADOR y sean muy antiguas
      const result = await prisma.noticia.deleteMany({
        where: {
          origen: 'WEBHOOK',
          estado: 'BORRADOR',
          createdAt: {
            lt: cutoffDate,
          },
          publicada: false,
        },
      });

      console.log(`🧹 ${result.count} noticias antiguas de webhook eliminadas (más de ${daysOld} días)`);

      return {
        deleted: result.count,
        success: true,
      };

    } catch (error) {
      console.error('Error cleaning up old webhook data:', error);
      throw new Error(`Error al limpiar datos antiguos de webhook: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }
}

export default WebhookService;
