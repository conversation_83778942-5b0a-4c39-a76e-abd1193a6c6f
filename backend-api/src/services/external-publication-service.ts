import axios, { AxiosResponse } from 'axios';
import FormData from 'form-data';
import { prisma } from '@/config/database';

export interface PublicationRequest {
  noticiaId: number;
  diarioExternoId: number;
  userId: number;
}

export interface PublicationResponse {
  success: boolean;
  publicacionId: number;
  estado: string;
  message: string;
}

export interface DiarioExternoConfig {
  id: number;
  nombre: string;
  urlBase: string;
  bearerToken: string;
  categoriaImagenId: number;
  endpointImagen: string;
  endpointCategoria: string;
  endpointArticulo: string;
  configuracion?: any;
}

export interface ExternalArticleData {
  title: string;
  content: string;
  summary?: string;
  author?: string;
  source?: string;
  category_id?: number;
  image_id?: number;
  featured?: boolean;
  published?: boolean;
}

export class ExternalPublicationService {
  // Obtener configuración de diario externo
  static async getDiarioConfig(diarioExternoId: number): Promise<DiarioExternoConfig> {
    try {
      const diario = await prisma.diarioExterno.findUnique({
        where: { id: diarioExternoId, activo: true },
      });

      if (!diario) {
        throw new Error('Diario externo no encontrado o inactivo');
      }

      return {
        id: diario.id,
        nombre: diario.nombre,
        urlBase: diario.urlBase,
        bearerToken: diario.bearerToken,
        categoriaImagenId: diario.categoriaImagenId,
        endpointImagen: diario.endpointImagen,
        endpointCategoria: diario.endpointCategoria,
        endpointArticulo: diario.endpointArticulo,
        configuracion: diario.configuracion ? JSON.parse(diario.configuracion) : {},
      };
    } catch (error) {
      console.error('Error getting diario config:', error);
      throw new Error(`Error al obtener configuración del diario: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Subir imagen al diario externo
  private static async uploadImage(
    imageUrl: string,
    diarioConfig: DiarioExternoConfig,
    titulo: string
  ): Promise<number> {
    try {
      console.log(`📤 Subiendo imagen al diario ${diarioConfig.nombre}: ${imageUrl}`);

      // Descargar imagen
      const imageResponse = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000,
      });

      // Crear FormData
      const formData = new FormData();
      formData.append('image', Buffer.from(imageResponse.data), {
        filename: `noticia-${Date.now()}.jpg`,
        contentType: 'image/jpeg',
      });
      formData.append('category_id', diarioConfig.categoriaImagenId.toString());
      formData.append('alt_text', titulo);
      formData.append('caption', titulo);

      // Subir imagen
      const uploadResponse = await axios.post(
        `${diarioConfig.urlBase}${diarioConfig.endpointImagen}`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${diarioConfig.bearerToken}`,
          },
          timeout: 60000,
        }
      );

      if (uploadResponse.data?.success && uploadResponse.data?.data?.id) {
        const imageId = uploadResponse.data.data.id;
        console.log(`✅ Imagen subida exitosamente con ID: ${imageId}`);
        return imageId;
      } else {
        throw new Error(`Respuesta inesperada del servidor: ${JSON.stringify(uploadResponse.data)}`);
      }

    } catch (error) {
      console.error('Error uploading image:', error);
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const data = error.response?.data;
        throw new Error(`Error HTTP ${status}: ${JSON.stringify(data) || error.message}`);
      }
      throw new Error(`Error al subir imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Mapear categoría local a externa
  private static async mapCategory(
    categoriaLocalId: number | null,
    diarioExternoId: number
  ): Promise<number | undefined> {
    if (!categoriaLocalId) return undefined;

    try {
      const mapeo = await prisma.categoriaMapeo.findUnique({
        where: {
          diarioExternoId_categoriaLocalId: {
            diarioExternoId,
            categoriaLocalId,
          },
        },
      });

      return mapeo?.categoriaExternaId;
    } catch (error) {
      console.error('Error mapping category:', error);
      return undefined;
    }
  }

  // Publicar artículo en diario externo
  private static async publishArticle(
    articleData: ExternalArticleData,
    diarioConfig: DiarioExternoConfig
  ): Promise<{ id: number; url?: string }> {
    try {
      console.log(`📝 Publicando artículo en ${diarioConfig.nombre}: "${articleData.title}"`);

      const response = await axios.post(
        `${diarioConfig.urlBase}${diarioConfig.endpointArticulo}`,
        articleData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${diarioConfig.bearerToken}`,
          },
          timeout: 60000,
        }
      );

      if (response.data?.success && response.data?.data?.id) {
        const articleId = response.data.data.id;
        const articleUrl = response.data.data.url || response.data.data.permalink;
        
        console.log(`✅ Artículo publicado exitosamente con ID: ${articleId}`);
        return { id: articleId, url: articleUrl };
      } else {
        throw new Error(`Respuesta inesperada del servidor: ${JSON.stringify(response.data)}`);
      }

    } catch (error) {
      console.error('Error publishing article:', error);
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const data = error.response?.data;
        throw new Error(`Error HTTP ${status}: ${JSON.stringify(data) || error.message}`);
      }
      throw new Error(`Error al publicar artículo: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Función principal para publicar noticia
  static async publishNoticia(request: PublicationRequest): Promise<PublicationResponse> {
    let publicacion: any = null;

    try {
      console.log(`🚀 Iniciando publicación externa - Noticia ID: ${request.noticiaId}, Diario ID: ${request.diarioExternoId}`);

      // Obtener noticia
      const noticia = await prisma.noticia.findUnique({
        where: { id: request.noticiaId },
        include: {
          categoria: true,
          user: {
            select: { name: true, email: true },
          },
        },
      });

      if (!noticia) {
        throw new Error('Noticia no encontrada');
      }

      // Obtener configuración del diario
      const diarioConfig = await this.getDiarioConfig(request.diarioExternoId);

      // Crear registro de publicación
      publicacion = await prisma.publicacionExterna.create({
        data: {
          noticiaId: request.noticiaId,
          diarioExternoId: request.diarioExternoId,
          estado: 'PENDIENTE',
        },
      });

      // Actualizar estado: subiendo imagen
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: { estado: 'SUBIENDO_IMAGEN' },
      });

      // Subir imagen si existe
      let imagenExternaId: number | undefined;
      if (noticia.imagenUrl) {
        try {
          imagenExternaId = await this.uploadImage(
            noticia.imagenUrl,
            diarioConfig,
            noticia.titulo
          );

          await prisma.publicacionExterna.update({
            where: { id: publicacion.id },
            data: {
              estado: 'IMAGEN_SUBIDA',
              imagenExternaId,
            },
          });
        } catch (imageError) {
          console.error('Error subiendo imagen:', imageError);
          await prisma.publicacionExterna.update({
            where: { id: publicacion.id },
            data: {
              estado: 'ERROR_IMAGEN',
              errorMensaje: `Error al subir imagen: ${imageError instanceof Error ? imageError.message : 'Error desconocido'}`,
            },
          });
          // Continuar sin imagen
        }
      }

      // Actualizar estado: publicando artículo
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: { estado: 'PUBLICANDO_ARTICULO' },
      });

      // Mapear categoría
      const categoriaExterna = await this.mapCategory(
        noticia.categoriaId,
        request.diarioExternoId
      );

      // Preparar datos del artículo
      const articleData: ExternalArticleData = {
        title: noticia.titulo,
        content: noticia.contenido,
        summary: noticia.resumen || undefined,
        author: noticia.autor || noticia.periodista || undefined,
        source: noticia.fuente || undefined,
        category_id: categoriaExterna,
        image_id: imagenExternaId,
        featured: noticia.destacada,
        published: true,
      };

      // Publicar artículo
      const articleResult = await this.publishArticle(articleData, diarioConfig);

      // Actualizar registro con éxito
      await prisma.publicacionExterna.update({
        where: { id: publicacion.id },
        data: {
          estado: 'EXITOSO',
          articuloExternoId: articleResult.id,
          urlPublicacion: articleResult.url,
          metadatos: JSON.stringify({
            imagenExternaId,
            categoriaExterna,
            fechaPublicacion: new Date().toISOString(),
            diario: diarioConfig.nombre,
          }),
        },
      });

      console.log(`✅ Publicación externa completada exitosamente - ID: ${publicacion.id}`);

      return {
        success: true,
        publicacionId: publicacion.id,
        estado: 'EXITOSO',
        message: `Noticia publicada exitosamente en ${diarioConfig.nombre}`,
      };

    } catch (error) {
      console.error('Error en publicación externa:', error);

      // Actualizar registro con error si existe
      if (publicacion) {
        await prisma.publicacionExterna.update({
          where: { id: publicacion.id },
          data: {
            estado: 'ERROR_ARTICULO',
            errorMensaje: error instanceof Error ? error.message : 'Error desconocido',
          },
        });
      }

      return {
        success: false,
        publicacionId: publicacion?.id || 0,
        estado: 'ERROR',
        message: `Error en publicación: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      };
    }
  }

  // Obtener estado de publicación
  static async getPublicationStatus(publicacionId: number): Promise<{
    id: number;
    estado: string;
    urlPublicacion?: string;
    errorMensaje?: string;
    metadatos?: any;
    createdAt: string;
    updatedAt: string;
  }> {
    try {
      const publicacion = await prisma.publicacionExterna.findUnique({
        where: { id: publicacionId },
        include: {
          diarioExterno: {
            select: {
              nombre: true,
            },
          },
          noticia: {
            select: {
              titulo: true,
            },
          },
        },
      });

      if (!publicacion) {
        throw new Error('Publicación no encontrada');
      }

      return {
        id: publicacion.id,
        estado: publicacion.estado,
        urlPublicacion: publicacion.urlPublicacion || undefined,
        errorMensaje: publicacion.errorMensaje || undefined,
        metadatos: publicacion.metadatos ? JSON.parse(publicacion.metadatos) : undefined,
        createdAt: publicacion.createdAt.toISOString(),
        updatedAt: publicacion.updatedAt.toISOString(),
      };

    } catch (error) {
      console.error('Error getting publication status:', error);
      throw new Error(`Error al obtener estado de publicación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Listar publicaciones de una noticia
  static async getNoticiaPublications(noticiaId: number): Promise<Array<{
    id: number;
    estado: string;
    diarioNombre: string;
    urlPublicacion?: string;
    createdAt: string;
  }>> {
    try {
      const publicaciones = await prisma.publicacionExterna.findMany({
        where: { noticiaId },
        include: {
          diarioExterno: {
            select: {
              nombre: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      return publicaciones.map(pub => ({
        id: pub.id,
        estado: pub.estado,
        diarioNombre: pub.diarioExterno.nombre,
        urlPublicacion: pub.urlPublicacion || undefined,
        createdAt: pub.createdAt.toISOString(),
      }));

    } catch (error) {
      console.error('Error getting noticia publications:', error);
      throw new Error(`Error al obtener publicaciones de la noticia: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Reintentar publicación fallida
  static async retryPublication(publicacionId: number): Promise<PublicationResponse> {
    try {
      const publicacion = await prisma.publicacionExterna.findUnique({
        where: { id: publicacionId },
      });

      if (!publicacion) {
        throw new Error('Publicación no encontrada');
      }

      if (publicacion.estado === 'EXITOSO') {
        return {
          success: true,
          publicacionId,
          estado: 'EXITOSO',
          message: 'La publicación ya fue exitosa',
        };
      }

      // Reiniciar publicación
      return await this.publishNoticia({
        noticiaId: publicacion.noticiaId,
        diarioExternoId: publicacion.diarioExternoId,
        userId: 1, // Usuario del sistema para reintentos
      });

    } catch (error) {
      console.error('Error retrying publication:', error);
      throw new Error(`Error al reintentar publicación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Verificar conexión con diario externo
  static async testConnection(diarioExternoId: number): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const diarioConfig = await this.getDiarioConfig(diarioExternoId);

      // Probar endpoint de categorías (más ligero que subir imagen)
      const response = await axios.get(
        `${diarioConfig.urlBase}${diarioConfig.endpointCategoria}`,
        {
          headers: {
            'Authorization': `Bearer ${diarioConfig.bearerToken}`,
          },
          timeout: 10000,
        }
      );

      if (response.status === 200) {
        return {
          success: true,
          message: `Conexión exitosa con ${diarioConfig.nombre}`,
          details: {
            status: response.status,
            categorias: Array.isArray(response.data?.data) ? response.data.data.length : 'N/A',
          },
        };
      } else {
        return {
          success: false,
          message: `Respuesta inesperada: ${response.status}`,
          details: response.data,
        };
      }

    } catch (error) {
      console.error('Error testing connection:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          message: `Error de conexión: ${error.response?.status || error.code}`,
          details: error.response?.data || error.message,
        };
      }

      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      };
    }
  }
}

export default ExternalPublicationService;
