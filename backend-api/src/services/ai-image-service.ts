import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from '@/config/database';
import { config } from '@/config/environment';

export interface ImageGenerationRequest {
  titulo: string;
  resumen?: string;
  contenido?: string;
  estilo?: string;
  configuracionId?: number;
  noticiaId?: number;
  userId: number;
}

export interface ImageGenerationResponse {
  id: number;
  estado: string;
  imagenUrl?: string;
  prompt: string;
  metadatos?: any;
}

export interface ImageConfig {
  id: number;
  nombre: string;
  proveedor: string;
  modelo: string;
  apiKey?: string;
  endpoint?: string;
  parametros?: any;
  promptPorDefecto?: string;
}

export class AIImageService {
  private static openaiClient: OpenAI | null = null;
  private static geminiClient: GoogleGenerativeAI | null = null;

  // Obtener configuración de IA para imágenes
  static async getImageConfig(configuracionId?: number): Promise<ImageConfig> {
    try {
      let configuracion;

      if (configuracionId) {
        configuracion = await prisma.configuracionIA.findUnique({
          where: { id: configuracionId, activo: true },
        });
      } else {
        // Buscar configuración por defecto
        configuracion = await prisma.configuracionIA.findFirst({
          where: { activo: true },
          orderBy: { createdAt: 'desc' },
        });
      }

      if (!configuracion) {
        // Crear configuración por defecto si no existe
        configuracion = await prisma.configuracionIA.create({
          data: {
            nombre: 'OpenAI DALL-E 3',
            proveedor: 'OPENAI',
            modelo: 'dall-e-3',
            apiKey: config.ai.openai.apiKey,
            parametros: JSON.stringify({
              size: '1024x1024',
              quality: 'standard',
              style: 'vivid',
            }),
            promptPorDefecto: 'Crear una imagen periodística profesional para la noticia: ',
            activo: true,
          },
        });
      }

      return {
        id: configuracion.id,
        nombre: configuracion.nombre,
        proveedor: configuracion.proveedor,
        modelo: configuracion.modelo,
        apiKey: configuracion.apiKey || config.ai.openai.apiKey,
        endpoint: configuracion.endpoint,
        parametros: configuracion.parametros ? JSON.parse(configuracion.parametros) : {},
        promptPorDefecto: configuracion.promptPorDefecto,
      };
    } catch (error) {
      console.error('Error getting image config:', error);
      throw new Error('Error al obtener configuración de IA para imágenes');
    }
  }

  // Inicializar cliente OpenAI
  private static getOpenAIClient(apiKey: string): OpenAI {
    if (!this.openaiClient || this.openaiClient.apiKey !== apiKey) {
      this.openaiClient = new OpenAI({ apiKey });
    }
    return this.openaiClient;
  }

  // Generar prompt optimizado para imagen
  private static generateImagePrompt(request: ImageGenerationRequest, config: ImageConfig): string {
    const basePrompt = config.promptPorDefecto || 'Crear una imagen para la noticia: ';
    
    let prompt = `${basePrompt}"${request.titulo}"`;
    
    if (request.resumen) {
      prompt += `. Resumen: ${request.resumen.substring(0, 200)}`;
    } else if (request.contenido) {
      prompt += `. Contenido: ${request.contenido.substring(0, 200)}`;
    }
    
    if (request.estilo) {
      prompt += `. Estilo: ${request.estilo}`;
    } else {
      prompt += '. Estilo: periodístico profesional, realista, alta calidad';
    }

    // Agregar restricciones de contenido
    prompt += '. Sin texto visible en la imagen, sin logos, sin marcas de agua.';

    return prompt;
  }

  // Generar imagen con OpenAI DALL-E
  private static async generateWithOpenAI(
    request: ImageGenerationRequest,
    config: ImageConfig,
    generacionId: number
  ): Promise<void> {
    if (!config.apiKey) {
      throw new Error('OpenAI API key no configurada');
    }

    const startTime = Date.now();
    const client = this.getOpenAIClient(config.apiKey);
    const prompt = this.generateImagePrompt(request, config);

    try {
      console.log(`🎨 Generando imagen con OpenAI DALL-E: ${prompt.substring(0, 100)}...`);

      const response = await client.images.generate({
        model: config.modelo as 'dall-e-2' | 'dall-e-3',
        prompt: prompt,
        n: 1,
        size: config.parametros?.size || '1024x1024',
        quality: config.parametros?.quality || 'standard',
        style: config.parametros?.style || 'vivid',
      });

      const imageUrl = response.data[0]?.url;
      if (!imageUrl) {
        throw new Error('No se recibió URL de imagen de OpenAI');
      }

      const endTime = Date.now();
      const tiempoGeneracion = endTime - startTime;

      // Actualizar registro en base de datos
      await prisma.generacionImagen.update({
        where: { id: generacionId },
        data: {
          estado: 'COMPLETADA',
          imagenUrl: imageUrl,
          tiempoGeneracion,
          metadatos: JSON.stringify({
            modelo: config.modelo,
            proveedor: 'OpenAI',
            parametros: config.parametros,
            prompt_usado: prompt,
            revised_prompt: response.data[0]?.revised_prompt,
          }),
        },
      });

      console.log(`✅ Imagen generada exitosamente en ${tiempoGeneracion}ms`);

    } catch (error) {
      console.error('Error generating image with OpenAI:', error);
      
      await prisma.generacionImagen.update({
        where: { id: generacionId },
        data: {
          estado: 'ERROR',
          error: error instanceof Error ? error.message : 'Error desconocido',
        },
      });

      throw error;
    }
  }

  // Generar imagen con Google Gemini (simulado - Gemini no genera imágenes directamente)
  private static async generateWithGemini(
    request: ImageGenerationRequest,
    config: ImageConfig,
    generacionId: number
  ): Promise<void> {
    // Nota: Google Gemini no tiene capacidades de generación de imágenes como DALL-E
    // Esta implementación es un placeholder para futuras integraciones
    
    const startTime = Date.now();
    const prompt = this.generateImagePrompt(request, config);

    try {
      console.log(`🎨 Simulando generación con Gemini: ${prompt.substring(0, 100)}...`);

      // Simular tiempo de procesamiento
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Por ahora, usar una imagen placeholder
      const placeholderUrl = `https://via.placeholder.com/1024x1024/3B82F6/FFFFFF?text=${encodeURIComponent(request.titulo.substring(0, 20))}`;

      const endTime = Date.now();
      const tiempoGeneracion = endTime - startTime;

      await prisma.generacionImagen.update({
        where: { id: generacionId },
        data: {
          estado: 'COMPLETADA',
          imagenUrl: placeholderUrl,
          tiempoGeneracion,
          metadatos: JSON.stringify({
            modelo: config.modelo,
            proveedor: 'Gemini',
            parametros: config.parametros,
            prompt_usado: prompt,
            nota: 'Imagen placeholder - Gemini no soporta generación de imágenes aún',
          }),
        },
      });

      console.log(`✅ Imagen placeholder generada en ${tiempoGeneracion}ms`);

    } catch (error) {
      console.error('Error generating image with Gemini:', error);
      
      await prisma.generacionImagen.update({
        where: { id: generacionId },
        data: {
          estado: 'ERROR',
          error: error instanceof Error ? error.message : 'Error desconocido',
        },
      });

      throw error;
    }
  }

  // Función principal para generar imagen
  static async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    try {
      console.log(`🎨 Iniciando generación de imagen para: "${request.titulo}"`);

      // Obtener configuración
      const config = await this.getImageConfig(request.configuracionId);
      const prompt = this.generateImagePrompt(request, config);

      // Crear registro inicial en base de datos
      const generacion = await prisma.generacionImagen.create({
        data: {
          noticiaId: request.noticiaId,
          usuarioId: request.userId,
          configuracionIAId: config.id,
          prompt,
          promptOriginal: `${request.titulo} - ${request.resumen || request.contenido?.substring(0, 100) || ''}`,
          estado: 'PROCESANDO',
        },
      });

      // Generar imagen de forma asíncrona
      setImmediate(async () => {
        try {
          if (config.proveedor === 'OPENAI') {
            await this.generateWithOpenAI(request, config, generacion.id);
          } else if (config.proveedor === 'GEMINI') {
            await this.generateWithGemini(request, config, generacion.id);
          } else {
            throw new Error(`Proveedor no soportado: ${config.proveedor}`);
          }
        } catch (error) {
          console.error(`❌ Error en generación asíncrona:`, error);
        }
      });

      return {
        id: generacion.id,
        estado: 'PROCESANDO',
        prompt,
      };

    } catch (error) {
      console.error('Error en generateImage:', error);
      throw new Error(`Error al iniciar generación de imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Obtener estado de generación
  static async getGenerationStatus(generacionId: number, userId: number): Promise<ImageGenerationResponse> {
    try {
      const generacion = await prisma.generacionImagen.findUnique({
        where: { id: generacionId },
        include: {
          configuracion: {
            select: {
              nombre: true,
              proveedor: true,
              modelo: true,
            },
          },
          usuario: {
            select: {
              name: true,
              email: true,
            },
          },
          noticia: {
            select: {
              titulo: true,
            },
          },
        },
      });

      if (!generacion) {
        throw new Error('Generación no encontrada');
      }

      // Verificar permisos (usuarios solo pueden ver sus propias generaciones)
      if (generacion.usuarioId !== userId) {
        throw new Error('No tienes permisos para ver esta generación');
      }

      return {
        id: generacion.id,
        estado: generacion.estado,
        imagenUrl: generacion.imagenUrl || undefined,
        prompt: generacion.prompt,
        metadatos: generacion.metadatos ? JSON.parse(generacion.metadatos) : undefined,
      };

    } catch (error) {
      console.error('Error getting generation status:', error);
      throw new Error(`Error al obtener estado de generación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Listar generaciones de un usuario
  static async getUserGenerations(
    userId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    generaciones: ImageGenerationResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      const [generaciones, total] = await Promise.all([
        prisma.generacionImagen.findMany({
          where: { usuarioId: userId },
          include: {
            configuracion: {
              select: {
                nombre: true,
                proveedor: true,
                modelo: true,
              },
            },
            noticia: {
              select: {
                titulo: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.generacionImagen.count({
          where: { usuarioId: userId },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        generaciones: generaciones.map(gen => ({
          id: gen.id,
          estado: gen.estado,
          imagenUrl: gen.imagenUrl || undefined,
          prompt: gen.prompt,
          metadatos: gen.metadatos ? JSON.parse(gen.metadatos) : undefined,
        })),
        total,
        page,
        totalPages,
      };

    } catch (error) {
      console.error('Error getting user generations:', error);
      throw new Error(`Error al obtener generaciones del usuario: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Verificar límites de uso
  static async checkUsageLimits(userId: number, configuracionId: number): Promise<{
    canGenerate: boolean;
    remaining: number;
    limit: number;
    resetDate: Date;
  }> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

      // Buscar límite existente
      let limite = await prisma.limiteUsoIA.findUnique({
        where: {
          usuarioId_configuracionIAId_periodo: {
            usuarioId,
            configuracionIAId: configuracionId,
            periodo: 'MENSUAL',
          },
        },
      });

      // Si no existe, crear límite por defecto
      if (!limite) {
        limite = await prisma.limiteUsoIA.create({
          data: {
            usuarioId,
            configuracionIAId: configuracionId,
            periodo: 'MENSUAL',
            limite: 50, // Límite por defecto
            usado: 0,
            fechaReset: nextMonth,
          },
        });
      }

      // Resetear si es un nuevo mes
      if (limite.fechaReset <= now) {
        limite = await prisma.limiteUsoIA.update({
          where: { id: limite.id },
          data: {
            usado: 0,
            fechaReset: nextMonth,
          },
        });
      }

      const remaining = Math.max(0, limite.limite - limite.usado);
      const canGenerate = remaining > 0;

      return {
        canGenerate,
        remaining,
        limit: limite.limite,
        resetDate: limite.fechaReset,
      };

    } catch (error) {
      console.error('Error checking usage limits:', error);
      throw new Error(`Error al verificar límites de uso: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Incrementar uso
  static async incrementUsage(userId: number, configuracionId: number): Promise<void> {
    try {
      await prisma.limiteUsoIA.update({
        where: {
          usuarioId_configuracionIAId_periodo: {
            usuarioId,
            configuracionIAId: configuracionId,
            periodo: 'MENSUAL',
          },
        },
        data: {
          usado: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      console.error('Error incrementing usage:', error);
      // No lanzar error aquí para no bloquear la generación
    }
  }
}

export default AIImageService;
