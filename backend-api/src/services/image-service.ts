import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { config } from '@/config/environment';

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp' | 'auto';
  progressive?: boolean;
  removeMetadata?: boolean;
}

export interface OptimizedImageResult {
  filename: string;
  originalName: string;
  size: number;
  width: number;
  height: number;
  format: string;
  url: string;
  path: string;
  optimizationStats: {
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    processingTime: number;
  };
}

export interface ImageUploadResult {
  success: boolean;
  data?: OptimizedImageResult;
  error?: string;
}

export class ImageService {
  private static readonly DEFAULT_OPTIONS: ImageOptimizationOptions = {
    maxWidth: 1920,
    maxHeight: 1080,
    quality: 85,
    format: 'auto',
    progressive: true,
    removeMetadata: true,
  };

  // Crear directorio si no existe
  private static async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  // Detectar formato óptimo basado en el contenido
  private static async detectOptimalFormat(
    buffer: Buffer,
    originalFormat: string
  ): Promise<'jpeg' | 'png' | 'webp'> {
    try {
      const metadata = await sharp(buffer).metadata();
      
      // Si tiene transparencia, usar PNG o WebP
      if (metadata.hasAlpha) {
        return 'webp'; // WebP maneja transparencia mejor que PNG
      }
      
      // Para fotos complejas, usar JPEG o WebP
      if (originalFormat === 'jpeg' || originalFormat === 'jpg') {
        return 'webp'; // WebP generalmente es mejor que JPEG
      }
      
      // Para gráficos simples, usar PNG o WebP
      if (originalFormat === 'png') {
        return 'webp';
      }
      
      // Por defecto, usar WebP para mejor compresión
      return 'webp';
      
    } catch (error) {
      console.error('Error detecting optimal format:', error);
      return 'jpeg'; // Fallback seguro
    }
  }

  // Optimizar imagen
  static async optimizeImage(
    inputBuffer: Buffer,
    originalName: string,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImageResult> {
    const startTime = Date.now();
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      console.log(`🖼️ Optimizando imagen: ${originalName}`);
      
      // Obtener metadata original
      const originalMetadata = await sharp(inputBuffer).metadata();
      const originalSize = inputBuffer.length;
      const originalFormat = originalMetadata.format || 'jpeg';
      
      console.log(`📊 Imagen original: ${originalMetadata.width}x${originalMetadata.height}, ${(originalSize / 1024).toFixed(1)}KB, formato: ${originalFormat}`);
      
      // Determinar formato de salida
      let outputFormat: 'jpeg' | 'png' | 'webp';
      if (opts.format === 'auto') {
        outputFormat = await this.detectOptimalFormat(inputBuffer, originalFormat);
      } else {
        outputFormat = opts.format!;
      }
      
      // Crear pipeline de Sharp
      let pipeline = sharp(inputBuffer);
      
      // Remover metadata si se solicita
      if (opts.removeMetadata) {
        pipeline = pipeline.withMetadata({
          exif: {},
          icc: 'srgb', // Mantener perfil de color básico
        });
      }
      
      // Redimensionar si es necesario
      const needsResize = (
        (opts.maxWidth && originalMetadata.width && originalMetadata.width > opts.maxWidth) ||
        (opts.maxHeight && originalMetadata.height && originalMetadata.height > opts.maxHeight)
      );
      
      if (needsResize) {
        pipeline = pipeline.resize(opts.maxWidth, opts.maxHeight, {
          fit: 'inside',
          withoutEnlargement: true,
        });
      }
      
      // Aplicar formato y compresión
      switch (outputFormat) {
        case 'jpeg':
          pipeline = pipeline.jpeg({
            quality: opts.quality,
            progressive: opts.progressive,
            mozjpeg: true, // Usar mozjpeg para mejor compresión
          });
          break;
          
        case 'png':
          pipeline = pipeline.png({
            quality: opts.quality,
            progressive: opts.progressive,
            compressionLevel: 9,
            adaptiveFiltering: true,
          });
          break;
          
        case 'webp':
          pipeline = pipeline.webp({
            quality: opts.quality,
            effort: 6, // Balance entre compresión y velocidad
          });
          break;
      }
      
      // Procesar imagen
      const optimizedBuffer = await pipeline.toBuffer();
      const optimizedMetadata = await sharp(optimizedBuffer).metadata();
      
      // Generar nombre de archivo único
      const fileExtension = `.${outputFormat}`;
      const filename = `${uuidv4()}${fileExtension}`;
      
      // Crear estructura de directorios por fecha
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      
      const uploadDir = path.join(config.upload.uploadDir, 'images', String(year), month, day);
      await this.ensureDirectoryExists(uploadDir);
      
      const filePath = path.join(uploadDir, filename);
      
      // Guardar archivo optimizado
      await fs.writeFile(filePath, optimizedBuffer);
      
      // Generar URL pública
      const publicUrl = `/uploads/images/${year}/${month}/${day}/${filename}`;
      
      const processingTime = Date.now() - startTime;
      const compressionRatio = ((originalSize - optimizedBuffer.length) / originalSize) * 100;
      
      console.log(`✅ Imagen optimizada: ${optimizedMetadata.width}x${optimizedMetadata.height}, ${(optimizedBuffer.length / 1024).toFixed(1)}KB, ${compressionRatio.toFixed(1)}% compresión, ${processingTime}ms`);
      
      return {
        filename,
        originalName,
        size: optimizedBuffer.length,
        width: optimizedMetadata.width || 0,
        height: optimizedMetadata.height || 0,
        format: outputFormat,
        url: publicUrl,
        path: filePath,
        optimizationStats: {
          originalSize,
          optimizedSize: optimizedBuffer.length,
          compressionRatio,
          processingTime,
        },
      };
      
    } catch (error) {
      console.error('Error optimizing image:', error);
      throw new Error(`Error al optimizar imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Crear múltiples tamaños de una imagen (thumbnails)
  static async createImageVariants(
    inputBuffer: Buffer,
    originalName: string,
    variants: Array<{
      name: string;
      width: number;
      height?: number;
      quality?: number;
    }>
  ): Promise<Array<OptimizedImageResult>> {
    try {
      console.log(`🖼️ Creando ${variants.length} variantes de: ${originalName}`);
      
      const results: OptimizedImageResult[] = [];
      
      for (const variant of variants) {
        const options: ImageOptimizationOptions = {
          maxWidth: variant.width,
          maxHeight: variant.height,
          quality: variant.quality || 85,
          format: 'webp', // WebP para variantes
        };
        
        const result = await this.optimizeImage(inputBuffer, `${variant.name}-${originalName}`, options);
        results.push(result);
      }
      
      console.log(`✅ ${results.length} variantes creadas exitosamente`);
      return results;
      
    } catch (error) {
      console.error('Error creating image variants:', error);
      throw new Error(`Error al crear variantes de imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Procesar imagen desde URL
  static async processImageFromUrl(
    imageUrl: string,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImageResult> {
    try {
      console.log(`🌐 Descargando imagen desde URL: ${imageUrl}`);
      
      // Descargar imagen
      const response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Panel-Unificado-Backend/1.0',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Error HTTP ${response.status}: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      
      // Extraer nombre de archivo de la URL
      const urlPath = new URL(imageUrl).pathname;
      const originalName = path.basename(urlPath) || 'downloaded-image.jpg';
      
      // Optimizar imagen descargada
      return await this.optimizeImage(buffer, originalName, options);
      
    } catch (error) {
      console.error('Error processing image from URL:', error);
      throw new Error(`Error al procesar imagen desde URL: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Validar archivo de imagen
  static async validateImageFile(buffer: Buffer, filename: string): Promise<{
    valid: boolean;
    error?: string;
    metadata?: sharp.Metadata;
  }> {
    try {
      // Verificar que sea una imagen válida
      const metadata = await sharp(buffer).metadata();
      
      if (!metadata.format) {
        return {
          valid: false,
          error: 'Formato de imagen no reconocido',
        };
      }
      
      // Verificar formatos permitidos
      const allowedFormats = ['jpeg', 'jpg', 'png', 'webp', 'gif', 'tiff'];
      if (!allowedFormats.includes(metadata.format)) {
        return {
          valid: false,
          error: `Formato no permitido: ${metadata.format}. Formatos permitidos: ${allowedFormats.join(', ')}`,
        };
      }
      
      // Verificar tamaño máximo
      const maxSize = config.upload.maxFileSize; // 10MB por defecto
      if (buffer.length > maxSize) {
        return {
          valid: false,
          error: `Archivo muy grande: ${(buffer.length / 1024 / 1024).toFixed(1)}MB. Máximo permitido: ${(maxSize / 1024 / 1024).toFixed(1)}MB`,
        };
      }
      
      // Verificar dimensiones mínimas
      const minWidth = 100;
      const minHeight = 100;
      if (metadata.width && metadata.width < minWidth) {
        return {
          valid: false,
          error: `Ancho muy pequeño: ${metadata.width}px. Mínimo: ${minWidth}px`,
        };
      }
      
      if (metadata.height && metadata.height < minHeight) {
        return {
          valid: false,
          error: `Alto muy pequeño: ${metadata.height}px. Mínimo: ${minHeight}px`,
        };
      }
      
      // Verificar dimensiones máximas
      const maxWidth = 8000;
      const maxHeight = 8000;
      if (metadata.width && metadata.width > maxWidth) {
        return {
          valid: false,
          error: `Ancho muy grande: ${metadata.width}px. Máximo: ${maxWidth}px`,
        };
      }
      
      if (metadata.height && metadata.height > maxHeight) {
        return {
          valid: false,
          error: `Alto muy grande: ${metadata.height}px. Máximo: ${maxHeight}px`,
        };
      }
      
      return {
        valid: true,
        metadata,
      };
      
    } catch (error) {
      return {
        valid: false,
        error: `Error al validar imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      };
    }
  }

  // Eliminar archivo de imagen
  static async deleteImage(filePath: string): Promise<{ success: boolean; error?: string }> {
    try {
      await fs.unlink(filePath);
      console.log(`🗑️ Imagen eliminada: ${filePath}`);
      return { success: true };
    } catch (error) {
      console.error('Error deleting image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      };
    }
  }

  // Obtener información de imagen
  static async getImageInfo(filePath: string): Promise<{
    exists: boolean;
    metadata?: sharp.Metadata;
    size?: number;
    error?: string;
  }> {
    try {
      const stats = await fs.stat(filePath);
      const buffer = await fs.readFile(filePath);
      const metadata = await sharp(buffer).metadata();
      
      return {
        exists: true,
        metadata,
        size: stats.size,
      };
      
    } catch (error) {
      return {
        exists: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      };
    }
  }
}

export default ImageService;
