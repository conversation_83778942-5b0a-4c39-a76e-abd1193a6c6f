import { prisma } from '@/config/database';

export interface NotificationData {
  tipo: 'WEBHOOK_NOTICIA' | 'SISTEMA';
  titulo: string;
  mensaje?: string;
  userId?: number;
  noticiaId?: number;
  metadata?: Record<string, any>;
}

export interface BulkNotificationData {
  tipo: 'WEBHOOK_NOTICIA' | 'SISTEMA';
  titulo: string;
  mensaje?: string;
  userIds: number[];
  noticiaId?: number;
  metadata?: Record<string, any>;
}

export interface NotificationFilter {
  userId?: number;
  tipo?: 'WEBHOOK_NOTICIA' | 'SISTEMA';
  leida?: boolean;
  noticiaId?: number;
  startDate?: Date;
  endDate?: Date;
}

export interface PaginatedNotifications {
  notifications: Array<{
    id: number;
    tipo: string;
    titulo: string;
    mensaje?: string;
    leida: boolean;
    createdAt: string;
    updatedAt: string;
    noticia?: {
      id: number;
      titulo: string;
    };
    metadata?: any;
  }>;
  total: number;
  page: number;
  totalPages: number;
  unreadCount: number;
}

export class NotificationService {
  // Crear notificación individual
  static async createNotification(data: NotificationData): Promise<{
    id: number;
    success: boolean;
  }> {
    try {
      console.log(`📢 Creando notificación: ${data.titulo}`);

      const notification = await prisma.notificacion.create({
        data: {
          tipo: data.tipo,
          titulo: data.titulo,
          mensaje: data.mensaje,
          userId: data.userId!,
          noticiaId: data.noticiaId,
          metadata: data.metadata ? JSON.stringify(data.metadata) : null,
          leida: false,
        },
      });

      console.log(`✅ Notificación creada con ID: ${notification.id}`);

      return {
        id: notification.id,
        success: true,
      };

    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error(`Error al crear notificación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Crear notificaciones masivas
  static async createBulkNotifications(data: BulkNotificationData): Promise<{
    created: number;
    success: boolean;
  }> {
    try {
      console.log(`📢 Creando ${data.userIds.length} notificaciones masivas: ${data.titulo}`);

      const notifications = await prisma.notificacion.createMany({
        data: data.userIds.map(userId => ({
          tipo: data.tipo,
          titulo: data.titulo,
          mensaje: data.mensaje,
          userId,
          noticiaId: data.noticiaId,
          metadata: data.metadata ? JSON.stringify(data.metadata) : null,
          leida: false,
        })),
      });

      console.log(`✅ ${notifications.count} notificaciones creadas exitosamente`);

      return {
        created: notifications.count,
        success: true,
      };

    } catch (error) {
      console.error('Error creating bulk notifications:', error);
      throw new Error(`Error al crear notificaciones masivas: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Obtener notificaciones de un usuario con paginación
  static async getUserNotifications(
    userId: number,
    page: number = 1,
    limit: number = 20,
    filters: NotificationFilter = {}
  ): Promise<PaginatedNotifications> {
    try {
      const skip = (page - 1) * limit;

      // Construir filtros
      const where: any = {
        userId,
        ...filters,
      };

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) where.createdAt.gte = filters.startDate;
        if (filters.endDate) where.createdAt.lte = filters.endDate;
      }

      // Obtener notificaciones y conteos
      const [notifications, total, unreadCount] = await Promise.all([
        prisma.notificacion.findMany({
          where,
          include: {
            noticia: {
              select: {
                id: true,
                titulo: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        prisma.notificacion.count({ where }),
        prisma.notificacion.count({
          where: {
            userId,
            leida: false,
          },
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        notifications: notifications.map(notif => ({
          id: notif.id,
          tipo: notif.tipo,
          titulo: notif.titulo,
          mensaje: notif.mensaje || undefined,
          leida: notif.leida,
          createdAt: notif.createdAt.toISOString(),
          updatedAt: notif.updatedAt.toISOString(),
          noticia: notif.noticia || undefined,
          metadata: notif.metadata ? JSON.parse(notif.metadata) : undefined,
        })),
        total,
        page,
        totalPages,
        unreadCount,
      };

    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw new Error(`Error al obtener notificaciones: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Marcar notificación como leída
  static async markAsRead(notificationId: number, userId: number): Promise<{
    success: boolean;
  }> {
    try {
      const notification = await prisma.notificacion.findUnique({
        where: { id: notificationId },
      });

      if (!notification) {
        throw new Error('Notificación no encontrada');
      }

      if (notification.userId !== userId) {
        throw new Error('No tienes permisos para marcar esta notificación');
      }

      await prisma.notificacion.update({
        where: { id: notificationId },
        data: { leida: true },
      });

      console.log(`✅ Notificación ${notificationId} marcada como leída`);

      return { success: true };

    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw new Error(`Error al marcar notificación como leída: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Marcar todas las notificaciones como leídas
  static async markAllAsRead(userId: number): Promise<{
    updated: number;
    success: boolean;
  }> {
    try {
      const result = await prisma.notificacion.updateMany({
        where: {
          userId,
          leida: false,
        },
        data: {
          leida: true,
        },
      });

      console.log(`✅ ${result.count} notificaciones marcadas como leídas para usuario ${userId}`);

      return {
        updated: result.count,
        success: true,
      };

    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw new Error(`Error al marcar todas las notificaciones como leídas: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Eliminar notificación
  static async deleteNotification(notificationId: number, userId: number): Promise<{
    success: boolean;
  }> {
    try {
      const notification = await prisma.notificacion.findUnique({
        where: { id: notificationId },
      });

      if (!notification) {
        throw new Error('Notificación no encontrada');
      }

      if (notification.userId !== userId) {
        throw new Error('No tienes permisos para eliminar esta notificación');
      }

      await prisma.notificacion.delete({
        where: { id: notificationId },
      });

      console.log(`✅ Notificación ${notificationId} eliminada`);

      return { success: true };

    } catch (error) {
      console.error('Error deleting notification:', error);
      throw new Error(`Error al eliminar notificación: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Obtener conteo de notificaciones no leídas
  static async getUnreadCount(userId: number): Promise<{
    count: number;
  }> {
    try {
      const count = await prisma.notificacion.count({
        where: {
          userId,
          leida: false,
        },
      });

      return { count };

    } catch (error) {
      console.error('Error getting unread count:', error);
      throw new Error(`Error al obtener conteo de notificaciones: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Limpiar notificaciones antiguas
  static async cleanupOldNotifications(daysOld: number = 30): Promise<{
    deleted: number;
    success: boolean;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await prisma.notificacion.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
          leida: true, // Solo eliminar las ya leídas
        },
      });

      console.log(`🧹 ${result.count} notificaciones antiguas eliminadas (más de ${daysOld} días)`);

      return {
        deleted: result.count,
        success: true,
      };

    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
      throw new Error(`Error al limpiar notificaciones antiguas: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Notificar sobre nueva noticia desde webhook
  static async notifyWebhookNoticia(
    noticiaId: number,
    titulo: string,
    periodista?: string,
    fuente?: string
  ): Promise<{ created: number; success: boolean }> {
    try {
      // Obtener editores y administradores activos
      const editorsAndAdmins = await prisma.user.findMany({
        where: {
          role: {
            in: ['EDITOR', 'ADMIN'],
          },
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      if (editorsAndAdmins.length === 0) {
        console.log('⚠️ No hay editores o administradores activos para notificar');
        return { created: 0, success: true };
      }

      // Crear notificaciones masivas
      const result = await this.createBulkNotifications({
        tipo: 'WEBHOOK_NOTICIA',
        titulo: `Nueva noticia desde webhook: ${titulo}`,
        mensaje: `Noticia recibida${periodista ? ` de ${periodista}` : ''}${fuente ? ` desde ${fuente}` : ''} vía webhook`,
        userIds: editorsAndAdmins.map(user => user.id),
        noticiaId,
        metadata: {
          source: 'webhook',
          periodista,
          fuente,
          timestamp: new Date().toISOString(),
        },
      });

      console.log(`📢 Notificaciones de webhook enviadas a ${editorsAndAdmins.length} usuarios`);

      return result;

    } catch (error) {
      console.error('Error notifying webhook noticia:', error);
      throw new Error(`Error al notificar nueva noticia desde webhook: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Notificar sobre publicación externa exitosa
  static async notifyExternalPublication(
    noticiaId: number,
    diarioNombre: string,
    urlPublicacion?: string
  ): Promise<{ created: number; success: boolean }> {
    try {
      // Obtener la noticia y su autor
      const noticia = await prisma.noticia.findUnique({
        where: { id: noticiaId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!noticia) {
        throw new Error('Noticia no encontrada');
      }

      // Notificar al autor de la noticia
      const result = await this.createNotification({
        tipo: 'SISTEMA',
        titulo: `Noticia publicada en ${diarioNombre}`,
        mensaje: `Tu noticia "${noticia.titulo}" ha sido publicada exitosamente en ${diarioNombre}${urlPublicacion ? `. Ver: ${urlPublicacion}` : ''}`,
        userId: noticia.userId,
        noticiaId,
        metadata: {
          source: 'external_publication',
          diario: diarioNombre,
          url: urlPublicacion,
          timestamp: new Date().toISOString(),
        },
      });

      console.log(`📢 Notificación de publicación externa enviada al autor (ID: ${noticia.userId})`);

      return { created: 1, success: result.success };

    } catch (error) {
      console.error('Error notifying external publication:', error);
      throw new Error(`Error al notificar publicación externa: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Obtener estadísticas de notificaciones
  static async getNotificationStats(userId?: number): Promise<{
    total: number;
    unread: number;
    byType: Record<string, number>;
    recent: number; // últimas 24 horas
  }> {
    try {
      const where = userId ? { userId } : {};
      const last24h = new Date();
      last24h.setHours(last24h.getHours() - 24);

      const [total, unread, byType, recent] = await Promise.all([
        prisma.notificacion.count({ where }),
        prisma.notificacion.count({ where: { ...where, leida: false } }),
        prisma.notificacion.groupBy({
          by: ['tipo'],
          where,
          _count: { tipo: true },
        }),
        prisma.notificacion.count({
          where: {
            ...where,
            createdAt: { gte: last24h },
          },
        }),
      ]);

      const byTypeMap = byType.reduce((acc, item) => {
        acc[item.tipo] = item._count.tipo;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        unread,
        byType: byTypeMap,
        recent,
      };

    } catch (error) {
      console.error('Error getting notification stats:', error);
      throw new Error(`Error al obtener estadísticas de notificaciones: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }
}

export default NotificationService;
