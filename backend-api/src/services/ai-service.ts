import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from '@/config/database';
import { config } from '@/config/environment';

export interface RewriteRequest {
  titulo: string;
  subtitulo?: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  prompt: string;
  diarioNombre: string;
}

export interface RewriteResponse {
  titulo: string;
  volanta?: string;
  contenido: string;
  resumen?: string;
  metadatos: {
    modelo: string;
    tokens_usados: number;
    tiempo_generacion: number;
    diario: string;
    proveedor: string;
  };
}

export interface AIConfig {
  openaiApiKey?: string;
  openaiModel: string;
  openaiMaxTokens: number;
  openaiTemperature: number;
  geminiApiKey?: string;
  geminiModel: string;
  geminiMaxTokens: number;
  geminiTemperature: number;
  defaultProvider: 'OPENAI' | 'GEMINI';
}

export class AIService {
  private static openaiClient: OpenAI | null = null;
  private static geminiClient: GoogleGenerativeAI | null = null;

  // Obtener configuración de IA
  static async getAIConfig(): Promise<AIConfig> {
    try {
      const aiConfig = await prisma.aIConfig.findFirst({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      if (aiConfig) {
        return {
          openaiApiKey: aiConfig.openaiApiKey || config.ai.openai.apiKey,
          openaiModel: aiConfig.openaiModel,
          openaiMaxTokens: aiConfig.openaiMaxTokens,
          openaiTemperature: aiConfig.openaiTemperature,
          geminiApiKey: aiConfig.geminiApiKey || config.ai.gemini.apiKey,
          geminiModel: aiConfig.geminiModel,
          geminiMaxTokens: aiConfig.geminiMaxTokens,
          geminiTemperature: aiConfig.geminiTemperature,
          defaultProvider: aiConfig.defaultProvider,
        };
      }

      // Configuración por defecto desde variables de entorno
      return {
        openaiApiKey: config.ai.openai.apiKey,
        openaiModel: 'gpt-3.5-turbo',
        openaiMaxTokens: 2000,
        openaiTemperature: 0.7,
        geminiApiKey: config.ai.gemini.apiKey,
        geminiModel: 'gemini-pro',
        geminiMaxTokens: 2000,
        geminiTemperature: 0.7,
        defaultProvider: 'OPENAI',
      };
    } catch (error) {
      console.error('Error getting AI config:', error);
      throw new Error('Error al obtener configuración de IA');
    }
  }

  // Inicializar cliente OpenAI
  private static getOpenAIClient(apiKey: string): OpenAI {
    if (!this.openaiClient || this.openaiClient.apiKey !== apiKey) {
      this.openaiClient = new OpenAI({ apiKey });
    }
    return this.openaiClient;
  }

  // Inicializar cliente Gemini
  private static getGeminiClient(apiKey: string): GoogleGenerativeAI {
    if (!this.geminiClient) {
      this.geminiClient = new GoogleGenerativeAI(apiKey);
    }
    return this.geminiClient;
  }

  // Reescribir con OpenAI
  private static async rewriteWithOpenAI(
    request: RewriteRequest,
    config: AIConfig
  ): Promise<RewriteResponse> {
    if (!config.openaiApiKey) {
      throw new Error('OpenAI API key no configurada');
    }

    const startTime = Date.now();
    const client = this.getOpenAIClient(config.openaiApiKey);

    const systemPrompt = `${request.prompt}

Instrucciones específicas:
- Mantén la información factual exacta
- Adapta el estilo y tono para ${request.diarioNombre}
- Conserva la estructura: título, volanta (si existe), contenido, resumen (si existe)
- Responde SOLO con un JSON válido con esta estructura:
{
  "titulo": "nuevo título",
  "volanta": "nueva volanta o null",
  "contenido": "nuevo contenido",
  "resumen": "nuevo resumen o null"
}`;

    const userContent = `
Título: ${request.titulo}
${request.volanta ? `Volanta: ${request.volanta}` : ''}
${request.subtitulo ? `Subtítulo: ${request.subtitulo}` : ''}
Contenido: ${request.contenido}
${request.resumen ? `Resumen: ${request.resumen}` : ''}
`;

    try {
      const completion = await client.chat.completions.create({
        model: config.openaiModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userContent },
        ],
        max_tokens: config.openaiMaxTokens,
        temperature: config.openaiTemperature,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No se recibió respuesta de OpenAI');
      }

      const parsedResponse = JSON.parse(response);
      const endTime = Date.now();

      return {
        titulo: parsedResponse.titulo,
        volanta: parsedResponse.volanta,
        contenido: parsedResponse.contenido,
        resumen: parsedResponse.resumen,
        metadatos: {
          modelo: config.openaiModel,
          tokens_usados: completion.usage?.total_tokens || 0,
          tiempo_generacion: endTime - startTime,
          diario: request.diarioNombre,
          proveedor: 'OpenAI',
        },
      };
    } catch (error) {
      console.error('Error with OpenAI:', error);
      throw new Error(`Error de OpenAI: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Reescribir con Gemini
  private static async rewriteWithGemini(
    request: RewriteRequest,
    config: AIConfig
  ): Promise<RewriteResponse> {
    if (!config.geminiApiKey) {
      throw new Error('Gemini API key no configurada');
    }

    const startTime = Date.now();
    const client = this.getGeminiClient(config.geminiApiKey);
    const model = client.getGenerativeModel({ model: config.geminiModel });

    const prompt = `${request.prompt}

Instrucciones específicas:
- Mantén la información factual exacta
- Adapta el estilo y tono para ${request.diarioNombre}
- Conserva la estructura: título, volanta (si existe), contenido, resumen (si existe)
- Responde SOLO con un JSON válido con esta estructura:
{
  "titulo": "nuevo título",
  "volanta": "nueva volanta o null",
  "contenido": "nuevo contenido",
  "resumen": "nuevo resumen o null"
}

Contenido a reescribir:
Título: ${request.titulo}
${request.volanta ? `Volanta: ${request.volanta}` : ''}
${request.subtitulo ? `Subtítulo: ${request.subtitulo}` : ''}
Contenido: ${request.contenido}
${request.resumen ? `Resumen: ${request.resumen}` : ''}`;

    try {
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error('No se recibió respuesta de Gemini');
      }

      const parsedResponse = JSON.parse(text);
      const endTime = Date.now();

      return {
        titulo: parsedResponse.titulo,
        volanta: parsedResponse.volanta,
        contenido: parsedResponse.contenido,
        resumen: parsedResponse.resumen,
        metadatos: {
          modelo: config.geminiModel,
          tokens_usados: 0, // Gemini no proporciona token count
          tiempo_generacion: endTime - startTime,
          diario: request.diarioNombre,
          proveedor: 'Gemini',
        },
      };
    } catch (error) {
      console.error('Error with Gemini:', error);
      throw new Error(`Error de Gemini: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Función principal para reescribir noticias
  static async rewriteNoticia(
    request: RewriteRequest,
    diarioId?: number
  ): Promise<RewriteResponse> {
    try {
      console.log(`🤖 Iniciando reescritura para diario: ${request.diarioNombre} (ID: ${diarioId})`);

      const aiConfig = await this.getAIConfig();
      let provider = aiConfig.defaultProvider;

      // Si se especifica un diario, verificar su configuración específica
      if (diarioId) {
        const diario = await prisma.diario.findUnique({
          where: { id: diarioId },
        });

        if (diario && !diario.useGlobalConfig && diario.aiProvider) {
          provider = diario.aiProvider as 'OPENAI' | 'GEMINI';
          console.log(`🔄 Usando proveedor específico del diario: ${provider}`);

          // Si el diario tiene un modelo específico, usarlo
          if (diario.aiModel) {
            if (provider === 'OPENAI') {
              aiConfig.openaiModel = diario.aiModel;
            } else {
              aiConfig.geminiModel = diario.aiModel;
            }
          }
        }
      }

      console.log(`🎯 Proveedor seleccionado: ${provider}`);

      // Ejecutar reescritura según el proveedor
      if (provider === 'GEMINI') {
        return await this.rewriteWithGemini(request, aiConfig);
      } else {
        return await this.rewriteWithOpenAI(request, aiConfig);
      }
    } catch (error) {
      console.error('Error en rewriteNoticia:', error);
      throw new Error(`Error al generar reescritura: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  }

  // Función con fallback automático
  static async rewriteNoticiaWithFallback(
    request: RewriteRequest,
    diarioId?: number
  ): Promise<RewriteResponse> {
    const aiConfig = await this.getAIConfig();
    let provider = aiConfig.defaultProvider;

    // Determinar proveedor según configuración del diario
    if (diarioId) {
      const diario = await prisma.diario.findUnique({
        where: { id: diarioId },
      });

      if (diario && !diario.useGlobalConfig && diario.aiProvider) {
        provider = diario.aiProvider as 'OPENAI' | 'GEMINI';
        console.log(`🔄 Usando proveedor específico del diario: ${provider}`);

        // Si el diario tiene un modelo específico, usarlo
        if (diario.aiModel) {
          if (provider === 'OPENAI') {
            aiConfig.openaiModel = diario.aiModel;
          } else {
            aiConfig.geminiModel = diario.aiModel;
          }
        }
      }
    }

    console.log(`🎯 Intentando con proveedor principal: ${provider}`);

    try {
      // Intentar con el proveedor principal
      if (provider === 'GEMINI') {
        return await this.rewriteWithGemini(request, aiConfig);
      } else {
        return await this.rewriteWithOpenAI(request, aiConfig);
      }
    } catch (primaryError) {
      console.log(`❌ Error con proveedor principal (${provider}):`, primaryError);

      // Si es un error de tokens con Gemini, intentar fallback a OpenAI
      if (provider === 'GEMINI' && primaryError instanceof Error &&
          (primaryError.message.includes('tokens') || primaryError.message.includes('MAX_TOKENS'))) {

        console.log(`🔄 Error de tokens detectado, intentando fallback a OpenAI...`);

        try {
          return await this.rewriteWithOpenAI(request, aiConfig);
        } catch (fallbackError) {
          console.log(`❌ Fallback a OpenAI también falló:`, fallbackError);
          throw new Error(`Gemini falló por tokens, OpenAI también falló: ${fallbackError instanceof Error ? fallbackError.message : 'Error desconocido'}`);
        }
      }

      // Para otros errores, intentar con el proveedor alternativo
      const alternativeProvider = provider === 'OPENAI' ? 'GEMINI' : 'OPENAI';
      console.log(`🔄 Intentando fallback a ${alternativeProvider}...`);

      try {
        if (alternativeProvider === 'GEMINI') {
          return await this.rewriteWithGemini(request, aiConfig);
        } else {
          return await this.rewriteWithOpenAI(request, aiConfig);
        }
      } catch (fallbackError) {
        console.log(`❌ Fallback también falló:`, fallbackError);
        throw new Error(`Ambos proveedores fallaron. Principal: ${primaryError instanceof Error ? primaryError.message : 'Error desconocido'}. Fallback: ${fallbackError instanceof Error ? fallbackError.message : 'Error desconocido'}`);
      }
    }
  }

  // Verificar conexión con OpenAI
  static async testOpenAIConnection(apiKey?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const key = apiKey || config.ai.openai.apiKey;
      if (!key) {
        return { success: false, error: 'API key no configurada' };
      }

      const client = this.getOpenAIClient(key);
      
      const completion = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Responde exactamente con "CONEXION_OK"' }],
        max_tokens: 10,
      });

      const response = completion.choices[0]?.message?.content?.trim();
      
      if (response?.includes('CONEXION_OK')) {
        console.log('✅ Conexión OpenAI exitosa');
        return { success: true };
      } else {
        return { success: false, error: `Respuesta inesperada: "${response}"` };
      }
    } catch (error) {
      console.error('Error testing OpenAI connection:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Error desconocido' 
      };
    }
  }

  // Verificar conexión con Gemini
  static async testGeminiConnection(apiKey?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const key = apiKey || config.ai.gemini.apiKey;
      if (!key) {
        return { success: false, error: 'API key no configurada' };
      }

      const client = this.getGeminiClient(key);
      const model = client.getGenerativeModel({ model: 'gemini-pro' });

      const result = await model.generateContent('Responde exactamente con "CONEXION_OK"');
      const response = await result.response;
      const text = response.text().trim();

      if (text.includes('CONEXION_OK')) {
        console.log('✅ Conexión Gemini exitosa');
        return { success: true };
      } else {
        return { success: false, error: `Respuesta inesperada: "${text}"` };
      }
    } catch (error) {
      console.error('Error testing Gemini connection:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Error desconocido' 
      };
    }
  }
}

export default AIService;
