import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import compression from 'compression';
import { config } from '@/config/environment';
import { checkDatabaseConnection } from '@/config/database';
import { 
  error<PERSON><PERSON><PERSON>, 
  notFoundHandler, 
  handleUncaughtExceptions 
} from '@/middleware/error';
import { 
  securityHeaders, 
  apiRateLimiter, 
  securityLogger,
  sanitizeHeaders 
} from '@/middleware/security';
import { sanitizeRequest } from '@/middleware/validation';

// Importar rutas
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import noticiaRoutes from '@/routes/noticias';
import categoriaRoutes from '@/routes/categorias';
import aiRoutes from '@/routes/ai';
import uploadRoutes from '@/routes/upload';
import webhookRoutes from '@/routes/webhooks';
import externalPublicationRoutes from '@/routes/external-publication';
import healthRoutes from '@/routes/health';

// Configurar manejo de errores no capturados
handleUncaughtExceptions();

// Crear aplicación Express
const app = express();

// Trust proxy (importante para rate limiting y logs)
app.set('trust proxy', 1);

// Middleware de seguridad
app.use(securityHeaders);
app.use(sanitizeHeaders);
app.use(securityLogger);

// CORS
app.use(cors(config.cors));

// Compresión
app.use(compression());

// Logging
if (config.server.isDevelopment) {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Parsing de JSON con límite de tamaño
app.use(express.json({ 
  limit: '10mb',
  strict: true,
}));

// Parsing de URL encoded
app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb' 
}));

// Sanitización de entrada
app.use(sanitizeRequest);

// Rate limiting global
app.use('/api', apiRateLimiter);

// Health check (sin rate limiting)
app.use('/health', healthRoutes);

// Rutas de la API
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/noticias', noticiaRoutes);
app.use('/api/categorias', categoriaRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/webhooks', webhookRoutes);
app.use('/api/external-publication', externalPublicationRoutes);

// Ruta raíz
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Panel Unificado Backend API',
    version: '1.0.0',
    environment: config.server.nodeEnv,
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      users: '/api/users',
      noticias: '/api/noticias',
      categorias: '/api/categorias',
      ai: '/api/ai',
      upload: '/api/upload',
      webhooks: '/api/webhooks',
    }
  });
});

// Middleware para rutas no encontradas
app.use(notFoundHandler);

// Middleware de manejo de errores (debe ir al final)
app.use(errorHandler);

// Función para iniciar el servidor
async function startServer() {
  try {
    console.log('🚀 Iniciando Panel Unificado Backend API...');
    
    // Verificar conexión a la base de datos
    console.log('📊 Verificando conexión a la base de datos...');
    const dbConnected = await checkDatabaseConnection();
    
    if (!dbConnected) {
      console.error('❌ No se pudo conectar a la base de datos');
      process.exit(1);
    }

    // Iniciar servidor
    const server = app.listen(config.server.port, () => {
      console.log('✅ Servidor iniciado exitosamente');
      console.log(`🌐 URL: ${config.server.apiBaseUrl}`);
      console.log(`🔧 Entorno: ${config.server.nodeEnv}`);
      console.log(`📡 Puerto: ${config.server.port}`);
      console.log('📋 Endpoints disponibles:');
      console.log('   - GET  /health          - Health check');
      console.log('   - POST /api/auth/login  - Autenticación');
      console.log('   - GET  /api/users       - Usuarios');
      console.log('   - GET  /api/noticias    - Noticias');
      console.log('   - GET  /api/categorias  - Categorías');
      console.log('   - POST /api/ai/rewrite  - IA Reescritura');
      console.log('   - POST /api/upload      - Subir archivos');
      console.log('   - POST /api/webhooks    - Webhooks');
    });

    // Manejo de cierre graceful
    const gracefulShutdown = (signal: string) => {
      console.log(`\n🛑 Recibida señal ${signal}, cerrando servidor...`);
      
      server.close(() => {
        console.log('✅ Servidor cerrado exitosamente');
        process.exit(0);
      });

      // Forzar cierre después de 10 segundos
      setTimeout(() => {
        console.error('❌ Forzando cierre del servidor');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Error al iniciar el servidor:', error);
    process.exit(1);
  }
}

// Iniciar servidor solo si este archivo es ejecutado directamente
if (require.main === module) {
  startServer();
}

export default app;
