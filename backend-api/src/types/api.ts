// Tipos base para respuestas de API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  data: {
    items: T[];
    pagination: PaginationInfo;
  };
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Tipos para filtros comunes
export interface BaseFilters {
  search?: string;
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
}

// Tipos para validación de entrada
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResponse {
  success: false;
  errors: ValidationError[];
}

// Tipos para manejo de errores
export interface ErrorResponse extends ApiResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
  stack?: string; // Solo en desarrollo
}

// Tipos para operaciones CRUD
export interface CreateResponse<T = any> extends ApiResponse<T> {
  data: T;
}

export interface UpdateResponse<T = any> extends ApiResponse<T> {
  data: T;
}

export interface DeleteResponse extends ApiResponse {
  data: {
    deleted: boolean;
    id: number | string;
  };
}

// Tipos para búsqueda
export interface SearchQuery extends PaginationQuery {
  q?: string;
  filters?: Record<string, any>;
}

export interface SearchResponse<T = any> extends PaginatedResponse<T> {
  data: {
    items: T[];
    pagination: PaginationInfo;
    query: string;
    totalMatches: number;
  };
}

// Tipos para estadísticas
export interface StatsResponse extends ApiResponse {
  data: {
    [key: string]: number | string | Date;
  };
}

// Tipos para operaciones en lote
export interface BulkOperation<T = any> {
  items: T[];
  operation: 'create' | 'update' | 'delete';
}

export interface BulkResponse<T = any> extends ApiResponse {
  data: {
    successful: T[];
    failed: Array<{
      item: T;
      error: string;
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
    };
  };
}

// Tipos para upload de archivos
export interface FileUploadResponse extends ApiResponse {
  data: {
    filename: string;
    originalName: string;
    size: number;
    mimetype: string;
    url: string;
    path: string;
    width?: number;
    height?: number;
    optimizationStats?: {
      originalSize: number;
      optimizedSize: number;
      compressionRatio: number;
      processingTime: number;
    };
  };
}

// Tipos para health check
export interface HealthCheckResponse extends ApiResponse {
  data: {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    database: {
      status: 'connected' | 'disconnected';
      responseTime?: number;
    };
    externalServices: {
      [serviceName: string]: {
        status: 'available' | 'unavailable';
        responseTime?: number;
        lastCheck: string;
      };
    };
  };
}

// Tipos para configuración de API
export interface ApiConfig {
  version: string;
  baseUrl: string;
  timeout: number;
  retries: number;
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

export default {
  ApiResponse,
  PaginatedResponse,
  PaginationInfo,
  PaginationQuery,
  BaseFilters,
  ValidationError,
  ValidationResponse,
  ErrorResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse,
  SearchQuery,
  SearchResponse,
  StatsResponse,
  BulkOperation,
  BulkResponse,
  FileUploadResponse,
  HealthCheckResponse,
  ApiConfig,
};
