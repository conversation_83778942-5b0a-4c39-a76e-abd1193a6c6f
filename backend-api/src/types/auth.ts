import { Request } from 'express';

// Tipos para autenticación JWT
export interface JWTPayload {
  userId: number;
  email: string;
  role: string;
  name: string;
  iat?: number;
  exp?: number;
}

// Extender Request de Express para incluir usuario autenticado
export interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}

// Tipos para login/registro
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  data?: {
    user: UserInfo;
    token: string;
    refreshToken: string;
  };
  error?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  data?: {
    token: string;
    refreshToken: string;
  };
  error?: string;
}

// Información del usuario
export interface UserInfo {
  id: number;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Tipos para registro de usuarios (admin)
export interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
  role?: 'USER' | 'EDITOR' | 'ADMIN';
  isActive?: boolean;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: 'USER' | 'EDITOR' | 'ADMIN';
  isActive?: boolean;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Tipos para validación de roles
export type UserRole = 'USER' | 'EDITOR' | 'ADMIN';

export interface RolePermissions {
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canAdmin: boolean;
}

// Tipos para rate limiting
export interface RateLimitInfo {
  limit: number;
  current: number;
  remaining: number;
  resetTime: Date;
}

// Tipos para sesiones
export interface SessionInfo {
  userId: number;
  email: string;
  role: string;
  loginTime: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
}

// Tipos para audit logs
export interface AuditLogEntry {
  userId: number;
  action: string;
  entityType: string;
  entityId: string;
  details?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
}

export default {
  JWTPayload,
  AuthenticatedRequest,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  UserInfo,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  UserRole,
  RolePermissions,
  RateLimitInfo,
  SessionInfo,
  AuditLogEntry,
};
