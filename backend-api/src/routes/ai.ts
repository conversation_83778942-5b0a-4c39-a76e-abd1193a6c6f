import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '@/config/database';
import {
  authenticateToken,
  requireRole
} from '@/middleware/auth';
import {
  validateSchema,
  validateParams,
  commonSchemas
} from '@/middleware/validation';
import {
  async<PERSON><PERSON><PERSON>,
  NotFoundError
} from '@/middleware/error';
import {
  AuthenticatedRequest
} from '@/types/auth';
import AIService from '@/services/ai-service';
import AIImageService from '@/services/ai-image-service';

const router = Router();

// Schemas de validación
const rewriteNoticiaSchema = z.object({
  titulo: commonSchemas.requiredString,
  subtitulo: z.string().optional(),
  volanta: z.string().optional(),
  contenido: commonSchemas.requiredString,
  resumen: z.string().optional(),
  diarioId: z.number().int().positive(),
  prompt: z.string().optional(),
});

const generateImageSchema = z.object({
  titulo: commonSchemas.requiredString,
  resumen: z.string().optional(),
  contenido: z.string().optional(),
  estilo: z.string().optional(),
  configuracionId: z.number().int().positive().optional(),
  noticiaId: z.number().int().positive().optional(),
});

// POST /api/ai/rewrite - Reescribir noticia con IA
router.post('/rewrite',
  authenticateToken,
  validateSchema(rewriteNoticiaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { titulo, subtitulo, volanta, contenido, resumen, diarioId, prompt } = req.body;
    const userId = req.user!.userId;

    // Verificar que el diario existe
    const diario = await prisma.diario.findUnique({
      where: { id: diarioId },
    });

    if (!diario || !diario.isActive) {
      throw new NotFoundError('Diario');
    }

    // Usar el servicio de IA para reescribir
    const rewriteResult = await AIService.rewriteNoticiaWithFallback({
      titulo,
      subtitulo,
      volanta,
      contenido,
      resumen,
      prompt: prompt || diario.prompt,
      diarioNombre: diario.nombre,
    }, diarioId);

    // Crear versión de la noticia
    const version = await prisma.versionNoticia.create({
      data: {
        titulo: rewriteResult.titulo,
        subtitulo,
        volanta: rewriteResult.volanta,
        contenido: rewriteResult.contenido,
        resumen: rewriteResult.resumen,
        noticiaId: req.body.noticiaId || 0, // Esto debería venir del request
        diarioId,
        generadaPor: userId,
        estado: 'GENERADA',
        promptUsado: prompt || diario.prompt,
        metadatos: JSON.stringify(rewriteResult.metadatos),
      },
      include: {
        diario: {
          select: {
            id: true,
            nombre: true,
            descripcion: true,
          },
        },
        usuario: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: {
        version: {
          ...version,
          createdAt: version.createdAt.toISOString(),
          updatedAt: version.updatedAt.toISOString(),
        },
        rewrite: rewriteResult,
      },
    });
  })
);

// POST /api/ai/generate-image - Generar imagen con IA
router.post('/generate-image',
  authenticateToken,
  validateSchema(generateImageSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { titulo, resumen, contenido, estilo, configuracionId, noticiaId } = req.body;
    const userId = req.user!.userId;

    // Verificar límites de uso si se especifica configuración
    if (configuracionId) {
      const limits = await AIImageService.checkUsageLimits(userId, configuracionId);
      if (!limits.canGenerate) {
        return res.status(429).json({
          success: false,
          error: 'Límite de generaciones alcanzado',
          data: {
            remaining: limits.remaining,
            limit: limits.limit,
            resetDate: limits.resetDate.toISOString(),
          },
        });
      }
    }

    // Usar el servicio de IA para generar imagen
    const result = await AIImageService.generateImage({
      titulo,
      resumen,
      contenido,
      estilo,
      configuracionId,
      noticiaId,
      userId,
    });

    // Incrementar uso si se especifica configuración
    if (configuracionId) {
      await AIImageService.incrementUsage(userId, configuracionId);
    }

    res.json({
      success: true,
      data: result,
    });
  })
);

// GET /api/ai/image-generation/:id - Obtener estado de generación de imagen
router.get('/image-generation/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const userId = req.user!.userId;

    const result = await AIImageService.getGenerationStatus(parseInt(id), userId);

    res.json({
      success: true,
      data: result,
    });
  })
);

// GET /api/ai/models - Obtener modelos de IA disponibles
router.get('/models',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const configuraciones = await prisma.configuracionIA.findMany({
      where: { activo: true },
      select: {
        id: true,
        nombre: true,
        proveedor: true,
        modelo: true,
        parametros: true,
        promptPorDefecto: true,
      },
      orderBy: { nombre: 'asc' },
    });

    res.json({
      success: true,
      data: {
        configuraciones,
        providers: [
          {
            name: 'OpenAI',
            models: ['gpt-3.5-turbo', 'gpt-4', 'dall-e-3'],
          },
          {
            name: 'Google Gemini',
            models: ['gemini-pro', 'gemini-pro-vision'],
          },
        ],
      },
    });
  })
);

export default router;
