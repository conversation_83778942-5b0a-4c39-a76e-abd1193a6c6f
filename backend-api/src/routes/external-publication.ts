import { Router } from 'express';
import { z } from 'zod';
import { 
  authenticateToken, 
  requireRole 
} from '@/middleware/auth';
import { 
  validateSchema, 
  validateParams,
  commonSchemas 
} from '@/middleware/validation';
import { 
  asyncHand<PERSON>, 
  NotFoundError 
} from '@/middleware/error';
import { 
  AuthenticatedRequest 
} from '@/types/auth';
import ExternalPublicationService from '@/services/external-publication-service';

const router = Router();

// Schemas de validación
const publishNoticiaSchema = z.object({
  noticiaId: z.number().int().positive(),
  diarioExternoId: z.number().int().positive(),
});

const testConnectionSchema = z.object({
  diarioExternoId: z.number().int().positive(),
});

// POST /api/external-publication/publish - Publicar noticia en diario externo
router.post('/publish',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  validateSchema(publishNoticiaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { noticiaId, diarioExternoId } = req.body;
    const userId = req.user!.userId;

    console.log(`🚀 Iniciando publicación externa - Usuario: ${req.user!.email}, Noticia: ${noticiaId}, Diario: ${diarioExternoId}`);

    const result = await ExternalPublicationService.publishNoticia({
      noticiaId,
      diarioExternoId,
      userId,
    });

    if (result.success) {
      res.json({
        success: true,
        data: {
          publicacionId: result.publicacionId,
          estado: result.estado,
        },
        message: result.message,
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        data: {
          publicacionId: result.publicacionId,
          estado: result.estado,
        },
      });
    }
  })
);

// GET /api/external-publication/status/:id - Obtener estado de publicación
router.get('/status/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    const status = await ExternalPublicationService.getPublicationStatus(parseInt(id));

    res.json({
      success: true,
      data: status,
    });
  })
);

// GET /api/external-publication/noticia/:id - Obtener publicaciones de una noticia
router.get('/noticia/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    const publications = await ExternalPublicationService.getNoticiaPublications(parseInt(id));

    res.json({
      success: true,
      data: {
        noticiaId: parseInt(id),
        publications,
        total: publications.length,
      },
    });
  })
);

// POST /api/external-publication/retry/:id - Reintentar publicación fallida
router.post('/retry/:id',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    console.log(`🔄 Reintentando publicación - Usuario: ${req.user!.email}, Publicación: ${id}`);

    const result = await ExternalPublicationService.retryPublication(parseInt(id));

    if (result.success) {
      res.json({
        success: true,
        data: {
          publicacionId: result.publicacionId,
          estado: result.estado,
        },
        message: result.message,
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        data: {
          publicacionId: result.publicacionId,
          estado: result.estado,
        },
      });
    }
  })
);

// POST /api/external-publication/test-connection - Probar conexión con diario externo
router.post('/test-connection',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  validateSchema(testConnectionSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { diarioExternoId } = req.body;

    console.log(`🔍 Probando conexión - Usuario: ${req.user!.email}, Diario: ${diarioExternoId}`);

    const result = await ExternalPublicationService.testConnection(diarioExternoId);

    res.json({
      success: result.success,
      message: result.message,
      data: result.details,
    });
  })
);

// GET /api/external-publication/diarios - Listar diarios externos disponibles
router.get('/diarios',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const diarios = await ExternalPublicationService.getDiarioConfig(0).catch(() => null);
    
    // En lugar de obtener uno específico, obtener todos los diarios activos
    const { prisma } = await import('@/config/database');
    
    const diariosExternos = await prisma.diarioExterno.findMany({
      where: { activo: true },
      select: {
        id: true,
        nombre: true,
        descripcion: true,
        urlBase: true,
        activo: true,
        createdAt: true,
      },
      orderBy: { nombre: 'asc' },
    });

    res.json({
      success: true,
      data: {
        diarios: diariosExternos.map(diario => ({
          ...diario,
          createdAt: diario.createdAt.toISOString(),
        })),
        total: diariosExternos.length,
      },
    });
  })
);

// GET /api/external-publication/stats - Estadísticas de publicaciones externas
router.get('/stats',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { prisma } = await import('@/config/database');
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      total,
      successful,
      failed,
      pending,
      todayCount,
      weekCount,
      monthCount,
      byDiario,
      byEstado,
    ] = await Promise.all([
      prisma.publicacionExterna.count(),
      prisma.publicacionExterna.count({ where: { estado: 'EXITOSO' } }),
      prisma.publicacionExterna.count({ 
        where: { 
          estado: { 
            in: ['ERROR_IMAGEN', 'ERROR_ARTICULO', 'ERROR_CONEXION'] 
          } 
        } 
      }),
      prisma.publicacionExterna.count({ 
        where: { 
          estado: { 
            in: ['PENDIENTE', 'SUBIENDO_IMAGEN', 'PUBLICANDO_ARTICULO'] 
          } 
        } 
      }),
      prisma.publicacionExterna.count({
        where: { createdAt: { gte: today } },
      }),
      prisma.publicacionExterna.count({
        where: { createdAt: { gte: thisWeek } },
      }),
      prisma.publicacionExterna.count({
        where: { createdAt: { gte: thisMonth } },
      }),
      prisma.publicacionExterna.groupBy({
        by: ['diarioExternoId'],
        _count: { diarioExternoId: true },
        include: {
          diarioExterno: {
            select: { nombre: true },
          },
        },
      }),
      prisma.publicacionExterna.groupBy({
        by: ['estado'],
        _count: { estado: true },
      }),
    ]);

    const byDiarioMap = byDiario.reduce((acc: any, item: any) => {
      acc[item.diarioExternoId] = item._count.diarioExternoId;
      return acc;
    }, {});

    const byEstadoMap = byEstado.reduce((acc: any, item: any) => {
      acc[item.estado] = item._count.estado;
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        total,
        successful,
        failed,
        pending,
        successRate: total > 0 ? ((successful / total) * 100).toFixed(1) : '0',
        activity: {
          today: todayCount,
          thisWeek: weekCount,
          thisMonth: monthCount,
        },
        byDiario: byDiarioMap,
        byEstado: byEstadoMap,
        timestamp: new Date().toISOString(),
      },
    });
  })
);

export default router;
