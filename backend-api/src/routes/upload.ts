import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import { config } from '@/config/environment';
import {
  authenticateToken
} from '@/middleware/auth';
import {
  uploadRateLimiter
} from '@/middleware/security';
import {
  asyncHandler,
  ValidationError
} from '@/middleware/error';
import {
  AuthenticatedRequest
} from '@/types/auth';
import {
  FileUploadResponse
} from '@/types/api';
import ImageService from '@/services/image-service';

const router = Router();

// Configuración de multer para subida de archivos
const storage = multer.memoryStorage();

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Tipos de archivo permitidos
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/gif',
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`Tipo de archivo no permitido: ${file.mimetype}. Tipos permitidos: ${allowedMimeTypes.join(', ')}`));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize, // 10MB por defecto
    files: 5, // Máximo 5 archivos por request
  },
});

// Función para crear directorio si no existe
async function ensureDirectoryExists(dirPath: string) {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

// Función para optimizar imagen
async function optimizeImage(buffer: Buffer, filename: string): Promise<Buffer> {
  const ext = path.extname(filename).toLowerCase();
  
  let sharpInstance = sharp(buffer);
  
  // Redimensionar si es muy grande
  const metadata = await sharpInstance.metadata();
  if (metadata.width && metadata.width > 1920) {
    sharpInstance = sharpInstance.resize(1920, null, {
      withoutEnlargement: true,
    });
  }

  // Optimizar según el formato
  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return sharpInstance
        .jpeg({ quality: 85, progressive: true })
        .toBuffer();
    
    case '.png':
      return sharpInstance
        .png({ quality: 85, progressive: true })
        .toBuffer();
    
    case '.webp':
      return sharpInstance
        .webp({ quality: 85 })
        .toBuffer();
    
    default:
      return sharpInstance.toBuffer();
  }
}

// POST /api/upload/image - Subir imagen
router.post('/image',
  uploadRateLimiter,
  authenticateToken,
  upload.single('image'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    if (!req.file) {
      throw new ValidationError('No se proporcionó ningún archivo');
    }

    const file = req.file;
    const userId = req.user!.userId;

    // Validar archivo de imagen
    const validation = await ImageService.validateImageFile(file.buffer, file.originalname);
    if (!validation.valid) {
      throw new ValidationError(validation.error || 'Archivo de imagen inválido');
    }

    try {
      // Optimizar imagen usando el servicio
      const result = await ImageService.optimizeImage(file.buffer, file.originalname, {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 85,
        format: 'auto',
      });

      const response: FileUploadResponse = {
        success: true,
        data: {
          filename: result.filename,
          originalName: result.originalName,
          size: result.size,
          mimetype: `image/${result.format}`,
          url: result.url,
          path: result.path,
          width: result.width,
          height: result.height,
          optimizationStats: result.optimizationStats,
        },
      };

      console.log(`✅ Imagen subida y optimizada: ${result.filename} por usuario ${userId}`);
      res.json(response);

    } catch (error) {
      console.error('Error processing image:', error);
      throw new ValidationError(`Error al procesar la imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
  })
);

// POST /api/upload/images - Subir múltiples imágenes
router.post('/images',
  uploadRateLimiter,
  authenticateToken,
  upload.array('images', 5),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      throw new ValidationError('No se proporcionaron archivos');
    }

    const userId = req.user!.userId;
    const uploadedFiles = [];
    
    // Crear estructura de directorios por fecha
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    const uploadDir = path.join(config.upload.uploadDir, 'images', String(year), month, day);
    await ensureDirectoryExists(uploadDir);

    for (const file of files) {
      try {
        // Generar nombre único para el archivo
        const fileExtension = path.extname(file.originalname);
        const fileName = `${uuidv4()}${fileExtension}`;
        const filePath = path.join(uploadDir, fileName);
        
        // Optimizar imagen
        const optimizedBuffer = await optimizeImage(file.buffer, file.originalname);
        
        // Guardar archivo
        await fs.writeFile(filePath, optimizedBuffer);
        
        // Generar URL pública
        const publicUrl = `/uploads/images/${year}/${month}/${day}/${fileName}`;
        
        uploadedFiles.push({
          filename: fileName,
          originalName: file.originalname,
          size: optimizedBuffer.length,
          mimetype: file.mimetype,
          url: publicUrl,
          path: filePath,
        });
        
      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        // Continuar con los otros archivos
      }
    }

    console.log(`✅ ${uploadedFiles.length} imágenes subidas por usuario ${userId}`);
    
    res.json({
      success: true,
      data: {
        files: uploadedFiles,
        uploaded: uploadedFiles.length,
        total: files.length,
      },
    });
  })
);

// DELETE /api/upload/image/:filename - Eliminar imagen
router.delete('/image/:filename',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { filename } = req.params;
    const userRole = req.user!.role;
    
    // Solo admins pueden eliminar archivos
    if (userRole !== 'ADMIN') {
      throw new ValidationError('No tienes permisos para eliminar archivos');
    }

    // Buscar archivo en la estructura de directorios
    // Esto es una implementación simplificada
    const uploadBaseDir = path.join(config.upload.uploadDir, 'images');
    
    try {
      // Buscar archivo recursivamente (implementación básica)
      const findFile = async (dir: string): Promise<string | null> => {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory()) {
            const found = await findFile(fullPath);
            if (found) return found;
          } else if (entry.name === filename) {
            return fullPath;
          }
        }
        
        return null;
      };
      
      const filePath = await findFile(uploadBaseDir);
      
      if (!filePath) {
        throw new ValidationError('Archivo no encontrado');
      }
      
      await fs.unlink(filePath);
      
      console.log(`✅ Archivo eliminado: ${filename} por ${req.user!.email}`);
      
      res.json({
        success: true,
        message: 'Archivo eliminado exitosamente',
        filename,
      });
      
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new ValidationError('Error al eliminar el archivo');
    }
  })
);

export default router;
