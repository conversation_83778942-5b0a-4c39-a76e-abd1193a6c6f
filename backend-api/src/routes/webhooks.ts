import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '@/config/database';
import { config } from '@/config/environment';
import {
  validateSchema
} from '@/middleware/validation';
import {
  async<PERSON><PERSON><PERSON>,
  UnauthorizedError
} from '@/middleware/error';
import WebhookService from '@/services/webhook-service';

const router = Router();

// Schema para webhook de noticia
const webhookNoticiaSchema = z.object({
  titulo: z.string().min(1, 'Título requerido'),
  volanta: z.string().optional(),
  subtitulo: z.string().optional(),
  resumen: z.string().optional(),
  contenido: z.string().min(1, 'Contenido requerido'),
  autor: z.string().optional(),
  periodista: z.string().optional(),
  fuente: z.string().optional(),
  urlFuente: z.string().url().optional(),
  imagenUrl: z.string().url().optional(),
  imagenAlt: z.string().optional(),
  categoria: z.string().optional(),
  destacada: z.boolean().optional().default(false),
  webhookToken: z.string().optional(),
});

// Middleware para validar token de webhook
const validateWebhookToken = (req: any, res: any, next: any) => {
  const token = req.body.webhookToken || req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    throw new UnauthorizedError('Token de webhook requerido');
  }

  if (!WebhookService.validateWebhookToken(token, config.webhooks.token)) {
    throw new UnauthorizedError('Token de webhook inválido');
  }

  next();
};

// POST /api/webhooks/noticia - Recibir noticia desde webhook
router.post('/noticia',
  validateWebhookToken,
  validateSchema(webhookNoticiaSchema),
  asyncHandler(async (req, res) => {
    const webhookData = req.body;
    const source = req.headers['user-agent'] || 'webhook';

    console.log('📥 Webhook recibido:', {
      titulo: webhookData.titulo.substring(0, 50) + '...',
      periodista: webhookData.periodista,
      fuente: webhookData.fuente,
      source,
      timestamp: new Date().toISOString(),
    });

    // Procesar noticia usando el servicio
    const result = await WebhookService.processWebhookNoticia(webhookData, source);

    if (result.success) {
      res.json({
        success: true,
        data: {
          noticiaId: result.noticiaId,
          categoriaId: result.categoriaId,
          notificationsCreated: result.notificationsCreated,
        },
        message: result.message,
        warnings: result.errors,
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message,
        errors: result.errors,
      });
    }
  })
);

// GET /api/webhooks/noticia - Información del webhook
router.get('/noticia', (req, res) => {
  const webhookInfo = WebhookService.getWebhookInfo();

  res.json({
    success: true,
    data: webhookInfo,
  });
});

// POST /api/webhooks/test - Endpoint de prueba
router.post('/test',
  validateWebhookToken,
  asyncHandler(async (req, res) => {
    console.log('🧪 Webhook de prueba recibido:', {
      body: req.body,
      headers: {
        'content-type': req.headers['content-type'],
        'user-agent': req.headers['user-agent'],
      },
      timestamp: new Date().toISOString(),
    });

    res.json({
      success: true,
      message: 'Webhook de prueba recibido exitosamente',
      data: {
        received: req.body,
        timestamp: new Date().toISOString(),
      },
    });
  })
);

// GET /api/webhooks/stats - Estadísticas de webhooks
router.get('/stats',
  asyncHandler(async (req, res) => {
    const stats = await WebhookService.getWebhookStats();

    res.json({
      success: true,
      data: {
        ...stats,
        timestamp: new Date().toISOString(),
      },
    });
  })
);

export default router;
