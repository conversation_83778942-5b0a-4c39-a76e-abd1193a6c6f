import { Router } from 'express';
import bcrypt from 'bcrypt';
import { z } from 'zod';
import { prisma } from '@/config/database';
import { config } from '@/config/environment';
import { 
  authenticateToken, 
  generateTokens 
} from '@/middleware/auth';
import { 
  authRateLimiter 
} from '@/middleware/security';
import { 
  validateSchema, 
  commonSchemas 
} from '@/middleware/validation';
import { 
  asyncHandler, 
  UnauthorizedError, 
  ValidationError 
} from '@/middleware/error';
import { 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenRequest, 
  RefreshTokenResponse,
  AuthenticatedRequest 
} from '@/types/auth';

const router = Router();

// Schemas de validación
const loginSchema = z.object({
  email: commonSchemas.email,
  password: z.string().min(1, 'La contraseña es requerida'),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token requerido'),
});

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Contraseña actual requerida'),
  newPassword: commonSchemas.password,
});

// POST /api/auth/login - Iniciar sesión
router.post('/login', 
  authRateLimiter,
  validateSchema(loginSchema),
  asyncHandler(async (req, res) => {
    const { email, password }: LoginRequest = req.body;

    // Buscar usuario
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (!user) {
      throw new UnauthorizedError('Credenciales inválidas');
    }

    if (!user.isActive) {
      throw new UnauthorizedError('Usuario inactivo');
    }

    // Verificar contraseña
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError('Credenciales inválidas');
    }

    // Generar tokens
    const { accessToken, refreshToken } = generateTokens({
      userId: user.id,
      email: user.email,
      role: user.role,
      name: user.name,
    });

    const response: LoginResponse = {
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
        },
        token: accessToken,
        refreshToken,
      },
    };

    console.log(`✅ Login exitoso: ${user.email}`);
    res.json(response);
  })
);

// POST /api/auth/refresh - Renovar token
router.post('/refresh',
  validateSchema(refreshTokenSchema),
  asyncHandler(async (req, res) => {
    const { refreshToken }: RefreshTokenRequest = req.body;

    try {
      // Verificar refresh token
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(refreshToken, config.jwt.secret);
      
      // Verificar que el usuario sigue existiendo y activo
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
      });

      if (!user || !user.isActive) {
        throw new UnauthorizedError('Usuario no válido');
      }

      // Generar nuevos tokens
      const { accessToken, refreshToken: newRefreshToken } = generateTokens({
        userId: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
      });

      const response: RefreshTokenResponse = {
        success: true,
        data: {
          token: accessToken,
          refreshToken: newRefreshToken,
        },
      };

      res.json(response);
    } catch (error) {
      throw new UnauthorizedError('Refresh token inválido');
    }
  })
);

// GET /api/auth/me - Obtener información del usuario actual
router.get('/me',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new UnauthorizedError('Usuario no encontrado');
    }

    res.json({
      success: true,
      data: {
        ...user,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      },
    });
  })
);

// POST /api/auth/change-password - Cambiar contraseña
router.post('/change-password',
  authenticateToken,
  validateSchema(changePasswordSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.userId;

    // Obtener usuario actual
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new UnauthorizedError('Usuario no encontrado');
    }

    // Verificar contraseña actual
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedError('Contraseña actual incorrecta');
    }

    // Hash de la nueva contraseña
    const hashedNewPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds);

    // Actualizar contraseña
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });

    console.log(`✅ Contraseña cambiada para usuario: ${user.email}`);
    
    res.json({
      success: true,
      message: 'Contraseña actualizada exitosamente',
    });
  })
);

// POST /api/auth/logout - Cerrar sesión
router.post('/logout',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    // En un sistema con blacklist de tokens, aquí se agregaría el token a la blacklist
    // Por ahora, simplemente confirmamos el logout
    
    console.log(`✅ Logout exitoso para usuario: ${req.user!.email}`);
    
    res.json({
      success: true,
      message: 'Sesión cerrada exitosamente',
    });
  })
);

export default router;
