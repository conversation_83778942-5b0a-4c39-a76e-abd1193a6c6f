import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '@/config/database';
import { 
  authenticateToken, 
  requireRole 
} from '@/middleware/auth';
import { 
  validateSchema, 
  validateQuery,
  validateParams,
  commonSchemas 
} from '@/middleware/validation';
import { 
  as<PERSON><PERSON><PERSON><PERSON>, 
  NotFoundError, 
  ConflictError 
} from '@/middleware/error';
import { 
  AuthenticatedRequest 
} from '@/types/auth';
import { 
  PaginatedResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse 
} from '@/types/api';

const router = Router();

// Schemas de validación
const createCategoriaSchema = z.object({
  nombre: commonSchemas.requiredString,
  descripcion: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Color debe ser un código hexadecimal válido').optional().default('#3B82F6'),
  orden: z.number().int().positive().optional().default(1),
  isActive: z.boolean().optional().default(true),
});

const updateCategoriaSchema = z.object({
  nombre: z.string().min(1).optional(),
  descripcion: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Color debe ser un código hexadecimal válido').optional(),
  orden: z.number().int().positive().optional(),
  isActive: z.boolean().optional(),
});

const getCategoriasQuerySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  isActive: commonSchemas.booleanFromString,
});

// GET /api/categorias - Obtener categorías
router.get('/',
  authenticateToken,
  validateQuery(getCategoriasQuerySchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { page, limit, search, isActive, sortBy, sortOrder } = req.query as any;

    // Construir filtros
    const where: any = {};
    
    if (search) {
      where.OR = [
        { nombre: { contains: search, mode: 'insensitive' } },
        { descripcion: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }

    // Construir ordenamiento
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'asc';
    } else {
      orderBy.orden = 'asc';
    }

    // Obtener total de registros
    const totalItems = await prisma.categoria.count({ where });
    const totalPages = Math.ceil(totalItems / limit);

    // Obtener categorías
    const categorias = await prisma.categoria.findMany({
      where,
      include: {
        _count: {
          select: {
            noticias: true,
          },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    const response: PaginatedResponse = {
      success: true,
      data: {
        items: categorias.map(categoria => ({
          ...categoria,
          createdAt: categoria.createdAt.toISOString(),
          updatedAt: categoria.updatedAt.toISOString(),
        })),
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    };

    res.json(response);
  })
);

// GET /api/categorias/active - Obtener solo categorías activas (para selects)
router.get('/active',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const categorias = await prisma.categoria.findMany({
      where: { isActive: true },
      select: {
        id: true,
        nombre: true,
        descripcion: true,
        color: true,
        orden: true,
      },
      orderBy: { orden: 'asc' },
    });

    res.json({
      success: true,
      data: categorias,
    });
  })
);

// GET /api/categorias/:id - Obtener categoría por ID
router.get('/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    const categoria = await prisma.categoria.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            noticias: true,
            categoriaMapeos: true,
          },
        },
      },
    });

    if (!categoria) {
      throw new NotFoundError('Categoría');
    }

    res.json({
      success: true,
      data: {
        ...categoria,
        createdAt: categoria.createdAt.toISOString(),
        updatedAt: categoria.updatedAt.toISOString(),
      },
    });
  })
);

// POST /api/categorias - Crear categoría (solo editores y admins)
router.post('/',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  validateSchema(createCategoriaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { nombre, descripcion, color, orden, isActive } = req.body;

    // Verificar que el nombre no exista
    const existingCategoria = await prisma.categoria.findUnique({
      where: { nombre },
    });

    if (existingCategoria) {
      throw new ConflictError('Ya existe una categoría con este nombre');
    }

    // Crear categoría
    const categoria = await prisma.categoria.create({
      data: {
        nombre,
        descripcion,
        color: color || '#3B82F6',
        orden: orden || 1,
        isActive: isActive !== undefined ? isActive : true,
      },
    });

    const response: CreateResponse = {
      success: true,
      data: {
        ...categoria,
        createdAt: categoria.createdAt.toISOString(),
        updatedAt: categoria.updatedAt.toISOString(),
      },
    };

    console.log(`✅ Categoría creada: ${categoria.nombre} por ${req.user!.email}`);
    res.status(201).json(response);
  })
);

// PUT /api/categorias/:id - Actualizar categoría (solo editores y admins)
router.put('/:id',
  authenticateToken,
  requireRole(['EDITOR', 'ADMIN']),
  validateParams(commonSchemas.id),
  validateSchema(updateCategoriaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const updateData = req.body;

    // Verificar que la categoría existe
    const existingCategoria = await prisma.categoria.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingCategoria) {
      throw new NotFoundError('Categoría');
    }

    // Si se está actualizando el nombre, verificar que no exista
    if (updateData.nombre && updateData.nombre !== existingCategoria.nombre) {
      const nombreExists = await prisma.categoria.findUnique({
        where: { nombre: updateData.nombre },
      });

      if (nombreExists) {
        throw new ConflictError('Ya existe una categoría con este nombre');
      }
    }

    // Actualizar categoría
    const updatedCategoria = await prisma.categoria.update({
      where: { id: parseInt(id) },
      data: updateData,
    });

    const response: UpdateResponse = {
      success: true,
      data: {
        ...updatedCategoria,
        createdAt: updatedCategoria.createdAt.toISOString(),
        updatedAt: updatedCategoria.updatedAt.toISOString(),
      },
    };

    console.log(`✅ Categoría actualizada: ${updatedCategoria.nombre} por ${req.user!.email}`);
    res.json(response);
  })
);

// DELETE /api/categorias/:id - Eliminar categoría (solo admins)
router.delete('/:id',
  authenticateToken,
  requireRole('ADMIN'),
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;

    // Verificar que la categoría existe
    const categoria = await prisma.categoria.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            noticias: true,
          },
        },
      },
    });

    if (!categoria) {
      throw new NotFoundError('Categoría');
    }

    // Verificar que no tenga noticias asociadas
    if (categoria._count.noticias > 0) {
      throw new ConflictError(
        `No se puede eliminar la categoría porque tiene ${categoria._count.noticias} noticias asociadas`
      );
    }

    // Eliminar categoría
    await prisma.categoria.delete({
      where: { id: parseInt(id) },
    });

    const response: DeleteResponse = {
      success: true,
      data: {
        deleted: true,
        id: parseInt(id),
      },
    };

    console.log(`✅ Categoría eliminada: ${categoria.nombre} por ${req.user!.email}`);
    res.json(response);
  })
);

export default router;
