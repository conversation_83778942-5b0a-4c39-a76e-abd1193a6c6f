import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '@/config/database';
import { 
  authenticateToken, 
  requireRole 
} from '@/middleware/auth';
import { 
  validateSchema, 
  validateQuery,
  validateParams,
  commonSchemas 
} from '@/middleware/validation';
import { 
  as<PERSON><PERSON><PERSON><PERSON>, 
  NotFoundError, 
  ForbiddenError 
} from '@/middleware/error';
import { 
  AuthenticatedRequest 
} from '@/types/auth';
import { 
  PaginatedResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse 
} from '@/types/api';

const router = Router();

// Schemas de validación
const createNoticiaSchema = z.object({
  titulo: commonSchemas.requiredString,
  subtitulo: z.string().optional(),
  volanta: z.string().optional(),
  contenido: commonSchemas.requiredString,
  resumen: z.string().optional(),
  imagenUrl: commonSchemas.optionalUrl,
  imagenAlt: z.string().optional(),
  autor: z.string().optional(),
  fuente: z.string().optional(),
  urlFuente: commonSchemas.optionalUrl,
  categoriaId: z.number().int().positive().optional(),
  destacada: z.boolean().optional().default(false),
  periodista: z.string().optional(),
});

const updateNoticiaSchema = createNoticiaSchema.partial().extend({
  estado: z.enum(['BORRADOR', 'EN_REVISION', 'APROBADA', 'PUBLICADA', 'ARCHIVADA']).optional(),
  publicada: z.boolean().optional(),
  fechaPublicacion: z.string().datetime().optional(),
});

const getNoticiasQuerySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  categoriaId: z.string().optional().transform((val) => val ? parseInt(val) : undefined),
  estado: z.enum(['BORRADOR', 'EN_REVISION', 'APROBADA', 'PUBLICADA', 'ARCHIVADA']).optional(),
  publicada: commonSchemas.booleanFromString,
  destacada: commonSchemas.booleanFromString,
  userId: z.string().optional().transform((val) => val ? parseInt(val) : undefined),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// GET /api/noticias - Obtener noticias
router.get('/',
  authenticateToken,
  validateQuery(getNoticiasQuerySchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { 
      page, 
      limit, 
      search, 
      categoriaId, 
      estado, 
      publicada, 
      destacada, 
      userId, 
      startDate, 
      endDate,
      sortBy, 
      sortOrder 
    } = req.query as any;

    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    // Construir filtros
    const where: any = {};
    
    // Los usuarios normales solo ven sus propias noticias
    if (currentUserRole === 'USER') {
      where.userId = currentUserId;
    } else if (userId) {
      where.userId = userId;
    }
    
    if (search) {
      where.OR = [
        { titulo: { contains: search, mode: 'insensitive' } },
        { volanta: { contains: search, mode: 'insensitive' } },
        { contenido: { contains: search, mode: 'insensitive' } },
        { autor: { contains: search, mode: 'insensitive' } },
        { periodista: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (categoriaId) {
      where.categoriaId = categoriaId;
    }
    
    if (estado) {
      where.estado = estado;
    }
    
    if (typeof publicada === 'boolean') {
      where.publicada = publicada;
    }
    
    if (typeof destacada === 'boolean') {
      where.destacada = destacada;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    // Construir ordenamiento
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }

    // Obtener total de registros
    const totalItems = await prisma.noticia.count({ where });
    const totalPages = Math.ceil(totalItems / limit);

    // Obtener noticias
    const noticias = await prisma.noticia.findMany({
      where,
      include: {
        categoria: {
          select: {
            id: true,
            nombre: true,
            color: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            versiones: true,
            programaciones: true,
            publicacionesExternas: true,
          },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    const response: PaginatedResponse = {
      success: true,
      data: {
        items: noticias.map(noticia => ({
          ...noticia,
          createdAt: noticia.createdAt.toISOString(),
          updatedAt: noticia.updatedAt.toISOString(),
          fechaPublicacion: noticia.fechaPublicacion?.toISOString(),
        })),
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    };

    res.json(response);
  })
);

// GET /api/noticias/:id - Obtener noticia por ID
router.get('/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    const noticia = await prisma.noticia.findUnique({
      where: { id: parseInt(id) },
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        versiones: {
          include: {
            diario: {
              select: {
                id: true,
                nombre: true,
                descripcion: true,
              },
            },
            usuario: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        programaciones: {
          include: {
            diarioExterno: {
              select: {
                id: true,
                nombre: true,
              },
            },
          },
          orderBy: {
            fechaPublicacion: 'desc',
          },
        },
        publicacionesExternas: {
          include: {
            diarioExterno: {
              select: {
                id: true,
                nombre: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!noticia) {
      throw new NotFoundError('Noticia');
    }

    // Los usuarios normales solo pueden ver sus propias noticias
    if (currentUserRole === 'USER' && noticia.userId !== currentUserId) {
      throw new ForbiddenError('No tienes permisos para ver esta noticia');
    }

    res.json({
      success: true,
      data: {
        ...noticia,
        createdAt: noticia.createdAt.toISOString(),
        updatedAt: noticia.updatedAt.toISOString(),
        fechaPublicacion: noticia.fechaPublicacion?.toISOString(),
        versiones: noticia.versiones.map(version => ({
          ...version,
          createdAt: version.createdAt.toISOString(),
          updatedAt: version.updatedAt.toISOString(),
        })),
        programaciones: noticia.programaciones.map(prog => ({
          ...prog,
          fechaPublicacion: prog.fechaPublicacion.toISOString(),
          createdAt: prog.createdAt.toISOString(),
          updatedAt: prog.updatedAt.toISOString(),
          ejecutadoEn: prog.ejecutadoEn?.toISOString(),
        })),
        publicacionesExternas: noticia.publicacionesExternas.map(pub => ({
          ...pub,
          createdAt: pub.createdAt.toISOString(),
          updatedAt: pub.updatedAt.toISOString(),
        })),
      },
    });
  })
);

// POST /api/noticias - Crear noticia
router.post('/',
  authenticateToken,
  validateSchema(createNoticiaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const noticiaData = req.body;
    const userId = req.user!.userId;

    // Verificar que la categoría existe si se proporciona
    if (noticiaData.categoriaId) {
      const categoria = await prisma.categoria.findUnique({
        where: { id: noticiaData.categoriaId },
      });

      if (!categoria || !categoria.isActive) {
        throw new NotFoundError('Categoría');
      }
    }

    // Crear noticia
    const noticia = await prisma.noticia.create({
      data: {
        ...noticiaData,
        userId,
        estado: 'BORRADOR',
        publicada: false,
      },
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const response: CreateResponse = {
      success: true,
      data: {
        ...noticia,
        createdAt: noticia.createdAt.toISOString(),
        updatedAt: noticia.updatedAt.toISOString(),
        fechaPublicacion: noticia.fechaPublicacion?.toISOString(),
      },
    };

    console.log(`✅ Noticia creada: "${noticia.titulo}" por ${req.user!.email}`);
    res.status(201).json(response);
  })
);

// PUT /api/noticias/:id - Actualizar noticia
router.put('/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  validateSchema(updateNoticiaSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    // Verificar que la noticia existe
    const existingNoticia = await prisma.noticia.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingNoticia) {
      throw new NotFoundError('Noticia');
    }

    // Los usuarios normales solo pueden editar sus propias noticias
    if (currentUserRole === 'USER' && existingNoticia.userId !== currentUserId) {
      throw new ForbiddenError('No tienes permisos para editar esta noticia');
    }

    // Solo editores y admins pueden cambiar el estado
    if (updateData.estado && currentUserRole === 'USER') {
      delete updateData.estado;
    }

    // Solo editores y admins pueden publicar
    if (updateData.publicada !== undefined && currentUserRole === 'USER') {
      delete updateData.publicada;
    }

    // Verificar que la categoría existe si se proporciona
    if (updateData.categoriaId) {
      const categoria = await prisma.categoria.findUnique({
        where: { id: updateData.categoriaId },
      });

      if (!categoria || !categoria.isActive) {
        throw new NotFoundError('Categoría');
      }
    }

    // Si se está publicando, establecer fecha de publicación
    if (updateData.publicada === true && !existingNoticia.publicada) {
      updateData.fechaPublicacion = new Date().toISOString();
      updateData.estado = 'PUBLICADA';
    }

    // Actualizar noticia
    const updatedNoticia = await prisma.noticia.update({
      where: { id: parseInt(id) },
      data: {
        ...updateData,
        fechaPublicacion: updateData.fechaPublicacion ? new Date(updateData.fechaPublicacion) : undefined,
      },
      include: {
        categoria: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const response: UpdateResponse = {
      success: true,
      data: {
        ...updatedNoticia,
        createdAt: updatedNoticia.createdAt.toISOString(),
        updatedAt: updatedNoticia.updatedAt.toISOString(),
        fechaPublicacion: updatedNoticia.fechaPublicacion?.toISOString(),
      },
    };

    console.log(`✅ Noticia actualizada: "${updatedNoticia.titulo}" por ${req.user!.email}`);
    res.json(response);
  })
);

// DELETE /api/noticias/:id - Eliminar noticia
router.delete('/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    // Verificar que la noticia existe
    const noticia = await prisma.noticia.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            versiones: true,
            programaciones: true,
            publicacionesExternas: true,
          },
        },
      },
    });

    if (!noticia) {
      throw new NotFoundError('Noticia');
    }

    // Los usuarios normales solo pueden eliminar sus propias noticias
    if (currentUserRole === 'USER' && noticia.userId !== currentUserId) {
      throw new ForbiddenError('No tienes permisos para eliminar esta noticia');
    }

    // Solo admins pueden eliminar noticias publicadas
    if (noticia.publicada && currentUserRole !== 'ADMIN') {
      throw new ForbiddenError('Solo los administradores pueden eliminar noticias publicadas');
    }

    // Eliminar noticia (las relaciones se eliminan en cascada)
    await prisma.noticia.delete({
      where: { id: parseInt(id) },
    });

    const response: DeleteResponse = {
      success: true,
      data: {
        deleted: true,
        id: parseInt(id),
      },
    };

    console.log(`✅ Noticia eliminada: "${noticia.titulo}" por ${req.user!.email}`);
    res.json(response);
  })
);

// GET /api/noticias/:id/stats - Obtener estadísticas de una noticia
router.get('/:id/stats',
  authenticateToken,
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    const noticia = await prisma.noticia.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            versiones: true,
            programaciones: true,
            publicacionesExternas: true,
            generacionesImagen: true,
          },
        },
      },
    });

    if (!noticia) {
      throw new NotFoundError('Noticia');
    }

    // Los usuarios normales solo pueden ver stats de sus propias noticias
    if (currentUserRole === 'USER' && noticia.userId !== currentUserId) {
      throw new ForbiddenError('No tienes permisos para ver las estadísticas de esta noticia');
    }

    res.json({
      success: true,
      data: {
        noticiaId: noticia.id,
        titulo: noticia.titulo,
        estado: noticia.estado,
        publicada: noticia.publicada,
        destacada: noticia.destacada,
        versiones: noticia._count.versiones,
        programaciones: noticia._count.programaciones,
        publicacionesExternas: noticia._count.publicacionesExternas,
        generacionesImagen: noticia._count.generacionesImagen,
        createdAt: noticia.createdAt.toISOString(),
        updatedAt: noticia.updatedAt.toISOString(),
      },
    });
  })
);

export default router;
