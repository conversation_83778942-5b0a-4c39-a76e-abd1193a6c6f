import { Router } from 'express';
import bcrypt from 'bcrypt';
import { z } from 'zod';
import { prisma } from '@/config/database';
import { config } from '@/config/environment';
import { 
  authenticateToken, 
  requireRole 
} from '@/middleware/auth';
import { 
  validateSchema, 
  validateQuery,
  validateParams,
  commonSchemas 
} from '@/middleware/validation';
import { 
  async<PERSON><PERSON>ler, 
  NotFoundError, 
  ConflictError 
} from '@/middleware/error';
import { 
  AuthenticatedRequest,
  CreateUserRequest,
  UpdateUserRequest 
} from '@/types/auth';
import { 
  PaginatedResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse 
} from '@/types/api';

const router = Router();

// Schemas de validación
const createUserSchema = z.object({
  email: commonSchemas.email,
  name: commonSchemas.requiredString,
  password: commonSchemas.password,
  role: z.enum(['USER', 'EDITOR', 'ADMIN']).optional().default('USER'),
  isActive: z.boolean().optional().default(true),
});

const updateUserSchema = z.object({
  email: commonSchemas.email.optional(),
  name: z.string().min(1).optional(),
  role: z.enum(['USER', 'EDITOR', 'ADMIN']).optional(),
  isActive: z.boolean().optional(),
});

const getUsersQuerySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  role: z.enum(['USER', 'EDITOR', 'ADMIN']).optional(),
  isActive: commonSchemas.booleanFromString,
});

// GET /api/users - Obtener usuarios (solo admins)
router.get('/',
  authenticateToken,
  requireRole('ADMIN'),
  validateQuery(getUsersQuerySchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { page, limit, search, role, isActive, sortBy, sortOrder } = req.query as any;

    // Construir filtros
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    if (role) {
      where.role = role;
    }
    
    if (typeof isActive === 'boolean') {
      where.isActive = isActive;
    }

    // Construir ordenamiento
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }

    // Obtener total de registros
    const totalItems = await prisma.user.count({ where });
    const totalPages = Math.ceil(totalItems / limit);

    // Obtener usuarios
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            noticias: true,
            correcciones: true,
          },
        },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    const response: PaginatedResponse = {
      success: true,
      data: {
        items: users.map(user => ({
          ...user,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
        })),
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
      },
    };

    res.json(response);
  })
);

// GET /api/users/:id - Obtener usuario por ID
router.get('/:id',
  authenticateToken,
  requireRole(['ADMIN', 'EDITOR']),
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;

    // Los usuarios normales solo pueden ver su propio perfil
    if (currentUserRole === 'USER' && parseInt(id) !== currentUserId) {
      throw new NotFoundError('Usuario');
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            noticias: true,
            correcciones: true,
            versionesGeneradas: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundError('Usuario');
    }

    res.json({
      success: true,
      data: {
        ...user,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      },
    });
  })
);

// POST /api/users - Crear usuario (solo admins)
router.post('/',
  authenticateToken,
  requireRole('ADMIN'),
  validateSchema(createUserSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { email, name, password, role, isActive }: CreateUserRequest = req.body;

    // Verificar que el email no exista
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      throw new ConflictError('Ya existe un usuario con este email');
    }

    // Hash de la contraseña
    const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

    // Crear usuario
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        name,
        password: hashedPassword,
        role: role || 'USER',
        isActive: isActive !== undefined ? isActive : true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const response: CreateResponse = {
      success: true,
      data: {
        ...user,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      },
    };

    console.log(`✅ Usuario creado: ${user.email} por ${req.user!.email}`);
    res.status(201).json(response);
  })
);

// PUT /api/users/:id - Actualizar usuario
router.put('/:id',
  authenticateToken,
  validateParams(commonSchemas.id),
  validateSchema(updateUserSchema),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;
    const currentUserRole = req.user!.role;
    const updateData: UpdateUserRequest = req.body;

    // Los usuarios normales solo pueden actualizar su propio perfil
    if (currentUserRole === 'USER' && parseInt(id) !== currentUserId) {
      throw new NotFoundError('Usuario');
    }

    // Solo admins pueden cambiar roles
    if (updateData.role && currentUserRole !== 'ADMIN') {
      delete updateData.role;
    }

    // Solo admins pueden cambiar el estado activo
    if (updateData.isActive !== undefined && currentUserRole !== 'ADMIN') {
      delete updateData.isActive;
    }

    // Verificar que el usuario existe
    const existingUser = await prisma.user.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingUser) {
      throw new NotFoundError('Usuario');
    }

    // Si se está actualizando el email, verificar que no exista
    if (updateData.email && updateData.email.toLowerCase() !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: updateData.email.toLowerCase() },
      });

      if (emailExists) {
        throw new ConflictError('Ya existe un usuario con este email');
      }
    }

    // Actualizar usuario
    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        ...updateData,
        email: updateData.email?.toLowerCase(),
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const response: UpdateResponse = {
      success: true,
      data: {
        ...updatedUser,
        createdAt: updatedUser.createdAt.toISOString(),
        updatedAt: updatedUser.updatedAt.toISOString(),
      },
    };

    console.log(`✅ Usuario actualizado: ${updatedUser.email} por ${req.user!.email}`);
    res.json(response);
  })
);

// DELETE /api/users/:id - Eliminar usuario (solo admins)
router.delete('/:id',
  authenticateToken,
  requireRole('ADMIN'),
  validateParams(commonSchemas.id),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { id } = req.params;
    const currentUserId = req.user!.userId;

    // No permitir que un admin se elimine a sí mismo
    if (parseInt(id) === currentUserId) {
      throw new ConflictError('No puedes eliminar tu propia cuenta');
    }

    // Verificar que el usuario existe
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
    });

    if (!user) {
      throw new NotFoundError('Usuario');
    }

    // Eliminar usuario
    await prisma.user.delete({
      where: { id: parseInt(id) },
    });

    const response: DeleteResponse = {
      success: true,
      data: {
        deleted: true,
        id: parseInt(id),
      },
    };

    console.log(`✅ Usuario eliminado: ${user.email} por ${req.user!.email}`);
    res.json(response);
  })
);

export default router;
