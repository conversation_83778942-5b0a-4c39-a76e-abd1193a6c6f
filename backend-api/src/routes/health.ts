import { Router } from 'express';
import { getDatabaseStats } from '@/config/database';
import { config } from '@/config/environment';
import { HealthCheckResponse } from '@/types/api';
import { asyncHandler } from '@/middleware/error';

const router = Router();

// GET /health - Health check básico
router.get('/', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  try {
    // Verificar base de datos
    const dbStats = await getDatabaseStats();
    const dbResponseTime = Date.now() - startTime;

    const healthData: HealthCheckResponse = {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        environment: config.server.nodeEnv,
        database: {
          status: 'connected',
          responseTime: dbResponseTime,
        },
        externalServices: {
          // Aquí se pueden agregar checks de servicios externos
        },
      },
    };

    res.json(healthData);
  } catch (error) {
    const healthData: HealthCheckResponse = {
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        environment: config.server.nodeEnv,
        database: {
          status: 'disconnected',
        },
        externalServices: {},
      },
      error: 'Database connection failed',
    };

    res.status(503).json(healthData);
  }
}));

// GET /health/detailed - Health check detallado
router.get('/detailed', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  try {
    // Verificar base de datos y obtener estadísticas
    const dbStats = await getDatabaseStats();
    const dbResponseTime = Date.now() - startTime;

    // Verificar memoria
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    const healthData: HealthCheckResponse = {
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        environment: config.server.nodeEnv,
        database: {
          status: 'connected',
          responseTime: dbResponseTime,
        },
        externalServices: {},
      },
    };

    // Agregar información adicional
    (healthData.data as any).system = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: memoryUsageMB,
      pid: process.pid,
    };

    (healthData.data as any).database.stats = dbStats;

    res.json(healthData);
  } catch (error) {
    const healthData: HealthCheckResponse = {
      success: false,
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        environment: config.server.nodeEnv,
        database: {
          status: 'disconnected',
        },
        externalServices: {},
      },
      error: error instanceof Error ? error.message : 'Unknown error',
    };

    res.status(503).json(healthData);
  }
}));

// GET /health/ready - Readiness probe
router.get('/ready', asyncHandler(async (req, res) => {
  try {
    // Verificar que todos los servicios críticos estén listos
    await getDatabaseStats();
    
    res.json({
      success: true,
      ready: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      ready: false,
      error: 'Service not ready',
      timestamp: new Date().toISOString(),
    });
  }
}));

// GET /health/live - Liveness probe
router.get('/live', (req, res) => {
  res.json({
    success: true,
    alive: true,
    timestamp: new Date().toISOString(),
  });
});

export default router;
