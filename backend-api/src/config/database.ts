import { PrismaClient } from '@prisma/client';

declare global {
  var __prisma: PrismaClient | undefined;
}

// Configuración del cliente Prisma con optimizaciones
const prismaConfig = {
  log: process.env.NODE_ENV === 'development' 
    ? ['query', 'info', 'warn', 'error'] as const
    : ['error'] as const,
  
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
};

// Singleton pattern para evitar múltiples conexiones
export const prisma = globalThis.__prisma || new PrismaClient(prismaConfig);

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Manejo de cierre de conexión
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

// Función para verificar la conexión a la base de datos
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Función para obtener estadísticas de la base de datos
export async function getDatabaseStats() {
  try {
    const [
      userCount,
      noticiaCount,
      categoriaCount,
      diarioCount,
      versionCount,
      programacionCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.noticia.count(),
      prisma.categoria.count(),
      prisma.diario.count(),
      prisma.versionNoticia.count(),
      prisma.programacionPublicacion.count()
    ]);

    return {
      users: userCount,
      noticias: noticiaCount,
      categorias: categoriaCount,
      diarios: diarioCount,
      versiones: versionCount,
      programaciones: programacionCount,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    throw error;
  }
}

export default prisma;
