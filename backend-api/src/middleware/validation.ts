import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema, ZodError } from 'zod';
import { ApiResponse, ValidationError } from '@/types/api';

// Middleware genérico para validación con Zod
export const validateSchema = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Validar el body de la request
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          value: err.code === 'invalid_type' ? undefined : err.input,
        }));

        res.status(400).json({
          success: false,
          error: 'Datos de entrada inválidos',
          code: 'VALIDATION_ERROR',
          details: validationErrors,
        } as ApiResponse);
        return;
      }

      // Error inesperado
      console.error('Validation error:', error);
      res.status(500).json({
        success: false,
        error: 'Error interno del servidor',
        code: 'INTERNAL_ERROR',
      } as ApiResponse);
    }
  };
};

// Middleware para validar parámetros de query
export const validateQuery = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      schema.parse(req.query);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          value: err.input,
        }));

        res.status(400).json({
          success: false,
          error: 'Parámetros de consulta inválidos',
          code: 'QUERY_VALIDATION_ERROR',
          details: validationErrors,
        } as ApiResponse);
        return;
      }

      console.error('Query validation error:', error);
      res.status(500).json({
        success: false,
        error: 'Error interno del servidor',
        code: 'INTERNAL_ERROR',
      } as ApiResponse);
    }
  };
};

// Middleware para validar parámetros de ruta
export const validateParams = (schema: ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      schema.parse(req.params);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const validationErrors: ValidationError[] = error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          value: err.input,
        }));

        res.status(400).json({
          success: false,
          error: 'Parámetros de ruta inválidos',
          code: 'PARAMS_VALIDATION_ERROR',
          details: validationErrors,
        } as ApiResponse);
        return;
      }

      console.error('Params validation error:', error);
      res.status(500).json({
        success: false,
        error: 'Error interno del servidor',
        code: 'INTERNAL_ERROR',
      } as ApiResponse);
    }
  };
};

// Schemas comunes de validación
export const commonSchemas = {
  // ID numérico
  id: z.object({
    id: z.string().transform((val) => {
      const num = parseInt(val, 10);
      if (isNaN(num) || num <= 0) {
        throw new Error('ID debe ser un número positivo');
      }
      return num;
    }),
  }),

  // Paginación
  pagination: z.object({
    page: z.string().optional().transform((val) => {
      if (!val) return 1;
      const num = parseInt(val, 10);
      return isNaN(num) || num < 1 ? 1 : num;
    }),
    limit: z.string().optional().transform((val) => {
      if (!val) return 10;
      const num = parseInt(val, 10);
      return isNaN(num) || num < 1 ? 10 : Math.min(num, 100);
    }),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  }),

  // Búsqueda
  search: z.object({
    q: z.string().optional(),
    search: z.string().optional(),
  }),

  // Fechas
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }),

  // Email
  email: z.string().email('Email inválido'),

  // Password
  password: z.string()
    .min(8, 'La contraseña debe tener al menos 8 caracteres')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
      'La contraseña debe contener al menos una mayúscula, una minúscula y un número'),

  // Texto requerido
  requiredString: z.string().min(1, 'Este campo es requerido'),

  // URL opcional
  optionalUrl: z.string().url('URL inválida').optional().or(z.literal('')),

  // Boolean desde string
  booleanFromString: z.string().optional().transform((val) => {
    if (!val) return undefined;
    return val === 'true' || val === '1';
  }),
};

// Función helper para crear schemas de actualización (campos opcionales)
export const createUpdateSchema = <T extends ZodSchema>(schema: T) => {
  return schema.partial();
};

// Función helper para sanitizar entrada
export const sanitizeInput = (input: any): any => {
  if (typeof input === 'string') {
    return input.trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
};

// Middleware para sanitizar entrada
export const sanitizeRequest = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (req.body) {
      req.body = sanitizeInput(req.body);
    }
    if (req.query) {
      req.query = sanitizeInput(req.query);
    }
    if (req.params) {
      req.params = sanitizeInput(req.params);
    }
    next();
  } catch (error) {
    console.error('Sanitization error:', error);
    res.status(500).json({
      success: false,
      error: 'Error procesando la solicitud',
      code: 'SANITIZATION_ERROR',
    } as ApiResponse);
  }
};

export default {
  validateSchema,
  validateQuery,
  validateParams,
  commonSchemas,
  createUpdateSchema,
  sanitizeInput,
  sanitizeRequest,
};
