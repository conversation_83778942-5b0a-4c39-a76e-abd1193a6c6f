import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config/environment';
import { AuthenticatedRequest, JWTPayload, UserRole } from '@/types/auth';
import { ApiResponse } from '@/types/api';

// Middleware para verificar JWT token
export const authenticateToken = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Token de acceso requerido',
        code: 'MISSING_TOKEN'
      } as ApiResponse);
      return;
    }

    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (err) {
        let errorMessage = 'Token inválido';
        let errorCode = 'INVALID_TOKEN';

        if (err.name === 'TokenExpiredError') {
          errorMessage = 'Token expirado';
          errorCode = 'EXPIRED_TOKEN';
        } else if (err.name === 'JsonWebTokenError') {
          errorMessage = 'Token malformado';
          errorCode = 'MALFORMED_TOKEN';
        }

        res.status(403).json({
          success: false,
          error: errorMessage,
          code: errorCode
        } as ApiResponse);
        return;
      }

      req.user = decoded as JWTPayload;
      next();
    });
  } catch (error) {
    console.error('Error in authenticateToken middleware:', error);
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor',
      code: 'INTERNAL_ERROR'
    } as ApiResponse);
  }
};

// Middleware para verificar roles específicos
export const requireRole = (allowedRoles: UserRole | UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Usuario no autenticado',
          code: 'NOT_AUTHENTICATED'
        } as ApiResponse);
        return;
      }

      const userRole = req.user.role as UserRole;
      const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

      if (!roles.includes(userRole)) {
        res.status(403).json({
          success: false,
          error: 'Permisos insuficientes',
          code: 'INSUFFICIENT_PERMISSIONS',
          details: {
            required: roles,
            current: userRole
          }
        } as ApiResponse);
        return;
      }

      next();
    } catch (error) {
      console.error('Error in requireRole middleware:', error);
      res.status(500).json({
        success: false,
        error: 'Error interno del servidor',
        code: 'INTERNAL_ERROR'
      } as ApiResponse);
    }
  };
};

// Middleware para verificar que el usuario esté activo
export const requireActiveUser = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Usuario no autenticado',
        code: 'NOT_AUTHENTICATED'
      } as ApiResponse);
      return;
    }

    // Aquí podrías verificar en la base de datos si el usuario sigue activo
    // Por ahora, asumimos que si el token es válido, el usuario está activo
    next();
  } catch (error) {
    console.error('Error in requireActiveUser middleware:', error);
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor',
      code: 'INTERNAL_ERROR'
    } as ApiResponse);
  }
};

// Middleware opcional de autenticación (no falla si no hay token)
export const optionalAuth = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // No hay token, continuar sin usuario
      next();
      return;
    }

    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (!err && decoded) {
        req.user = decoded as JWTPayload;
      }
      // Continuar independientemente del resultado
      next();
    });
  } catch (error) {
    console.error('Error in optionalAuth middleware:', error);
    // Continuar sin usuario en caso de error
    next();
  }
};

// Función helper para generar tokens
export const generateTokens = (payload: Omit<JWTPayload, 'iat' | 'exp'>) => {
  const accessToken = jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });

  const refreshToken = jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });

  return { accessToken, refreshToken };
};

// Función helper para verificar permisos
export const hasPermission = (userRole: UserRole, requiredRole: UserRole): boolean => {
  const roleHierarchy: Record<UserRole, number> = {
    'USER': 1,
    'EDITOR': 2,
    'ADMIN': 3,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};

export default {
  authenticateToken,
  requireRole,
  requireActiveUser,
  optionalAuth,
  generateTokens,
  hasPermission,
};
