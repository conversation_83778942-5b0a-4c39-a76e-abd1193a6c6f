import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { config } from '@/config/environment';
import { ApiResponse } from '@/types/api';

// Rate limiting middleware
export const createRateLimiter = (options?: {
  windowMs?: number;
  max?: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options?.windowMs || config.rateLimit.windowMs,
    max: options?.max || config.rateLimit.max,
    message: {
      success: false,
      error: options?.message || 'Demasiadas solicitudes, intenta más tarde',
      code: 'RATE_LIMIT_EXCEEDED',
    } as ApiResponse,
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options?.skipSuccessfulRequests || false,
  });
};

// Rate limiter específico para autenticación
export const authRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // 5 intentos por IP
  message: 'Demasiados intentos de login, intenta en 15 minutos',
  skipSuccessfulRequests: true,
});

// Rate limiter para APIs generales
export const apiRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // 100 requests por IP
});

// Rate limiter para uploads
export const uploadRateLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minuto
  max: 10, // 10 uploads por minuto
  message: 'Demasiadas subidas de archivos, intenta más tarde',
});

// Configuración de Helmet para seguridad
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Deshabilitado para compatibilidad
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
});

// Middleware para validar Content-Type en requests POST/PUT
export const validateContentType = (allowedTypes: string[] = ['application/json']) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.get('Content-Type');
      
      if (!contentType) {
        res.status(400).json({
          success: false,
          error: 'Content-Type header requerido',
          code: 'MISSING_CONTENT_TYPE',
        } as ApiResponse);
        return;
      }

      const isValidType = allowedTypes.some(type => 
        contentType.toLowerCase().includes(type.toLowerCase())
      );

      if (!isValidType) {
        res.status(415).json({
          success: false,
          error: `Content-Type no soportado. Tipos permitidos: ${allowedTypes.join(', ')}`,
          code: 'UNSUPPORTED_MEDIA_TYPE',
        } as ApiResponse);
        return;
      }
    }

    next();
  };
};

// Middleware para validar tamaño del body
export const validateBodySize = (maxSize: number = 10 * 1024 * 1024) => { // 10MB por defecto
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      res.status(413).json({
        success: false,
        error: `Payload demasiado grande. Máximo permitido: ${maxSize} bytes`,
        code: 'PAYLOAD_TOO_LARGE',
      } as ApiResponse);
      return;
    }

    next();
  };
};

// Middleware para sanitizar headers peligrosos
export const sanitizeHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Remover headers potencialmente peligrosos
  delete req.headers['x-forwarded-host'];
  delete req.headers['x-forwarded-server'];
  
  // Validar User-Agent
  const userAgent = req.get('User-Agent');
  if (!userAgent || userAgent.length > 500) {
    req.headers['user-agent'] = 'Unknown';
  }

  next();
};

// Middleware para logging de seguridad
export const securityLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Log de requests sospechosos
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /<script/i,  // XSS
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
  ];

  const url = req.url;
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(url));

  if (isSuspicious) {
    console.warn('🚨 Suspicious request detected:', {
      ip: req.ip,
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
    });
  }

  // Log de requests completados
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (config.server.isDevelopment) {
      console.log(`${req.method} ${req.url} - ${res.statusCode} (${duration}ms)`);
    }
  });

  next();
};

// Middleware para prevenir ataques de timing
export const preventTimingAttacks = (req: Request, res: Response, next: NextFunction): void => {
  // Agregar un pequeño delay aleatorio para prevenir timing attacks
  const delay = Math.random() * 10; // 0-10ms
  
  setTimeout(() => {
    next();
  }, delay);
};

// Middleware para validar origen de requests
export const validateOrigin = (allowedOrigins: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const origin = req.get('Origin');
    const referer = req.get('Referer');

    // Para requests sin origen (como Postman), permitir en desarrollo
    if (!origin && !referer && config.server.isDevelopment) {
      next();
      return;
    }

    // Validar origen
    if (origin && !allowedOrigins.includes(origin)) {
      res.status(403).json({
        success: false,
        error: 'Origen no permitido',
        code: 'FORBIDDEN_ORIGIN',
      } as ApiResponse);
      return;
    }

    next();
  };
};

export default {
  createRateLimiter,
  authRateLimiter,
  apiRateLimiter,
  uploadRateLimiter,
  securityHeaders,
  validateContentType,
  validateBodySize,
  sanitizeHeaders,
  securityLogger,
  preventTimingAttacks,
  validateOrigin,
};
