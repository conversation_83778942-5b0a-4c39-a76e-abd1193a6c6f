{"name": "panel-unificado-backend-api", "version": "1.0.0", "description": "Backend API independiente para Panel Unificado V2", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "keywords": ["api", "backend", "express", "typescript", "prisma"], "author": "Panel Unificado Team", "license": "MIT", "dependencies": {"@prisma/client": "^6.10.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "bcrypt": "^6.0.0", "zod": "^3.25.67", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "sharp": "^0.34.3", "cloudinary": "^2.7.0", "openai": "^5.10.1", "@google/generative-ai": "^0.24.1", "node-fetch": "^3.3.2", "form-data": "^4.0.4", "uuid": "^11.1.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "@types/multer": "^1.4.11", "@types/uuid": "^10.0.0", "@types/node": "^20.19.4", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "prisma": "^6.10.1"}, "engines": {"node": ">=18.0.0"}}