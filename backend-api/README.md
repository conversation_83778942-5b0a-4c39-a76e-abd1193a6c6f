# Panel Unificado Backend API

Backend API independiente para el sistema Panel Unificado V2. Esta API proporciona todos los servicios de backend necesarios para la aplicación frontend.

## 🚀 Características

- **API REST** completa con Express.js y TypeScript
- **Autenticación JWT** con refresh tokens
- **Base de datos PostgreSQL** con Prisma ORM
- **Validación robusta** con Zod
- **Seguridad avanzada** con Helmet, CORS y rate limiting
- **Subida de archivos** con optimización de imágenes
- **Servicios de IA** integrados (OpenAI, Google Gemini)
- **Sistema de webhooks** para integración externa
- **Logging y monitoreo** completo
- **Documentación automática** de APIs

## 📋 Requisitos

- Node.js >= 18.0.0
- PostgreSQL >= 13
- npm o yarn

## 🛠️ Instalación

1. **Clonar el repositorio**
```bash
git clone <repository-url>
cd backend-api
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
# Editar .env con tus configuraciones
```

4. **Configurar base de datos**
```bash
# Generar cliente Prisma
npm run db:generate

# Ejecutar migraciones
npm run db:migrate

# Opcional: Ejecutar seeds
npm run db:seed
```

5. **Iniciar en desarrollo**
```bash
npm run dev
```

## 🔧 Variables de Entorno

### Configuración del Servidor
```env
NODE_ENV=development
PORT=3001
API_BASE_URL=http://localhost:3001
```

### Base de Datos
```env
DATABASE_URL="postgresql://username:password@localhost:5432/panel_unificado"
```

### JWT
```env
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
```

### APIs Externas
```env
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

## 📡 Endpoints de la API

### Autenticación
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/refresh` - Renovar token
- `GET /api/auth/me` - Información del usuario actual
- `POST /api/auth/change-password` - Cambiar contraseña
- `POST /api/auth/logout` - Cerrar sesión

### Usuarios
- `GET /api/users` - Listar usuarios (Admin)
- `GET /api/users/:id` - Obtener usuario por ID
- `POST /api/users` - Crear usuario (Admin)
- `PUT /api/users/:id` - Actualizar usuario
- `DELETE /api/users/:id` - Eliminar usuario (Admin)

### Noticias
- `GET /api/noticias` - Listar noticias
- `GET /api/noticias/:id` - Obtener noticia por ID
- `POST /api/noticias` - Crear noticia
- `PUT /api/noticias/:id` - Actualizar noticia
- `DELETE /api/noticias/:id` - Eliminar noticia
- `GET /api/noticias/:id/stats` - Estadísticas de noticia

### Categorías
- `GET /api/categorias` - Listar categorías
- `GET /api/categorias/active` - Categorías activas
- `GET /api/categorias/:id` - Obtener categoría por ID
- `POST /api/categorias` - Crear categoría
- `PUT /api/categorias/:id` - Actualizar categoría
- `DELETE /api/categorias/:id` - Eliminar categoría

### Inteligencia Artificial
- `POST /api/ai/rewrite` - Reescribir noticia con IA
- `POST /api/ai/generate-image` - Generar imagen con IA
- `GET /api/ai/image-generation/:id` - Estado de generación
- `GET /api/ai/models` - Modelos disponibles

### Subida de Archivos
- `POST /api/upload/image` - Subir imagen
- `POST /api/upload/images` - Subir múltiples imágenes
- `DELETE /api/upload/image/:filename` - Eliminar imagen

### Webhooks
- `POST /api/webhooks/noticia` - Recibir noticia desde webhook
- `GET /api/webhooks/noticia` - Información del webhook
- `POST /api/webhooks/test` - Webhook de prueba
- `GET /api/webhooks/stats` - Estadísticas de webhooks

### Health Check
- `GET /health` - Health check básico
- `GET /health/detailed` - Health check detallado
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

## 🔐 Autenticación

La API utiliza JWT (JSON Web Tokens) para autenticación:

1. **Login**: Envía credenciales a `/api/auth/login`
2. **Token**: Recibe `accessToken` y `refreshToken`
3. **Autorización**: Incluye `Authorization: Bearer <token>` en headers
4. **Refresh**: Usa `/api/auth/refresh` cuando el token expire

### Ejemplo de uso:
```javascript
// Login
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { data } = await response.json();
const { token, refreshToken } = data;

// Usar token en requests
const apiResponse = await fetch('/api/noticias', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## 🛡️ Seguridad

- **Rate Limiting**: Límites por IP y endpoint
- **CORS**: Configurado para orígenes permitidos
- **Helmet**: Headers de seguridad
- **Validación**: Sanitización de entrada con Zod
- **Hashing**: Contraseñas con bcrypt
- **JWT**: Tokens seguros con expiración

## 📊 Monitoreo

### Health Checks
- `/health` - Estado básico del servicio
- `/health/detailed` - Información detallada del sistema
- `/health/ready` - Para readiness probes de Kubernetes
- `/health/live` - Para liveness probes de Kubernetes

### Logging
- Logs estructurados con niveles
- Logs de seguridad para requests sospechosos
- Logs de errores con stack traces (solo en desarrollo)

## 🚀 Despliegue

### Docker
```bash
# Construir imagen
docker build -t panel-backend-api .

# Ejecutar contenedor
docker run -p 3001:3001 --env-file .env panel-backend-api
```

### Docker Compose
```yaml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=******************************/panel_unificado
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: panel_unificado
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🧪 Testing

```bash
# Ejecutar tests
npm test

# Tests en modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

## 📝 Scripts Disponibles

- `npm run dev` - Desarrollo con hot reload
- `npm run build` - Construir para producción
- `npm start` - Ejecutar en producción
- `npm run lint` - Linter
- `npm run lint:fix` - Corregir errores de linting
- `npm run db:generate` - Generar cliente Prisma
- `npm run db:push` - Push schema a DB
- `npm run db:migrate` - Ejecutar migraciones
- `npm run db:studio` - Abrir Prisma Studio
- `npm run db:seed` - Ejecutar seeds

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

Si tienes problemas o preguntas:

1. Revisa la documentación
2. Busca en los issues existentes
3. Crea un nuevo issue con detalles del problema
4. Incluye logs relevantes y pasos para reproducir

---

**Desarrollado con ❤️ para Panel Unificado V2**
