generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   Int                @id @default(autoincrement())
  email                String             @unique
  name                 String
  password             String
  role                 Role               @default(USER)
  isActive             Boolean            @default(true)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  auditLogs            AuditLog[]
  boardMemberships     BoardMember[]
  createdBoards        Board[]            @relation("BoardCreator")
  correcciones         Correccion[]
  generacionesImagen   GeneracionImagen[]
  limitesUsoIA         LimiteUsoIA[]
  noticias             Noticia[]
  notificaciones       Notificacion[]
  systemConfigsCreated SystemConfig[]     @relation("SystemConfigCreator")
  systemConfigsUpdated SystemConfig[]     @relation("SystemConfigUpdater")
  versionesGeneradas   VersionNoticia[]

  @@index([email])
  @@index([isActive])
  @@index([role])
  @@index([createdAt])
  @@map("users")
}

model Categoria {
  id              Int              @id @default(autoincrement())
  nombre          String           @unique
  descripcion     String?
  color           String           @default("#3B82F6")
  isActive        Boolean          @default(true)
  orden           Int              @default(1)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  categoriaMapeos CategoriaMapeo[]
  noticias        Noticia[]

  @@map("categorias")
}

model Noticia {
  id                    Int                       @id @default(autoincrement())
  titulo                String
  subtitulo             String?
  volanta               String?
  contenido             String
  resumen               String?
  imagenUrl             String?
  imagenAlt             String?
  autor                 String?
  fuente                String?
  urlFuente             String?
  estado                EstadoNoticia             @default(BORRADOR)
  destacada             Boolean                   @default(false)
  publicada             Boolean                   @default(false)
  fechaPublicacion      DateTime?
  periodista            String?
  origen                OrigenNoticia             @default(PANEL)
  webhookData           String?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  categoriaId           Int?
  userId                Int
  generacionesImagen    GeneracionImagen[]
  categoria             Categoria?                @relation(fields: [categoriaId], references: [id])
  user                  User                      @relation(fields: [userId], references: [id])
  notificaciones        Notificacion[]
  programaciones        ProgramacionPublicacion[]
  publicacionesExternas PublicacionExterna[]
  versiones             VersionNoticia[]

  @@index([estado])
  @@index([publicada])
  @@index([userId])
  @@index([categoriaId])
  @@index([origen])
  @@index([createdAt])
  @@index([fechaPublicacion])
  @@map("noticias")
}

model Notificacion {
  id        Int              @id @default(autoincrement())
  tipo      TipoNotificacion @default(WEBHOOK_NOTICIA)
  titulo    String
  mensaje   String?
  leida     Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  noticiaId Int?
  userId    Int
  metadata  String?
  noticia   Noticia?         @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notificaciones")
}

model Correccion {
  id               Int       @id @default(autoincrement())
  titulo           String
  contenido        String
  medio            String
  fechaPublicacion DateTime
  fechaCorreccion  DateTime
  estado           Estado    @default(PENDIENTE)
  prioridad        Prioridad @default(MEDIA)
  imagenUrl        String?
  observaciones    String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  userId           Int
  user             User      @relation(fields: [userId], references: [id])

  @@map("correcciones")
}

model Board {
  id          Int           @id @default(autoincrement())
  name        String
  description String?
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  createdById Int
  members     BoardMember[]
  createdBy   User          @relation("BoardCreator", fields: [createdById], references: [id])

  @@map("boards")
}

model BoardMember {
  id        Int       @id @default(autoincrement())
  role      BoardRole @default(MEMBER)
  joinedAt  DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  userId    Int
  boardId   Int
  board     Board     @relation(fields: [boardId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, boardId])
  @@map("board_members")
}

model Diario {
  id              Int                 @id @default(autoincrement())
  nombre          String              @unique
  descripcion     String?
  prompt          String              @default("Reescribe la siguiente noticia manteniendo la información principal pero adaptando el estilo y tono:")
  isActive        Boolean             @default(true)
  aiProvider      AIProvider          @default(OPENAI)
  aiModel         String?
  useGlobalConfig Boolean             @default(true)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  socialConfig    DiarioSocialConfig?
  versiones       VersionNoticia[]

  @@map("diarios")
}

model VersionNoticia {
  id                Int                       @id @default(autoincrement())
  titulo            String
  subtitulo         String?
  volanta           String?
  contenido         String
  resumen           String?
  imagenUrl         String?
  estado            EstadoVersion             @default(GENERADA)
  estadoPublicacion EstadoPublicacionVersion? @default(PENDIENTE)
  urlPublicacion    String?
  promptUsado       String?
  metadatos         String?
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
  noticiaId         Int
  diarioId          Int
  generadaPor       Int
  programaciones    ProgramacionPublicacion[]
  diario            Diario                    @relation(fields: [diarioId], references: [id])
  usuario           User                      @relation(fields: [generadaPor], references: [id])
  noticia           Noticia                   @relation(fields: [noticiaId], references: [id], onDelete: Cascade)

  @@map("versiones_noticias")
}

model AIConfig {
  id                Int        @id @default(autoincrement())
  openaiApiKey      String?
  openaiModel       String     @default("gpt-3.5-turbo")
  openaiMaxTokens   Int        @default(2000)
  openaiTemperature Float      @default(0.7)
  geminiApiKey      String?
  geminiModel       String     @default("gemini-pro")
  geminiMaxTokens   Int        @default(2000)
  geminiTemperature Float      @default(0.7)
  defaultProvider   AIProvider @default(OPENAI)
  isActive          Boolean    @default(true)
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt

  @@index([isActive])
  @@index([defaultProvider])
  @@map("ai_config")
}

model ConfiguracionIA {
  id               Int                @id @default(autoincrement())
  nombre           String             @unique
  proveedor        String             @default("GOOGLE_GEMINI")
  modelo           String             @default("veo3")
  apiKey           String?
  endpoint         String?
  parametros       String?
  promptPorDefecto String?
  limitesUso       String?
  activo           Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  generaciones     GeneracionImagen[]
  limites          LimiteUsoIA[]

  @@index([activo])
  @@index([proveedor])
  @@index([nombre])
  @@map("configuracion_ia")
}

model GeneracionImagen {
  id                Int             @id @default(autoincrement())
  noticiaId         Int?
  usuarioId         Int
  configuracionIAId Int
  prompt            String
  promptOriginal    String
  imagenUrl         String?
  estado            String          @default("PENDIENTE")
  metadatos         String?
  tiempoGeneracion  Int?
  tokensUsados      Int?
  costo             Float?
  error             String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  configuracion     ConfiguracionIA @relation(fields: [configuracionIAId], references: [id])
  noticia           Noticia?        @relation(fields: [noticiaId], references: [id])
  usuario           User            @relation(fields: [usuarioId], references: [id])

  @@index([noticiaId])
  @@index([usuarioId])
  @@index([estado])
  @@map("generacion_imagen")
}

model LimiteUsoIA {
  id                Int             @id @default(autoincrement())
  usuarioId         Int
  configuracionIAId Int
  periodo           String          @default("MENSUAL")
  limite            Int
  usado             Int             @default(0)
  fechaReset        DateTime
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  configuracion     ConfiguracionIA @relation(fields: [configuracionIAId], references: [id])
  usuario           User            @relation(fields: [usuarioId], references: [id])

  @@unique([usuarioId, configuracionIAId, periodo])
  @@map("limite_uso_ia")
}

model SystemConfig {
  id        Int      @id @default(autoincrement())
  settings  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy Int
  updatedBy Int
  creator   User     @relation("SystemConfigCreator", fields: [createdBy], references: [id])
  updater   User     @relation("SystemConfigUpdater", fields: [updatedBy], references: [id])

  @@map("system_configs")
}

model AuditLog {
  id         Int      @id @default(autoincrement())
  action     String
  entityType String
  entityId   String
  userId     Int
  details    Json?
  createdAt  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
  @@map("audit_logs")
}

model DiarioExterno {
  id                    Int                       @id @default(autoincrement())
  nombre                String
  urlBase               String                    @map("url_base")
  bearerToken           String                    @map("bearer_token")
  categoriaImagenId     Int                       @map("categoria_imagen_id")
  endpointImagen        String                    @default("/api/v1/image/insert") @map("endpoint_imagen")
  endpointCategoria     String                    @default("/api/v1/categories/fetch") @map("endpoint_categoria")
  endpointArticulo      String                    @default("/api/v1/article/insert") @map("endpoint_articulo")
  configuracion         String?
  descripcion           String?
  activo                Boolean                   @default(true)
  createdAt             DateTime                  @default(now()) @map("created_at")
  updatedAt             DateTime                  @updatedAt @map("updated_at")
  categoriaMapeos       CategoriaMapeo[]
  programaciones        ProgramacionPublicacion[]
  publicacionesExternas PublicacionExterna[]

  @@index([activo])
  @@index([createdAt])
  @@map("diarios_externos")
}

model CategoriaMapeo {
  id                 Int           @id @default(autoincrement())
  diarioExternoId    Int           @map("diario_externo_id")
  categoriaLocalId   Int           @map("categoria_local_id")
  categoriaExternaId Int           @map("categoria_externa_id")
  createdAt          DateTime      @default(now()) @map("created_at")
  categoriaLocal     Categoria     @relation(fields: [categoriaLocalId], references: [id], onDelete: Cascade)
  diarioExterno      DiarioExterno @relation(fields: [diarioExternoId], references: [id], onDelete: Cascade)

  @@unique([diarioExternoId, categoriaLocalId])
  @@index([diarioExternoId])
  @@index([categoriaLocalId])
  @@map("categoria_mapeos")
}

model PublicacionExterna {
  id                      Int                      @id @default(autoincrement())
  noticiaId               Int                      @map("noticia_id")
  diarioExternoId         Int                      @map("diario_externo_id")
  imagenExternaId         Int?                     @map("imagen_externa_id")
  articuloExternoId       Int?                     @map("articulo_externo_id")
  urlPublicacion          String?                  @map("url_publicacion")
  estado                  EstadoPublicacion        @default(PENDIENTE)
  errorMensaje            String?                  @map("error_mensaje")
  metadatos               String?
  createdAt               DateTime                 @default(now()) @map("created_at")
  updatedAt               DateTime                 @updatedAt @map("updated_at")
  diarioExterno           DiarioExterno            @relation(fields: [diarioExternoId], references: [id], onDelete: Cascade)
  noticia                 Noticia                  @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  socialMediaPublications SocialMediaPublication[]

  @@index([noticiaId])
  @@index([diarioExternoId])
  @@index([estado])
  @@index([createdAt])
  @@map("publicaciones_externas")
}

model ProgramacionPublicacion {
  id               Int                @id @default(autoincrement())
  noticiaId        Int                @map("noticia_id")
  versionId        Int?               @map("version_id")
  diarioExternoId  Int?               @map("diario_externo_id")
  tipo             TipoProgramacion   @default(ORIGINAL)
  fechaPublicacion DateTime           @map("fecha_publicacion")
  descripcion      String?
  estado           EstadoProgramacion @default(PENDIENTE)
  creadoPor        String             @map("creado_por")
  ejecutadoEn      DateTime?          @map("ejecutado_en")
  errorMensaje     String?            @map("error_mensaje")
  metadatos        Json?
  createdAt        DateTime           @default(now()) @map("created_at")
  updatedAt        DateTime           @updatedAt @map("updated_at")
  diarioExterno    DiarioExterno?     @relation(fields: [diarioExternoId], references: [id], onDelete: Cascade)
  noticia          Noticia            @relation(fields: [noticiaId], references: [id], onDelete: Cascade)
  version          VersionNoticia?    @relation(fields: [versionId], references: [id], onDelete: Cascade)

  @@index([noticiaId])
  @@index([versionId])
  @@index([diarioExternoId])
  @@index([fechaPublicacion])
  @@index([estado])
  @@index([createdAt])
  @@map("programacion_publicaciones")
}

model SocialMediaAccount {
  id           Int                      @id @default(autoincrement())
  platform     String
  accountName  String                   @map("account_name")
  profileId    String                   @map("profile_id")
  isActive     Boolean                  @default(true) @map("is_active")
  createdAt    DateTime                 @default(now()) @map("created_at")
  updatedAt    DateTime                 @updatedAt @map("updated_at")
  publications SocialMediaPublication[]

  @@map("social_media_accounts")
}

model SocialMediaPublication {
  id                   Int                @id @default(autoincrement())
  publicacionExternaId Int                @map("publicacion_externa_id")
  socialMediaAccountId Int                @map("social_media_account_id")
  platform             String
  caption              String
  status               String             @default("SCHEDULED")
  scheduledFor         DateTime?          @map("scheduled_for")
  publishedAt          DateTime?          @map("published_at")
  externalPostId       String?            @map("external_post_id")
  errorMessage         String?            @map("error_message")
  retryCount           Int                @default(0) @map("retry_count")
  createdAt            DateTime           @default(now()) @map("created_at")
  createdBy            String             @map("created_by")
  publicacionExterna   PublicacionExterna @relation(fields: [publicacionExternaId], references: [id])
  socialMediaAccount   SocialMediaAccount @relation(fields: [socialMediaAccountId], references: [id])

  @@index([status])
  @@index([scheduledFor])
  @@index([platform])
  @@index([publicacionExternaId])
  @@index([platform], map: "idx_social_media_publications_platform")
  @@index([status], map: "idx_social_media_publications_status")
  @@map("social_media_publications")
}

model DiarioSocialConfig {
  id                Int      @id @default(autoincrement())
  diarioId          Int      @unique
  uploadPostProfile String   @unique
  facebookEnabled   Boolean  @default(false)
  twitterEnabled    Boolean  @default(false)
  instagramEnabled  Boolean  @default(false)
  linkedinEnabled   Boolean  @default(false)
  facebookPageId    String?
  activo            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  diario            Diario   @relation(fields: [diarioId], references: [id])

  @@map("diario_social_configs")
}

model CacheEntry {
  id        Int      @id @default(autoincrement())
  key       String   @unique
  data      Json
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("cache_entries")
}

enum Role {
  ADMIN
  EDITOR
  USER
}

enum EstadoNoticia {
  BORRADOR
  EN_REVISION
  APROBADA
  PUBLICADA
  ARCHIVADA
}

enum OrigenNoticia {
  PANEL
  WEBHOOK
}

enum TipoNotificacion {
  WEBHOOK_NOTICIA
  SISTEMA
}

enum Estado {
  PENDIENTE
  EN_REVISION
  COMPLETADA
  RECHAZADA
}

enum Prioridad {
  BAJA
  MEDIA
  ALTA
  URGENTE
}

enum BoardRole {
  ADMIN
  MEMBER
}

enum AIProvider {
  OPENAI
  GEMINI

  @@map("ai_provider")
}

enum EstadoVersion {
  GENERADA
  APROBADA
  RECHAZADA
  EN_REVISION
}

enum EstadoPublicacionVersion {
  PENDIENTE
  PUBLICANDO
  PUBLICADA
  ERROR
}

enum EstadoPublicacion {
  PENDIENTE
  SUBIENDO_IMAGEN
  IMAGEN_SUBIDA
  PUBLICANDO_ARTICULO
  EXITOSO
  ERROR_IMAGEN
  ERROR_ARTICULO
  ERROR_CONEXION
}

enum TipoProgramacion {
  ORIGINAL
  VERSION
  EXTERNA
}

enum EstadoProgramacion {
  PENDIENTE
  EJECUTANDO
  COMPLETADA
  ERROR
  CANCELADA
}
