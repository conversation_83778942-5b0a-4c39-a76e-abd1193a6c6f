# Dockerfile para Backend API - Node.js/Express
FROM node:18-alpine AS base

# Instalar dependencias del sistema
RUN apk add --no-cache \
    libc6-compat \
    vips-dev \
    build-base \
    python3 \
    make \
    g++

WORKDIR /app

# Instalar dependencias
FROM base AS deps
COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci --include=dev; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Construir aplicación
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Variables de entorno para build
ENV NODE_ENV=production

# Variables de entorno mínimas para build
ENV DATABASE_URL="*****************************************************/placeholder"
ENV JWT_SECRET="build-time-secret-placeholder"

# Generar cliente Prisma
RUN npx prisma generate

# Construir aplicación TypeScript
RUN npm run build

# Imagen de producción
FROM base AS runner
WORKDIR /app

# Instalar dependencias de runtime
RUN apk add --no-cache vips

ENV NODE_ENV=production

# Crear usuario no-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 apiuser

# Copiar archivos necesarios
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/dist ./dist

# Crear directorio de uploads
RUN mkdir -p ./uploads/images && \
    chown -R apiuser:nodejs ./uploads

# Copiar node_modules con Prisma Client
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# Instalar solo dependencias de producción
RUN npm ci --only=production && npm cache clean --force

# Cambiar a usuario no-root
USER apiuser

# Exponer puerto
EXPOSE 3001

ENV PORT=3001
ENV HOSTNAME="0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando de inicio
CMD ["node", "dist/server.js"]
