{"name": "next-auth", "version": "4.24.11", "description": "Authentication for Next.js", "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth.git", "author": "<PERSON> <<EMAIL>>", "contributors": ["Balá<PERSON>s <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "main": "index.js", "module": "index.js", "types": "index.d.ts", "keywords": ["react", "nodejs", "o<PERSON>h", "jwt", "oauth2", "authentication", "nextjs", "csrf", "oidc", "<PERSON><PERSON><PERSON>"], "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./adapters": {"types": "./adapters.d.ts"}, "./jwt": {"types": "./jwt/index.d.ts", "default": "./jwt/index.js"}, "./react": {"types": "./react/index.d.ts", "default": "./react/index.js"}, "./next": {"types": "./next/index.d.ts", "default": "./next/index.js"}, "./middleware": {"types": "./middleware.d.ts", "default": "./middleware.js"}, "./client/_utils": {"types": "./client/_utils.d.ts", "default": "./client/_utils.js"}, "./providers/*": {"types": "./providers/*.d.ts", "default": "./providers/*.js"}}, "files": ["client", "core", "css", "jwt", "lib", "next", "providers", "react", "src", "utils", "*.d.ts*", "*.js"], "license": "ISC", "dependencies": {"@babel/runtime": "^7.20.13", "@panva/hkdf": "^1.0.2", "cookie": "^0.7.0", "jose": "^4.15.5", "oauth": "^0.9.15", "openid-client": "^5.4.0", "preact": "^10.6.3", "preact-render-to-string": "^5.1.19", "uuid": "^8.3.2"}, "peerDependencies": {"@auth/core": "0.34.2", "next": "^12.2.5 || ^13 || ^14 || ^15", "nodemailer": "^6.6.5", "react": "^17.0.2 || ^18 || ^19", "react-dom": "^17.0.2 || ^18 || ^19"}, "peerDependenciesMeta": {"@auth/core": {"optional": true}, "nodemailer": {"optional": true}}, "devDependencies": {"@auth/core": "0.34.2", "@babel/cli": "^7.17.10", "@babel/core": "^7.18.2", "@babel/plugin-proposal-optional-catch-binding": "^7.16.7", "@babel/plugin-transform-runtime": "^7.18.2", "@babel/preset-env": "^7.18.2", "@babel/preset-react": "^7.17.12", "@babel/preset-typescript": "^7.17.12", "@edge-runtime/jest-environment": "1.1.0-beta.35", "@next-auth/tsconfig": "0.0.0", "@swc/core": "^1.2.198", "@swc/jest": "^0.2.21", "@testing-library/dom": "^8.13.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/react-hooks": "^8.0.0", "@testing-library/user-event": "^14.2.0", "@types/jest": "^28.1.3", "@types/node": "^17.0.42", "@types/nodemailer": "^6.4.6", "@types/oauth": "^0.9.1", "@types/react": "18.0.37", "@types/react-dom": "^18.0.6", "autoprefixer": "^10.4.7", "babel-plugin-jsx-pragmatic": "^1.0.2", "babel-preset-preact": "^2.0.0", "concurrently": "^7", "cssnano": "^5.1.11", "jest": "^28.1.1", "jest-environment-jsdom": "^28.1.1", "jest-watch-typeahead": "^1.1.0", "msw": "^0.42.3", "next": "13.3.0", "postcss": "^8.4.14", "postcss-cli": "^9.1.0", "postcss-nested": "^5.0.6", "react": "^18", "react-dom": "^18", "whatwg-fetch": "^3.6.2"}, "scripts": {"build": "pnpm clean && pnpm build:js && pnpm build:css", "build:js": "pnpm clean && pnpm generate-providers && pnpm tsc --project tsconfig.json && babel --config-file ./config/babel.config.js src --out-dir . --extensions \".tsx,.ts,.js,.jsx\"", "clean": "rm -rf coverage client css utils providers core jwt react next lib ./*.js ./*.ts*", "build:css": "postcss --config config/postcss.config.js src/**/*.css --base src --dir . && node config/wrap-css.js", "dev": "pnpm clean && pnpm generate-providers && concurrently \"pnpm watch:css\" \"pnpm watch:ts\"", "watch:ts": "pnpm tsc --project tsconfig.dev.json", "watch:css": "postcss --config config/postcss.config.js --watch src/**/*.css --base src --dir .", "test": "jest --config ./config/jest.config.js", "generate-providers": "node ./config/generate-providers.js", "lint": "eslint src config tests"}}