"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Atlassian;
function Atlassian(options) {
  return {
    id: "atlassian",
    name: "Atlassian",
    type: "oauth",
    authorization: {
      url: "https://auth.atlassian.com/authorize",
      params: {
        audience: "api.atlassian.com",
        prompt: "consent"
      }
    },
    token: "https://auth.atlassian.com/oauth/token",
    userinfo: "https://api.atlassian.com/me",
    profile(profile) {
      return {
        id: profile.account_id,
        name: profile.name,
        email: profile.email,
        image: profile.picture
      };
    },
    style: {
      logo: "/atlassian.svg",
      bg: "#fff",
      text: "#0052cc"
    },
    options
  };
}